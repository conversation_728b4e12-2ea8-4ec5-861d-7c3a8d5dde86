/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

describe('chart-treemap: treemap component', () => {
	beforeEach(() =>
		cy.visit('/iframe.html?id=chart-treemap--treemap-story&viewMode=docs')
	);

	it('should render the component', () => {
		cy.get('[data-component="chart/treemap"]');
	});
});

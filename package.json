{"name": "ui-component-library", "version": "0.0.3", "license": "MIT", "scripts": {"start": "nx serve", "component": "nx run --buildableProjectDepsInPackageJsonType=dependencies", "test": "nx test", "test:all": "nx run-many --target=test --all --u --parallel --silent --runInBand --only-failed", "build:all": "nx run-many --target=build --all --buildableProjectDepsInPackageJsonType=dependencies --exclude=ifg,lib-storybook", "createLayoutTemplate": "nx workspace-generator layout-generator", "createComponent": "cross-env PUBLISH_REGISTRY=https://repo.jiffy.ai/repository nx workspace-generator component-generator", "storybook": "export NODE_OPTIONS=\"--openssl-legacy-provider --max-old-space-size=8192\" && nx run lib-storybook:storybook", "storybook-win": "set NODE_OPTIONS=--openssl-legacy-provider && nx run lib-storybook:storybook", "storybook:build": "nx run lib-storybook:build-storybook", "e2e": "TEST_NAME=e2e nx e2e", "e2e:all": "TEST_NAME=e2e nx run-many --target=e2e --all", "e2e:all:headless": "TEST_NAME=e2e nx run-many --target=e2e --all --headless", "login-registry": "npm login --registry=https://repo.jiffy.ai/repository/dreamer-group/", "login-atomic": "npm login --registry=https://repo.jiffy.ai/repository/atomic/ --scope=@atomic", "login-chart": "npm login --registry=https://repo.jiffy.ai/repository/chart/ --scope=@chart", "login-composite": "npm login --registry=https://repo.jiffy.ai/repository/composite/ --scope=@composite", "login-base": "npm login --registry=https://repo.jiffy.ai/repository/base/ --scope=@base", "login-ifg": "npm login --registry=https://repo.jiffy.ai/repository/ifg/ --scope=@ifg", "login-workflow": "npm login --registry=https://repo.jiffy.ai/repository/workflow/ --scope=@workflow", "build:ifg": "nx run-many --target=build --buildableProjectDepsInPackageJsonType=dependencies --projects=ifg-accountheader,ifg-accountopeningform,ifg-accountopeningformsummary,ifg-approve-modal,ifg-assign-modal,ifg-audittrail,ifg-basicinformationform,ifg-client-list,ifg-document-viewer,ifg-documentheader,ifg-documentlist,ifg-documentupload,ifg-footer,ifg-formdata,ifg-forward-modal,ifg-header,ifg-reject-modal,ifg-statusloader,ifg-success-modal,ifg-summary,ifg-summaryheader,ifg-supervisor-review-modal,ifg-validationpopup", "prepare": "husky install"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,html,css,scss,json}": ["npx prettier --write"], "**/*.{js,jsx,ts,tsx}": ["npx eslint --fix"]}, "workspaces": ["libs/*"], "dependencies": {"@ag-grid-community/client-side-row-model": "^32.2.1", "@ag-grid-community/core": "^32.2.1", "@ag-grid-community/csv-export": "^32.2.1", "@ag-grid-community/infinite-row-model": "^32.3.0", "@ag-grid-community/react": "^32.2.1", "@ag-grid-community/styles": "^32.2.1", "@ag-grid-enterprise/advanced-filter": "^32.3.3", "@ag-grid-enterprise/column-tool-panel": "^32.3.2", "@ag-grid-enterprise/excel-export": "^32.3.3", "@ag-grid-enterprise/filter-tool-panel": "^32.3.3", "@ag-grid-enterprise/menu": "^32.3.2", "@ag-grid-enterprise/range-selection": "^32.3.3", "@ag-grid-enterprise/server-side-row-model": "^32.3.0", "@ag-grid-enterprise/set-filter": "^32.3.2", "@ag-grid-enterprise/status-bar": "^32.3.3", "@amcharts/amcharts5": "^5.2.22", "@codemirror/autocomplete": "6.10.2", "@codemirror/commands": "6.3.0", "@codemirror/lang-html": "6.4.6", "@codemirror/lang-javascript": "6.0.1", "@codemirror/lang-json": "6.0.1", "@codemirror/language": "6.9.1", "@codemirror/lint": "6.4.2", "@codemirror/search": "6.5.4", "@codemirror/state": "6.4.0", "@codemirror/theme-one-dark": "6.1.2", "@codemirror/view": "6.21.3", "@draft-js-plugins/editor": "^4.1.0", "@draft-js-plugins/mention": "^5.1.0", "@helloviu/viewer": "3.0.0", "@hookform/resolvers": "^2.8.2", "@hookstate/core": "^3.0.13", "@lezer/common": "1.2.1", "@mapbox/search-js-react": "^1.0.0-beta.21", "@react-spring/web": "^9.5.2", "@storybook/addon-docs": "6.5.0", "@storybook/core-server": "6.4.22", "@szhsin/react-menu": "^4.0.0", "@tabler/icons": "^1.41.2", "@types/react-grid-layout": "^1.3.2", "@uiw/codemirror-theme-github": "4.21.20", "@uiw/codemirror-themes": "4.21.20", "@uiw/react-codemirror": "4.10.4", "ag-grid-enterprise": "^32.3.0", "ajv": "^8.17.1", "axios": "^0.24.0", "canvas": "^2.8.0", "classnames": "^2.3.1", "codemirror": "6.0.1", "core-js": "^3.6.5", "country-flag-icons": "^1.4.10", "cross-env": "^7.0.3", "date-fns": "^2.25.0", "devextreme": "21.2.3", "devextreme-react": "21.2.3", "draft-js": "^0.11.7", "draft-js-custom-styles": "^2.1.1", "draftjs-utils": "^0.10.2", "enzyme": "^3.11.0", "exceljs": "^4.3.0", "express": "4.18.2", "file-saver": "^2.0.5", "http-proxy-middleware": "^2.0.6", "icomoon-react": "^2.0.19", "innovate-utils-lib": "^1.5.5", "install": "^0.13.0", "joi": "^17.4.2", "jquery": "^3.6.4", "jspdf": "^2.4.0", "jspdf-autotable": "^3.5.23", "konva": "^8.2.3", "libphonenumber-js": "^1.11.11", "lodash": "^4.17.21", "moment": "^2.30.1", "npm": "^8.16.0", "object-hash": "^3.0.0", "pdfmake": "^0.2.15", "prop-types": "^15.7.2", "protobufjs": "^6.11.2", "re-resizable": "^6.9.1", "react": "18.2.0", "react-collapse-pane": "^2.0.1", "react-complex-tree": "1.1.10", "react-data-grid": "7.0.0-beta.43", "react-dnd": "^14.0.5", "react-dnd-html5-backend": "^15.1.3", "react-dom": "18.2.0", "react-draggable": "^4.4.4", "react-dropzone": "^12.0.4", "react-elastic-carousel": "0.11.5", "react-flow-renderer": "10.2.2", "react-google-autocomplete": "^2.7.3", "react-grid-layout": "^1.3.4", "react-hook-form": "^7.17.3", "react-image-gallery": "^1.3.0", "react-infinite-scroll-hook": "^4.1.1", "react-input-mask": "^2.0.4", "react-is": "18.0.0", "react-jss": "10.9.0", "react-konva": "^17.0.2-5", "react-laag": "^2.0.3", "react-loading-skeleton": "^3.5.0", "react-multi-carousel": "2.8.2", "react-number-format": "^4.8.0", "react-pdf": "^5.7.1", "react-player": "2.11.0", "react-query": "^3.34.12", "react-resizable": "^3.0.4", "react-resizable-panels": "^2.0.22", "react-rnd": "^10.3.7", "react-router-dom": "6.4.1", "react-speech-recognition": "^3.9.0", "react-table-column-resizer": "^1.2.4", "react-test-renderer": "18.2.0", "react-use-wizard": "^2.3.0", "react-virtuoso": "^2.2.2", "regenerator-runtime": "0.13.7", "tslib": "^2.0.0", "url-join-ts": "^1.0.5", "use-debounce": "^10.0.3", "uuid": "9.0.0", "web-tree-sitter": "0.20.7", "xlsx": "^0.18.5", "xss": "^1.0.15"}, "devDependencies": {"@babel/core": "7.16.0", "@babel/preset-flow": "7.14.5", "@babel/preset-typescript": "7.16.0", "@faker-js/faker": "^6.2.0", "@nrwl/cli": "14.8.0", "@nrwl/cypress": "14.8.0", "@nrwl/devkit": "14.8.0", "@nrwl/eslint-plugin-nx": "14.8.0", "@nrwl/express": "14.8.0", "@nrwl/jest": "14.8.0", "@nrwl/linter": "14.8.0", "@nrwl/node": "14.8.0", "@nrwl/react": "14.8.0", "@nrwl/storybook": "14.8.0", "@nrwl/web": "14.8.0", "@nrwl/workspace": "14.8.0", "@stomp/stompjs": "^6.1.2", "@storybook/addon-essentials": "6.5.16", "@storybook/builder-webpack5": "6.5.16", "@storybook/manager-webpack5": "6.5.16", "@storybook/react": "6.5.16", "@svgr/webpack": "5.4.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "13.4.0", "@types/express": "4.17.13", "@types/jest": "28.1.8", "@types/jquery": "^3.5.16", "@types/lodash": "^4.14.172", "@types/node": "18.7.18", "@types/react": "18.0.20", "@types/react-dom": "18.0.6", "@types/react-image-gallery": "^1.2.4", "@types/react-resizable": "^3.0.3", "@types/react-router-dom": "5.3.3", "@types/react-speech-recognition": "^3.6.1", "@types/testing-library__jest-dom": "^5.14.1", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "5.62.0", "@wojtekmaj/enzyme-adapter-react-17": "^0.6.3", "babel-jest": "28.1.3", "babel-loader": "8.1.0", "babel-plugin-polyfill-corejs2": "^0.2.2", "css-loader": "^6.5.1", "cypress": "^8.3.0", "cypress-xpath": "^1.6.2", "eslint": "^8.54.0", "eslint-config-prettier": "^8.1.0", "eslint-config-standard-with-typescript": "^40.0.0", "eslint-plugin-cypress": "^2.10.3", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "6.6.1", "eslint-plugin-n": "^16.3.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-storybook": "^0.5.6", "husky": "^8.0.0", "jest": "28.1.3", "jest-environment-jsdom": "28.1.1", "jest-mock-axios": "^4.6.1", "lint-staged": "^15.1.0", "nx": "14.8.0", "nx-cloud": "^16.5.2", "preset-react": "^1.0.0", "prettier": "^2.8.8", "react-laag": "^2.0.3", "resize-observer-polyfill": "^1.5.1", "rollup-plugin-node-polyfills": "^0.2.1", "sockjs-client": "^1.6.1", "style-loader": "^3.3.1", "ts-jest": "28.0.8", "ts-node": "10.9.1", "typescript": "^5.3.2", "url-loader": "^3.0.0"}}
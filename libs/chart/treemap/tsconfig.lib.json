{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../../dist/out-tsc", "types": ["node"], "rootDirs": ["../../libs"]}, "files": ["../../../node_modules/@nrwl/react/typings/cssmodule.d.ts", "../../../node_modules/@nrwl/react/typings/image.d.ts"], "exclude": ["jest.config.ts", "**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "**/*.spec.js", "**/*.test.js", "**/*.spec.jsx", "**/*.test.jsx", "**/*.stories.ts", "**/*.stories.js", "**/*.stories.jsx", "**/*.stories.tsx"], "include": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"]}
/* eslint-disable */
export default {
	displayName: 'chart-treemap',
	preset: '../../../jest.preset.js',
	transform: {
		'^.+\\.[tj]sx?$': ['babel-jest', { presets: ['@nrwl/react/babel'] }],
	},
	moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
	coverageDirectory: '../../../coverage/libs/chart/treemap',
	setupFilesAfterEnv: ['<rootDir>/src/jest.setup.ts'],
	moduleNameMapper: {
		'\\.(css|less|sass|scss)$': '<rootDir>/__mocks__/styleMock.js',
		'\\.(png|jpg|gif|ttf|eot|svg)$': '<rootDir>/__mocks__/fileMock.js',
	},
};

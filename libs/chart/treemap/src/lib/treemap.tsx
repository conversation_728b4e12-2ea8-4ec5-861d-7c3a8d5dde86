/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the Treemap component
import { FC } from 'react';
import { useTheme } from 'react-jss';
import { configurable, useEditContext } from '@base/config-provider';
import Box from '@atomic/box';
import { setMetaData } from '@base/utils-hooks';
import { ApexTheme } from '@base/theme';
import { defaultProps, metadata } from './metadata';
import { ITreemapProps } from './types';
import { styles } from './style';
import TreemapConfig from './treemapchart';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface TreemapProps extends ITreemapProps {}

const Treemap: FC<TreemapProps> = (props: TreemapProps) => {
	// Framework generated code, below line registers the component
	// NOT TO BE DELETED
	setMetaData('Treemap', metadata);

	// Framework generated code, below line provides the contextProvider instance
	const context = useEditContext();

	const theme: ApexTheme = useTheme();
	const componentStyles = styles(theme, props);
	const { id, containerStyles } = props;
	return (
		<Box
			data-component="chart/treemap"
			{...containerStyles}
			// backgroundColor="#efefef"
		>
			<div
				id={id}
				style={{ width: '100%', height: containerStyles?.height || '500px' }}
			></div>
			<TreemapConfig {...props} />
		</Box>
	);
};

Treemap.defaultProps = defaultProps;

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
export default configurable(Treemap, 'Treemap');

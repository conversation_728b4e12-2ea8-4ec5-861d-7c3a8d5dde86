/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { ITreemapProps } from './types';

export const defaultProps: ITreemapProps = {
	id: '1',
};

export const configurationProps = {
	//any config params
	containerId: '', //carries default configuration if any
};

export const editableProps = {
	component: 'Treemap',
	props: [
		{
			props: 'title',
			propLabel: 'Chart Title',
			help: 'Provide a text for the chart title',
			allowedValues: [
				{
					show: true,
					default: '',
					type: 'string',
				},
			],
		},
	],
};

export const metadata = {
	description: 'This is a help text',
	configurationProps,
	editableProps,
	defaultProps,
};

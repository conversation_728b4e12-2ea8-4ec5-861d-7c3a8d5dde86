import { cleanup, fireEvent, render, screen } from '@testing-library/react';
import { ThemeProvider } from 'react-jss';
import { ConfigProvider } from '@base/config-provider';
import { jiffy } from '@base/theme';
import Treemap, { TreemapProps } from './treemap';

describe('Treemap', () => {
	afterEach(cleanup);

	it('should render products', () => {
		const { getByText } = render(
			renderedComponent({
				id: 1,
			})
		);
		expect(getByText('Welcome to Treemap!', { exact: false })).toBeTruthy();
	});
});

const renderedComponent = (props: TreemapProps) => {
	return (
		<ThemeProvider theme={jiffy}>
			<Treemap {...props} />
		</ThemeProvider>
	);
};

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */
import { BoxProps } from '@atomic/box';

export interface ITreemapProps {
	id?: string;
	containerStyles?: BoxProps;
	data: any;
	processData?: (data: any) => any;
}

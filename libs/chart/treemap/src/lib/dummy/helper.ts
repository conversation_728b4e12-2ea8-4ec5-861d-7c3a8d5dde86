interface PlanNode {
	node_type?: string;
	plan_id?: string;
	startup_cost?: number;
	total_cost?: number;
	plan_rows?: number;
	plan_width?: number;
	actual_startup_time?: number;
	actual_total_time?: number;
	actual_rows?: number;
	actual_loops?: number;
	output?: string[];
	parent_relationship?: string;
	subplan_name?: string;
	plans?: PlanNode[];
	relation_name?: string;
	schema?: string;
	alias?: string;
	filter?: string;
	hash_cond?: string;
	recheck_cond?: string;
	index_name?: string;
	index_cond?: string;
	rows_removed_by_filter?: number;
	rows_removed_by_index_recheck?: number;
	exact_heap_blocks?: number;
	lossy_heap_blocks?: number;
	join_type?: string;
	inner_unique?: boolean;
	strategy?: string;
	partial_mode?: string;
}

interface Context {
	id: number;
	bufIdx: number;
	start: number;
	end: number;
	text: string;
	linkedBufIdx: number | null;
}

type Contexts = Context[];

interface Plan {
	id: number;
	parent: Plan | null;
	childDepth: number;
	name: string | null;
	totalTime: number | null;
	totalCost: number;
	selfTime: number | null;
	selfCost: number;
	selfTimePercent: number | null;
	selfCostPercent: number;
	subPlans: Plan[];
	contextId: number | null;
	raw: any;
}

interface D3TreemapNode {
	name: string;
	value: number; // totalCost for sizing
	children?: D3TreemapNode[];
	data: Omit<Plan, 'subPlans' | 'parent'>; // Store Plan data excluding hierarchy
}

function walkPlanNode(
	data: any,
	queryTotalTime: number | null,
	queryTotalCost: number,
	contexts: Contexts,
	planName: string | null = null
): Plan {
	const subPlans: Plan[] = (data.plans ?? [])
		.filter((child: any) => child != null)
		.map((child: any) =>
			walkPlanNode(
				child,
				queryTotalTime,
				queryTotalCost,
				contexts,
				child.subplan_name ||
					(child.parent_relationship === 'SubPlan' ? 'SubPlan' : null)
			)
		);

	const totalTime =
		data.actual_total_time !== undefined
			? data.actual_total_time * (data.actual_loops || 1)
			: null;
	const totalCost = data.total_cost || 0;

	const selfTime =
		totalTime &&
		Math.max(
			0,
			totalTime - subPlans.reduce((sum, plan) => sum + (plan.totalTime || 0), 0)
		);
	const selfCost = Math.max(
		0,
		totalCost - subPlans.reduce((sum, plan) => sum + plan.totalCost, 0)
	);

	const selfTimePercent =
		selfTime && queryTotalTime ? selfTime / queryTotalTime : null;
	const selfCostPercent = queryTotalCost ? selfCost / queryTotalCost : 0;

	let contextId = null;
	if (data.contexts) {
		const rawCtxs = data.contexts;
		const rawCtx = rawCtxs[rawCtxs.length - 1];
		const ctx = contexts.find(
			(ctx) =>
				ctx.bufIdx === rawCtx.buffer_idx &&
				ctx.start === rawCtx.start &&
				ctx.end === rawCtx.end
		);
		if (!ctx) {
			contextId = contexts.length;
			const linked = [...rawCtxs]
				.reverse()
				.find((lctx) => lctx.buffer_idx !== rawCtx.buffer_idx);
			contexts.push({
				id: contextId,
				bufIdx: rawCtx.buffer_idx,
				start: rawCtx.start,
				end: rawCtx.end,
				text: rawCtx.text,
				linkedBufIdx: linked?.buffer_idx ?? null,
			});
		} else {
			contextId = ctx.id;
		}
	}

	if (data.full_total_time != null) {
		subPlans.sort((a, b) => (b.totalTime || 0) - (a.totalTime || 0));
	}

	const plan: Plan = {
		id: data.plan_id || Math.random().toString(36).slice(2),
		parent: null,
		childDepth: subPlans.length
			? Math.max(...subPlans.map((subplan) => subplan.childDepth)) + 1
			: 0,
		name: planName || data.node_type || 'Unknown',
		totalTime,
		totalCost,
		selfTime,
		selfCost,
		selfTimePercent,
		selfCostPercent,
		contextId,
		subPlans,
		//raw: data,
		raw: { ...data, plans: undefined },
	};

	for (const subplan of subPlans) {
		subplan.parent = plan;
	}

	return plan;
}

function planToD3TreemapNode(plan: Plan): D3TreemapNode {
	return {
		name: plan.name || 'Unknown',
		value: plan.totalCost,
		children: plan.subPlans.length
			? plan.subPlans.map(planToD3TreemapNode)
			: undefined,
		data: {
			id: plan.id,
			childDepth: plan.childDepth,
			totalTime: plan.totalTime,
			selfTime: plan.selfTime,
			selfTimePercent: plan.selfTimePercent,
			selfCost: plan.selfCost,
			selfCostPercent: plan.selfCostPercent,
			contextId: plan.contextId,
			raw: plan.raw,
		},
	};
}

export function generateTreemapData(fullPlan: PlanNode): {
	plan: Plan;
	d3Treemap: D3TreemapNode;
} {
	const contexts: Contexts = [];
	const plan = walkPlanNode(
		fullPlan,
		fullPlan.actual_total_time || null,
		fullPlan.total_cost || 0,
		contexts,
		fullPlan.node_type || null
	);
	const d3Treemap = planToD3TreemapNode(plan);
	return { plan, d3Treemap };
}

function printStructure(
	plan: Plan,
	indent: number = 0,
	prefix: string = '➊',
	isLast: boolean = true
): string {
	let output = '';
	const indentStr = ' '.repeat(indent * 2);
	const connector = isLast ? '╰' : '├';
	const line = indent ? `${indentStr}${connector}──` : '';
	const time = (plan.totalTime || 0).toFixed(1).padStart(4);
	const cost = plan.totalCost.toFixed(2).padStart(6);
	const loops = (plan.raw.actual_loops || 1).toFixed(1).padStart(4);
	const rows = (plan.raw.actual_rows || 0).toFixed(1).padStart(4);
	const width = (plan.raw.plan_width || 0).toString().padStart(5);
	let planInfo = `${prefix} ${plan.name}`;

	if (plan.raw.relation_name)
		planInfo += ` relation_name=${plan.raw.relation_name}`;
	if (plan.raw.strategy) planInfo += ` strategy=${plan.raw.strategy}`;
	if (plan.raw.partial_mode)
		planInfo += `, partial_mode=${plan.raw.partial_mode}`;
	if (plan.raw.join_type) planInfo += ` join_type=${plan.raw.join_type}`;
	if (plan.raw.index_name) planInfo += ` index_name=${plan.raw.index_name}`;

	output += `${line} │ ${time} ${cost} ${loops} ${rows} ${width} │ ${planInfo}\n`;

	if (plan.subPlans) {
		const childCount = plan.subPlans.length;
		plan.subPlans.forEach((child, index) => {
			const isChildLast = index === childCount - 1;
			const childPrefix = String.fromCharCode(prefix.charCodeAt(0) + 1);
			const childIndent = indent + (indent ? 1 : 0);
			output += printStructure(child, childIndent, childPrefix, isChildLast);
		});
	}

	return output;
}

function printPlanHeader(): string {
	return '    │ Time   Cost Loops Rows Width │ Plan Info\n';
}

export const queryPlan = {
	arguments: {
		buffers: false,
		execute: true,
	},
	buffers: [
		'analyze select Branch {\n    branchCode,\n    branchID,\n    branchName,\n    branchURL\n};',
	],
	coarse_grained: {
		actual_loops: 1,
		actual_rows: 6,
		actual_startup_time: 0.022,
		actual_total_time: 0.033,
		children: [],
		contexts: [
			{
				buffer_idx: 0,
				end: 21,
				start: 15,
				text: 'Branch',
			},
		],
		plan_id: 'a7c525f1-405a-447d-85eb-1314bef3d207',
		plan_rows: 400,
		plan_width: 32,
		relations: ['Branch'],
		startup_cost: 0,
		total_cost: 30,
	},
	config_vals: {
		allow_user_specified_id: false,
		apply_access_policies: false,
		default_transaction_access_mode: 'ReadWrite',
		default_transaction_isolation: 'Serializable',
		query_cache_mode: 'Default',
		simple_scoping: null,
		store_migration_sdl: 'NeverStore',
		warn_old_scoping: null,
	},
	debug_info: {
		analysis_info: {
			alias_info: {
				'Branch~2': {
					contexts: [
						{
							buffer_idx: 0,
							end: 21,
							start: 15,
							text: 'Branch',
						},
					],
				},
			},
			buffers: [
				'analyze select Branch {\n    branchCode,\n    branchID,\n    branchName,\n    branchURL\n};',
			],
			shape_tree: {
				aliases: [],
				main_alias: 'Branch~2',
				pointers: {
					branchCode: {
						aliases: [],
						pointers: {},
					},
					branchID: {
						aliases: [],
						pointers: {},
					},
					branchName: {
						aliases: [],
						pointers: {},
					},
					branchURL: {
						aliases: [],
						pointers: {},
					},
					id: {
						aliases: [],
						pointers: {},
					},
				},
			},
		},
		full_plan: {
			actual_loops: 1,
			actual_rows: 6,
			actual_startup_time: 0.022,
			actual_total_time: 0.033,
			alias: 'Branch~2',
			async_capable: false,
			node_type: 'SeqScan',
			output: ['ROW((SubPlan 1), (SubPlan 2), (SubPlan 3), (SubPlan 4))'],
			parallel_aware: false,
			plan_id: 'a7c525f1-405a-447d-85eb-1314bef3d207',
			plan_rows: 400,
			plan_width: 32,
			plans: [
				{
					actual_loops: 6,
					actual_rows: 1,
					actual_startup_time: 0,
					actual_total_time: 0,
					async_capable: false,
					node_type: 'Result',
					output: ['"Branch~2"."7b78ea55-d94f-11ef-b113-155bb6e41bf7"'],
					parallel_aware: false,
					parent_relationship: 'SubPlan',
					plan_id: '8ba0195c-c5a3-4de6-af8e-5516e90ccebb',
					plan_rows: 1,
					plan_width: 8,
					plans: [],
					startup_cost: 0,
					subplan_name: 'SubPlan 1',
					total_cost: 2,
				},
				{
					actual_loops: 6,
					actual_rows: 1,
					actual_startup_time: 0,
					actual_total_time: 0,
					async_capable: false,
					node_type: 'Result',
					output: ['"Branch~2"."7b791564-d94f-11ef-bc87-4d5c45198dd2"'],
					parallel_aware: false,
					parent_relationship: 'SubPlan',
					plan_id: '64817754-a5ac-4827-8cd0-af54e7f3d218',
					plan_rows: 1,
					plan_width: 8,
					plans: [],
					startup_cost: 0,
					subplan_name: 'SubPlan 2',
					total_cost: 1,
				},
				{
					actual_loops: 6,
					actual_rows: 1,
					actual_startup_time: 0,
					actual_total_time: 0,
					async_capable: false,
					node_type: 'Result',
					output: ['"Branch~2"."7b793ffe-d94f-11ef-8062-c7d32ccdeb83"'],
					parallel_aware: false,
					parent_relationship: 'SubPlan',
					plan_id: 'a63b8ebf-0348-4f62-892c-a8a92d93dc2f',
					plan_rows: 1,
					plan_width: 32,
					plans: [],
					startup_cost: 0,
					subplan_name: 'SubPlan 3',
					total_cost: 1,
				},
				{
					actual_loops: 6,
					actual_rows: 1,
					actual_startup_time: 0,
					actual_total_time: 0,
					async_capable: false,
					node_type: 'Result',
					output: ['"Branch~2"."901747c2-d954-11ef-a795-4d1b535381c9"'],
					parallel_aware: false,
					parent_relationship: 'SubPlan',
					plan_id: 'dc465fd1-21e5-4c85-9596-06a929328230',
					plan_rows: 1,
					plan_width: 32,
					plans: [],
					startup_cost: 0,
					subplan_name: 'SubPlan 4',
					total_cost: 1,
				},
			],
			relation_name: 'Branch',
			schema: 'edgedbpub',
			startup_cost: 0,
			total_cost: 30,
		},
	},
	fine_grained: {
		contexts: [
			{
				buffer_idx: 0,
				end: 21,
				start: 15,
				text: 'Branch',
			},
		],
		pipeline: [
			{
				actual_loops: 1,
				actual_rows: 6,
				actual_startup_time: 0.022,
				actual_total_time: 0.033,
				plan_id: 'a7c525f1-405a-447d-85eb-1314bef3d207',
				plan_rows: 400,
				plan_type: 'SeqScan',
				plan_width: 32,
				properties: [
					{
						important: false,
						title: 'schema',
						type: 'text',
						value: 'edgedbpub',
					},
					{
						important: false,
						title: 'alias',
						type: 'text',
						value: 'Branch~2',
					},
					{
						important: true,
						title: 'relation_name',
						type: 'relation',
						value: 'Branch',
					},
				],
				startup_cost: 0,
				total_cost: 30,
			},
		],
		subplans: [],
	},
	globals_used: [],
	module_aliases: {
		null: 'default',
	},
	version: '6.7+07cb11c',
};

// const { plan, d3Treemap } = generateTreemapData(queryPlan.debug_info.full_plan);
// console.log(printPlanHeader());
// console.log(printStructure(plan));
// console.log('\nD3 Treemap Data:');
// console.log(JSON.stringify(d3Treemap, null, 2));

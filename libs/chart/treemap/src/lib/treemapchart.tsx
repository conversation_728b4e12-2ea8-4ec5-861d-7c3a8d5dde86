/* Imports */
import { useEffect } from 'react';
import * as am5hierarchy from '@amcharts/amcharts5/hierarchy';
import * as am5 from '@amcharts/amcharts5';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { ITreemapProps } from './types';
/* Chart code */
// Create root element
// https://www.amcharts.com/docs/v5/getting-started/#Root_element

const TreemapConfig = ({ id, data, processData }: ITreemapProps) => {
	function assignColors(node, colors, colorIndex = 0) {
		if (node.children && node.children.length > 0) {
			node.children.forEach((child) => assignColors(child, colors, colorIndex));
		}
	}
	const processChartData = (data: any, colors) => {
		let finalData = data;
		if (processData) {
			finalData = processData(data);
		} else {
			if (Array.isArray(data)) {
				finalData = [
					{
						name: 'Root',
						children: data,
					},
				];
			} else {
				finalData = [
					{
						name: data.name || 'Root',
						value: data.value || 0,
						children: data.children || [],
						...data,
					},
				];
			}
		}
		const colorIndex = 1;
		assignColors(finalData[0], colors, colorIndex);
		return finalData;
	};
	function formatDetails(details: Record<string, any>): string {
		if (!details || typeof details !== 'object') return '';

		const text = Object.entries(details)
			.map(([key, value], index) => {
				if (key === 'output') {
					return ''; // Skip raw data
				}
				const cleanValue = value === null || value === undefined ? '—' : value;
				return `${
					index ? '\n' : ''
				}[bold]${key}:[/][/][fontFamily:monospace] ${cleanValue}[/]`;
			})
			.join('');
		return text ? `\n\n[fontSize:10]${text}[/]` : '';
	}
	useEffect(() => {
		const root = am5.Root.new(id);

		const myTheme = am5.Theme.new(root);

		myTheme
			.rule('RoundedRectangle', ['hierarchy', 'node', 'shape', 'depth1'])
			.setAll({
				strokeWidth: 2,
			});

		myTheme
			.rule('RoundedRectangle', ['hierarchy', 'node', 'shape', 'depth2'])
			.setAll({
				fillOpacity: 0,
				strokeWidth: 1,
				strokeOpacity: 0.2,
			});

		// myTheme.rule('Label', ['node', 'depth1']).setAll({
		// 	forceHidden: true,
		// });

		myTheme.rule('Label', ['node', 'depth2']).setAll({
			fontSize: 10,
		});

		root.setThemes([am5themes_Animated.new(root), myTheme]);

		const zoomableContainer = root.container.children.push(
			am5.ZoomableContainer.new(root, {
				width: am5.p100,
				height: am5.p100,
				wheelable: false,
				pinchZoom: false,
			})
		);

		// Create series
		// https://www.amcharts.com/docs/v5/charts/hierarchy/#Adding
		const chart = am5hierarchy.Treemap.new(root, {
			// maskContent: false, //!important with zoomable containers
			// sort: 'descending',
			// upDepth: 1,
			singleBranchOnly: false, // only one branch open at a time
			// downDepth: 1, // only show first-level children
			initialDepth: Infinity, // start with first-level only
			valueField: 'value',
			categoryField: 'name',
			childDataField: 'children',

			// colorField: 'customColor',
			layoutAlgorithm: 'squarify',
		});
		// chart.nodes.template.setAll({
		// 	//   tooltipText: "{name}: {value}",
		// 	interactive: true,
		// 	setStateOnChildren: false, // ← keeps parent nodes visible
		// });

		const series = zoomableContainer.contents.children.push(chart);
		// let zoomTools = zoomableContainer.children.push(
		// 	am5.ZoomTools.new(root, {
		// 		target: zoomableContainer,
		// 	})
		// );
		// zoomTools.events.on('click', (ev) => {
		// 	series.set('selectedDataItem', series.dataItems[0]);
		// 	// series.set('initialDepth', Infinity);
		// 	// chart.set
		// });
		series.rectangles.template.setAll({
			// fillOpacity: 0.7,
			cornerRadiusTL: 4,
			cornerRadiusTR: 4,
			cornerRadiusBL: 4,
			cornerRadiusBR: 4,
		});
		series.nodes.template.events.on('click', function (ev) {
			// let depth = ev.target.dataItem.get('depth');
			// const dataContext = ev.target.dataItem.get("dataContext");
			const hasChildren = ev.target.dataItem.dataContext?.children?.length > 0;
			if (!hasChildren) {
				series.selectDataItem(ev.target.dataItem.get('parent'));
			}
		});
		// series.nodes.template.set("tooltipText", "{category}: [bold]{sum}[/]\n[fontSize:10]${JSON.stringify(data.raw)}");
		series.nodes.template.adapters.add('tooltipText', (text, target) => {
			const dataContext: any = target.dataItem?.dataContext;
			const details = formatDetails(dataContext?.data?.raw ?? {});
			return `[bold]{category}: {sum}[/]${details}`;
		});
		series.nodes.template.set(
			'tooltip',
			am5.Tooltip.new(root, {
				pointerOrientation: 'horizontal', // or "vertical"
				dy: 0, // vertical offset
				dx: 50, // horizontal offset
			})
		);
		series.labels.template.set('minScale', 0);
		series.get('colors').set('step', 1);
		const colors = am5.ColorSet.new(root, {
			step: 1, // how fast it changes
			reuse: false, // don't repeat colors
		});
		series.data.setAll(processChartData(data, colors));
		series.set('selectedDataItem', series.dataItems[0]);
	}, []);
	return <></>;
};
export default TreemapConfig;

import { Story, Meta } from '@storybook/react';
import Treemap, { TreemapProps } from './treemap';
import { defaultProps } from './metadata';
import { data, dummayData, processData } from './dummy/data';
import { generateTreemapData, queryPlan } from './dummy/helper';

export default {
	component: Treemap,
	title: 'Chart/Treemap',
	argTypes: {
		id: { type: 'number' },
	},
} as Meta;

const Template: Story<TreemapProps & HTMLButtonElement> = (args) => {
	const { d3Treemap } = generateTreemapData(queryPlan.debug_info.full_plan);
	return (
		<div style={{ gap: '20px', display: 'flex', flexDirection: 'column' }}>
			<Treemap {...args} id="test2" data={d3Treemap}></Treemap>
			{/* <Treemap
				{...args}
				id="test1"
				data={[dummayData[0], dummayData[1]]}
			></Treemap> */}
		</div>
	);
};

export const TreemapStory = Template.bind({});
TreemapStory.args = {
	containerStyles: { height: '600px', width: '100%' },
	...defaultProps,
};

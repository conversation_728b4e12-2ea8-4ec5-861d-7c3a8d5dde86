/*
 * Copyright (c) 2020.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import React from 'react';
import { ThemeProvider } from 'react-jss';
import { ConfigProvider } from '@base/config-provider';
import { jiffy, alerus, kountable } from '@base/theme';

const getTheme = (themename) => {
	switch (themename) {
		case 'Green':
			return jiffy;
		case 'Blue':
			return alerus;
		case 'pacificBlue':
			return kountable;
		default:
			return jiffy;
	}
};

const withThemeProvider = (story, context) => {
	const theme = getTheme(context.globals.theme);
	return (
		<ThemeProvider theme={theme}>
			<ConfigProvider mode={false} open={() => alert('hi')}>
				<React.Fragment>
					<link
						href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap"
						rel="stylesheet"
					></link>
					<link
						href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,400&display=swap"
						rel="stylesheet"
					></link>
					<link
						href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap"
						rel="stylesheet"
					></link>
					{story()}
				</React.Fragment>
			</ConfigProvider>
		</ThemeProvider>
	);
};
export const decorators = [withThemeProvider];

export const globalTypes = {
	theme: {
		name: 'Theme',
		description: 'Global theme for components',
		defaultValue: 'Green',
		toolbar: {
			//icon: 'theme',
			// array of plain string values or MenuItem shape (see below)
			items: ['Green', 'Blue', 'pacificBlue'],
		},
	},
};

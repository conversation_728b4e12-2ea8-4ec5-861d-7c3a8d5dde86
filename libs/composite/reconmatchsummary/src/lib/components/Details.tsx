import { FC, useEffect, useState } from 'react';
import { useTheme } from 'react-jss';
import { v4 as generateUUID } from 'uuid';
import { isEqual } from 'lodash';
import { ApexTheme } from '@base/theme';
import Box from '@atomic/box';
import Text, { TextTypes, TextSizes } from '@atomic/text';
import Button, {
	ButtonTypes,
	HTMLButtonTypes,
	ButtonRadius,
	ButtonSizes,
	IconPosition,
} from '@atomic/button';
import { IconCodes } from '@atomic/icon';
import Dropdown from '@atomic/dropdown';
import Datepicker from '@composite/datepicker';
import DataTable from '@composite/datatable';
import Form, { IFormMode } from '@composite/form';

import Banner from './Banner';
import TableCell from './TableCell';
import TargetDetailsModal from './TargetDetailsModal';
import { DetailsProps } from './types';
import { PAGE_HEADERS, VIEW_TYPE } from '../constants';
import {
	getColAndRow,
	conditionalFormattingCriteriaTypes,
	lowerCaseFirstLetter,
	titleCaseString,
} from './helper';
import {
	loadBODataByNameAndSchema,
	performMatch,
} from '../components/api/helper';
import { useDetailsStyles } from './styles';

const colExclusionList = [
	'id',
	'bulkLoadRecIdJfyApx',
	'bulkLoadRunIdJfyApx',
	'matchStatus',
	'matchRuleName',
	'matchRemarks',
	'matchType',
	'matchedOn',
	'target',
];

const getBOMappingText = (reconData, matchData) => {
	const sourceName =
		reconData?.dataSources?.find(
			(item: Record<string, any>) => item?.name === matchData.sourceType
		)?.label || matchData.sourceType;

	const targetName =
		reconData?.dataSources?.find(
			(item: Record<string, any>) => item?.name === matchData.targetType
		)?.label || matchData.targetType;

	return `${sourceName} -> ${targetName}`;
};

const Details: FC<DetailsProps> = (props) => {
	const {
		setView,
		reconDetail,
		selectedMatchRecord,
		reconId,
		getSelectedMatchRecordData,
		matchSummaryDataSet,
		setSelectedMatchRecord,
	} = props;
	const theme: ApexTheme = useTheme();
	const classes = useDetailsStyles();

	const [rowData, setRowData] = useState([]);
	const [tableFilter, setTableFilter] = useState([]);
	const [headerFilter, setHeaderFilter] = useState([]);
	const [globalFilter, setGlobalFilter] = useState([]);
	const [pageData, setPageData] = useState(null);
	const [totalCount, setTotalCount] = useState(0);
	const [columnData, setColumnData] = useState([]);
	const [selectedRecord, setSelectedRecord] = useState(null);
	const [showMapDetails, setShowMapDetails] = useState(false);
	const [showUnmatchModal, setShowUnmatchModal] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [selectedUmmatchRcd, setSelectedUnmatchRcd] = useState(null);
	const ruleSymbolMap = {
		ilike: 'constains',
		eq: '==',
		neq: '!=',
		gt: '>',
		lt: '<',
		gte: '>=',
		lte: '<=',
		empty: 'empty',
	};
	const matchFilterTypes = ['matchStatus', 'matchType', 'matchedOn'];

	const processFilterString = (str = '') =>
		str?.split('&')?.map((item: string) => {
			const keyValuesPair = item?.split(':');
			const key = keyValuesPair?.[0];
			const values = keyValuesPair?.[1]?.split('=');
			if (values?.[0] === 'ilike') {
				return `strContains(${key},  '${values?.[1]}' )`;
			} else if (values?.[0] === 'empty') {
				return `${key} == null`;
			} else {
				return `${
					matchFilterTypes.includes(key)
						? key
						: `${selectedMatchRecord?.sourceType}.${key}`
				} ${ruleSymbolMap?.[values?.[0]]} '${values?.[1]}'`;
			}
		});

	const getData = async (pageSpec: Record<string, any>) => {
		try {
			setIsLoading(true);
			const sourceBoName = selectedMatchRecord?.sourceType || '';
			const formattedBOName =
				sourceBoName.charAt(0).toUpperCase() +
				sourceBoName.toLowerCase().slice(1);
			const pipelineBOName = `${lowerCaseFirstLetter(
				selectedMatchRecord?.reconName
			)}${titleCaseString(selectedMatchRecord?.targetType)}`;
			const targetBOName = lowerCaseFirstLetter(sourceBoName);
			const schema = {
				select: {
					'*': true,
					[targetBOName]: {
						select: {
							'*': true,
						},
					},
				},
			};
			if (globalFilter?.length) {
				schema['filter'] = globalFilter;
			}

			const BOFetchResults: Record<string, any> =
				(await loadBODataByNameAndSchema(
					pipelineBOName,
					schema,
					false,
					pageSpec
				)) || {
					data: [],
					total: 0,
				};

			const nestedData = BOFetchResults.data.flatMap((record: any) => {
				const rawNested = record[sourceBoName];

				const innerRecords = Array.isArray(rawNested)
					? rawNested
					: rawNested
					? [rawNested]
					: [];

				return innerRecords.map((innerRecord: any) => ({
					...innerRecord,
					matchStatus: record.matchStatus || null,
					matchRemarks: record.matchRemarks || null,
					matchRuleName: record.matchRuleName || null,
					matchType: record.matchType || null,
					matchedOn: record.matchedOn || null,
					target: record.target || null,
					id: record.id || null,
				}));
			});

			const results = BOFetchResults?.total
				? getColAndRow(
						colExclusionList,
						// BOFetchResults?.data,
						nestedData,
						targetBOName,
						reconDetail,
						columnData
				  )
				: { colData: [], rowData: [] };

			setRowData(results.rowData);
			setTotalCount(BOFetchResults?.total);
		} catch (error) {
			console.error(error);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		const targetBOName = selectedMatchRecord?.sourceType;

		const cols = [
			{
				id: generateUUID().toString(),
				key: 'id',
				name: 'ID',
				width: 100,
			},
			{
				id: generateUUID().toString(),
				key: 'matchStatus',
				name: 'Match status',
				width: 140,
				formattingRules: {
					rules: [
						{
							criteria: conditionalFormattingCriteriaTypes.StringIsEmpty,
							type: '',
							value: '',
							style: {
								primaryColor: theme.colors.monochrome.label,
								fontColor: null,
							},
						},
						{
							criteria: conditionalFormattingCriteriaTypes.StringIsExactly,
							type: 'Tag',
							value: 'Unmatched',
							style: {
								primaryColor: theme.colors.red[300],
								fontColor: theme.colors.monochrome.label,
							},
						},
						{
							criteria: conditionalFormattingCriteriaTypes.StringIsExactly,
							type: 'Tag',
							value: 'Matched',
							style: {
								primaryColor: theme.colors.green[300],
								fontColor: theme.colors.monochrome.label,
							},
						},
					],
				},
			},
			{
				id: generateUUID().toString(),
				key: 'matchRuleName',
				name: 'Match rule name',
				width: 160,
			},
			{
				id: generateUUID().toString(),
				key: 'matchRemarks',
				name: 'Match remark',
				width: 120,
			},
			{
				id: generateUUID().toString(),
				key: 'matchType',
				name: 'Match type',
				width: 110,
				formattingRules: {
					rules: [
						{
							criteria: conditionalFormattingCriteriaTypes.StringIsEmpty,
							type: '',
							value: '',
							style: {
								primaryColor: theme.colors.monochrome.label,
								fontColor: null,
							},
						},
						{
							criteria: conditionalFormattingCriteriaTypes.StringIsExactly,
							type: 'Tag',
							value: 'Partial',
							style: {
								primaryColor: theme.colors.purple[200],
								fontColor: theme.colors.monochrome.white,
							},
						},
						{
							criteria: conditionalFormattingCriteriaTypes.StringIsExactly,
							type: 'Tag',
							value: 'Automatic',
							style: {
								primaryColor: '#cc9b3c',
								fontColor: theme.colors.monochrome.white,
							},
						},
						{
							criteria: conditionalFormattingCriteriaTypes.StringIsExactly,
							type: 'Tag',
							value: 'Suggested',
							style: {
								primaryColor: theme.colors.tertiary[600],
								fontColor: theme.colors.monochrome.white,
							},
						},
						{
							criteria: conditionalFormattingCriteriaTypes.StringIsExactly,
							type: 'Tag',
							value: 'Manual',
							style: {
								primaryColor: theme.colors.lavender[600],
								fontColor: theme.colors.monochrome.white,
							},
						},
					],
				},
			},
			{
				id: generateUUID().toString(),
				key: 'matchedOn',
				name: 'Matched on',
				width: 195,
				dataType: 'DateTime',
			},
			{
				id: generateUUID().toString(),
				key: 'target',
				name: 'Target',
				maxWidth: 150,
				renderCell: (props: any) => {
					return (
						<TableCell
							entityId={props?.id}
							classes={classes}
							rowData={props.rowData}
							column={props.column}
							label="View records"
							emptyRowValue=""
							fallbackComponent={props.fallbackComponent}
							onCellClick={(record) => {
								setSelectedRecord(record);
								setShowMapDetails(true);
							}}
						/>
					);
				},
			},
			{
				id: generateUUID().toString(),
				key: 'matchStatus',
				name: 'Action',
				width: 90,
				renderCell: (props: any) => {
					const isMatched = props.rowData.matchStatus === 'Matched';
					return (
						<TableCell
							entityId={props?.id}
							classes={classes}
							rowData={props.rowData}
							column={props.column}
							label={isMatched ? 'Un Match' : ''}
							emptyRowValue={isMatched ? 'unmatched' : ''}
							fallbackComponent={props.fallbackComponent}
							onCellClick={(record) => {
								if (isMatched) {
									setSelectedUnmatchRcd(record.id);
									setSelectedRecord(record);
									setShowUnmatchModal(true);
								}
							}}
						/>
					);
				},
			},
		];

		const fieldsFromReconDef =
			reconDetail?.dataSources
				?.find((item: Record<string, any>) => item?.name === targetBOName)
				?.fields?.filter(
					(innerItem: Record<string, any>) =>
						!colExclusionList.includes(innerItem?.name)
				)
				?.map((item: Record<string, any>) => ({
					id: item?.id,
					key: item?.name,
					name: item?.label,
					width: (item?.label?.length || 10) * 11,
				})) || [];
		// Only set column data when fieldsFromReconDef is derived
		if (fieldsFromReconDef?.length > 0) {
			const combinedArrays = [...cols, ...fieldsFromReconDef];
			setColumnData(combinedArrays);
		}
	}, [reconDetail]);

	useEffect(() => {
		const combinedFilter = [...tableFilter, ...headerFilter];
		setGlobalFilter(combinedFilter);
	}, [tableFilter, headerFilter]);

	useEffect(() => {
		if (tableFilter) {
			getData(pageData);
		}
	}, [globalFilter]);

	useEffect(() => {
		if (pageData) {
			getData(pageData);
		}
	}, [pageData]);

	useEffect(() => {
		const updatedBannerdata = matchSummaryDataSet?.find(
			(item) => item?.id === selectedMatchRecord?.id
		);
		setSelectedMatchRecord(updatedBannerdata);
	}, [matchSummaryDataSet]);

	const onGlobalFilterSubmit = (items) => {
		const filter = [];
		if (items?.fromDate) {
			filter.push(`matchedOn >= '${items.fromDate}T00:00:00.000000+00:00'`);
		}
		if (items?.toDate) {
			filter.push(`matchedOn <= '${items.toDate}T23:59:59.000000+00:00'`);
		}
		if (items?.filterMatch) {
			filter.push(`matchStatus == '${items.filterMatch}'`);
		}
		if (items?.matchType) {
			filter.push(`matchType == '${items.matchType}'`);
		}
		setHeaderFilter(filter);
	};

	return (
		<Box className={classes.detailsContainer}>
			<Box className={classes.detailsHeader}>
				<Box className={classes.headerLeftContent}>
					<Button
						buttonType={ButtonTypes.Text}
						radius={ButtonRadius.SemiRounded}
						size={ButtonSizes.Small}
						title=""
						iconPosition={IconPosition.Left}
						iconProps={{
							icon: IconCodes.icon_Tb_chevron_left,
						}}
						onClick={() => {
							setView(VIEW_TYPE.SUMMARY);
						}}
					/>
					<Box flexDirection="column">
						<Text
							type={TextTypes.Heading}
							size={TextSizes.XSmall}
							fontWeight={theme.fontWeights.semiBold}
						>
							{PAGE_HEADERS['DETAILS']}{' '}
							{getBOMappingText(reconDetail, selectedMatchRecord)}
						</Text>
						<Text
							type={TextTypes.Body}
							size={TextSizes.Small}
							alignContent="center"
						>
							Match Ruleset: {selectedMatchRecord?.reconName}
						</Text>
					</Box>
				</Box>
				<Box className={classes.headerRightContent}>
					<Form
						onSubmit={onGlobalFilterSubmit}
						validationTrigger={IFormMode.onSubmit}
						formStyles={{ width: '100%' }}
					>
						<Box className={classes.headerRightFormContent}>
							<Dropdown
								name="filterMatch"
								placeholder="Filter match"
								data={[
									{ id: 1, label: 'All', value: '' },
									{ id: 1, label: 'Matched', value: 'Matched' },
									{ id: 1, label: 'Unmatched', value: 'Unmatched' },
								]}
								withForm
								foregroundColor={theme.colors.monochrome.offWhite}
							/>
							<Dropdown
								name="matchType"
								placeholder="Match type"
								data={[
									{ label: 'All', value: '' },
									{ label: 'Automatic', value: 'Automatic' },
									{ label: 'Manual', value: 'Manual' },
									{ label: 'Suggested', value: 'Suggested' },
									{ label: 'Partial', value: 'Partial' },
								]}
								withForm
								foregroundColor={theme.colors.monochrome.offWhite}
							/>
							<Button
								buttonType={ButtonTypes.Primary}
								type={HTMLButtonTypes.Submit}
								title="Apply Filters"
								width="130px"
							/>
						</Box>
					</Form>
				</Box>
			</Box>
			<Box
				display="flex"
				width="100%"
				flexDirection="column"
				backgroundColor={theme.colors.monochrome.white}
				padding="10px 5px"
			>
				<Banner matchSummaryData={selectedMatchRecord} />
				{columnData?.length > 0 && (
					<DataTable
						id="summary-details"
						rowData={rowData}
						columnData={columnData}
						hiddenColumns={['id']}
						showLoader={isLoading}
						enableInfinteScrolling={false}
						enableFilter={true}
						allowServersideFilter={true}
						handleGlobalFilter={(value) => {
							if (value !== undefined && value !== '') {
								const updatedFilters = processFilterString(value);
								if (!isEqual(updatedFilters, tableFilter)) {
									setTableFilter(updatedFilters);
								}
							} else if (value === '' && tableFilter?.length > 0) {
								setTableFilter([]);
							}
						}}
						allowPagination={true}
						totalRecordsCount={totalCount}
						onPaginationChange={setPageData}
						isSortingEnabled={false}
						enableGlobalSearch={false}
						enableGrouping={true}
						enableColumnSelection={true}
						isExportEnabled={true}
						showMoreOption={false}
						showColumnSelect={false}
						dataTableStyles={{
							width: '100%',
							minHeight: '467px',
							height: '100%',
						}}
						datatableContarinerBoxStyles={{
							gap: '16px',
							maxHeight: '573px',
							backgroundColor: theme.colors.monochrome.bg,
						}}
						enableActions={false}
					/>
				)}
			</Box>
			{(showMapDetails || showUnmatchModal) && selectedRecord && (
				<TargetDetailsModal
					onClose={() => {
						setSelectedRecord(null);
						setShowMapDetails(false);
						setShowUnmatchModal(false);
					}}
					isOpen={showMapDetails || showUnmatchModal}
					showUnmatchModal={showUnmatchModal}
					onUnmatch={() => {
						performMatch(
							selectedMatchRecord.reconId,
							{
								type: selectedMatchRecord.sourceType,
								Ids: [selectedUmmatchRcd],
							},
							{ matchFor: 'Unmatch' },
							() => {
								setShowUnmatchModal(false);
								setShowMapDetails(false);
								setIsLoading(true);
								getData(pageData);
								getSelectedMatchRecordData(reconId);
							}
						);
					}}
					title="Matched records"
					recordDetails={{
						selectedMatchRecord: selectedMatchRecord,
						selectedRecord: selectedRecord,
					}}
					reconDetails={reconDetail}
					bodyContainerProps={{
						width: '100%',
						overflowX: 'hidden',
						padding: '15px',
					}}
					{...(showUnmatchModal
						? {
								windowContainerProps: {
									width: '30%',
									height: '30%',
								},
						  }
						: {})}
				/>
			)}
		</Box>
	);
};

export default Details;

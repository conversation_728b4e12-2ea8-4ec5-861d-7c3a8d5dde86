import React, { FC, useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from 'react-jss';

import { ApexTheme } from '@base/theme';
import { v4 as generateUUID } from 'uuid';
import ModalWindows, {
	DockType,
	IModalFooterProps,
	IModalHeaderProps,
	ModalBody,
} from '@composite/modalwindows';
import Box from '@atomic/box';
import { ButtonTypes } from '@atomic/button';

import DataTable from '@composite/datatable';

import { generateTargetDetailsModalStyles } from './styles';
import { TargetDetailsModalProps } from './types';
import { loadBODataByNameAndSchema } from '../components/api/helper';
import { lowerCaseFirstLetter } from './helper';
import Icon, { IconCodes } from '@atomic/icon';
import Accordion, {
	AccordionItem,
	AccordionItemBody,
	AccordionItemHeader,
} from '@atomic/accordion';

const colExclusionList = ['bulkLoadRecIdJfyApx', 'bulkLoadRunIdJfyApx'];

export const footerProps = (onClose: any): IModalFooterProps => ({
	// className: classes.popupFooter,
	// footerContainerProps: footerContainerProps,
	footerButtonBarProps: {
		buttonBarProps: { padding: '16px' },
		buttonBarRightSectionProps: { width: '50%' },
		buttonbarRightSectionOverflowProps: { gapBetweenItems: '16px' },
	},
	rightButtonArray: [
		{
			// ...footerButtonProps,
			buttonType: ButtonTypes.Subtle,
			backgroundColor: 'transparent',
			title: 'Cancel',
			onClick: onClose,
		},
		{
			// ...footerButtonProps,
			buttonType: ButtonTypes.Subtle,
			backgroundColor: 'transparent',
			title: 'Unmatch',
			onClick: onClose,
		},
	],
});

const TargetDetailsModal: FC<TargetDetailsModalProps> = React.memo((props) => {
	const theme: ApexTheme = useTheme();
	const {
		onClose,
		showUnmatchModal,
		isOpen,
		onUnmatch,
		recordDetails,
		reconDetails,
		title,
		backdropStyles,
		bodyContainerProps,
		windowContainerProps,
	} = props;
	const componentStyles = useMemo(
		() => generateTargetDetailsModalStyles(theme),
		[theme]
	);

	const [rowData, setRowData] = useState([]);
	const [colData, setColData] = useState<any>([]);
	const [lhsColData, setLhsColData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const getData = useCallback(async () => {
		try {
			setIsLoading(true);

			const sourceBoName = recordDetails?.selectedMatchRecord?.sourceType || '';
			const formattedBOName =
				sourceBoName.charAt(0).toUpperCase() +
				sourceBoName.toLowerCase().slice(1);
			const pipelineBOName = `${lowerCaseFirstLetter(
				recordDetails?.selectedMatchRecord?.reconName
			)}${formattedBOName}`;
			const targetBOName = recordDetails?.selectedMatchRecord?.targetType;

			const targetIds = recordDetails?.selectedRecord?.target;

			const schema = {
				select: {
					'*': true,
					// [targetBOName]: {
					//     select: {
					//         '*': true,
					//     },
					// },
				},
				filter: `includes([${targetIds
					.split(',')
					.map((id: string) => `"${id}"`)}], id)`,
			};

			const BOFetchResults = (await loadBODataByNameAndSchema(
				targetBOName,
				schema,
				true,
				{}
			)) || {
				data: [],
			};
			setRowData(BOFetchResults?.data || []);
		} catch (error) {
			console.error('Error loading data:', error);
			setRowData([]);
		}
	}, [reconDetails]);

	useEffect(() => {
		const fieldsFromReconDef =
			reconDetails?.dataSources
				?.find(
					(item: Record<string, any>) =>
						item?.name === recordDetails?.selectedMatchRecord?.sourceType
				)
				?.fields?.filter(
					(innerItem: Record<string, any>) =>
						!colExclusionList.includes(innerItem?.name)
				)
				.map((item: Record<string, any>) => ({
					id: item?.id,
					key: item?.name,
					name: item?.label,
					width: (item?.label?.length || 10) * 11,
				})) || [];
		// Only set column data when fieldsFromReconDef is derived
		if (fieldsFromReconDef.length > 0) {
			setColData(fieldsFromReconDef);
		}

		const leftFieldsFromReconDef =
			reconDetails?.dataSources
				?.find(
					(item: Record<string, any>) =>
						item?.name === recordDetails?.selectedMatchRecord?.targetType
				)
				?.fields?.filter(
					(innerItem: Record<string, any>) =>
						!colExclusionList.includes(innerItem?.name)
				)
				.map((item: Record<string, any>) => ({
					id: item?.id,
					key: item?.name,
					name: item?.label,
					width: (item?.label?.length || 10) * 11,
				})) || [];
		if (leftFieldsFromReconDef.length > 0) {
			setLhsColData(leftFieldsFromReconDef);
		}
		getData();
	}, []);

	const getFooterProps = (
		theme: ApexTheme,
		onClose: () => void,
		onUnmatch: () => void
	): IModalFooterProps => ({
		footerButtonBarProps: {
			buttonBarProps: { padding: '16px' },
			buttonBarRightSectionProps: { width: '50%' },
			buttonbarRightSectionOverflowProps: { gapBetweenItems: '16px' },
		},
		rightButtonArray: [
			{
				// ...footerButtonProps,
				buttonType: ButtonTypes.Subtle,
				backgroundColor: 'transparent',
				title: 'Cancel',
				onClick: onClose,
			},
			{
				// ...footerButtonPrps,
				buttonType: ButtonTypes.Primary,
				title: 'Ok',
				onClick: onUnmatch,
			},
		],
	});

	const getModalHeaderProps = (
		theme: ApexTheme,
		handleModalClose: () => void
	): IModalHeaderProps => ({
		title: title || 'Detail view',
		tooltipProps: {
			containerStyles: {
				display: 'none',
			},
		},
		headerButtonBarProps: {
			buttonBarBackgroundColor: theme.colors.monochrome.offWhite,
		},
		boxProps: {
			borderBottom: `1px solid ${theme.colors.monochrome.input}`,
			padding: '8px 8px 8px 16px',
		},
		buttonArray: [
			{
				id: 'assign-modal-close',
				iconProps: {
					icon: IconCodes.icon_Bd_Close_X,
					color: theme.colors.monochrome.label,
					fontSize: 22,
					onClick: handleModalClose,
				},
				hoverStyles: {
					backgroundColor: 'none',
				},
				activeStyles: { backgroundColor: 'none' },
				buttonStyles: {
					backgroundColor: theme.colors.monochrome.offWhite,
					paddingRight: 0,
				},
			},
		],
	});

	return (
		<ModalWindows
			dockType={DockType.Center}
			showModal={isOpen}
			isButtonBarEnabled={true}
			isTitleBarEnabled={true}
			windowContainerProps={{
				...componentStyles.windowContainerProps,
				...windowContainerProps,
			}}
			showBackgroundMask={true}
			backdropStyles={{
				backgroundColor: `${theme.colors.monochrome.black}50`,
				zIndex: 110,
				...(backdropStyles || {}),
			}}
			headerProps={getModalHeaderProps(theme, onClose)}
			footerProps={
				showUnmatchModal && getFooterProps(theme, onClose, onUnmatch)
			}
		>
			<ModalBody
				bodyContainerProps={{
					...componentStyles.bodyContainerProps,
					...bodyContainerProps,
				}}
			>
				<Box
					{...{
						display: 'flex',
						width: '100%',
						height: '100%',
					}}
				>
					{showUnmatchModal ? (
						<div>Are you sure you want to proceed?</div>
					) : (
						<div style={{ width: '100%' }}>
							<Accordion
								allowMultiple={true}
								id="1293298"
								key="78328732"
								{...{
									iconPlacement: 'left',
									margin: '0px 0px 0px 0px',
									width: '100%',
								}}
							>
								{rowData.length > 0 ? (
									<AccordionItem id="lhs-item-id" key="lhs-item-key">
										<AccordionItemHeader
											id="201"
											key="LHS-Accordian-header"
											itemHeaderStyles={{ color: 'green' }}
										>
											LHS
										</AccordionItemHeader>
										<AccordionItemBody
											id="101"
											key="78"
											containerStyles={{
												width: '100%',
												padding: '15px',
											}}
										>
											<div style={{ width: '100%' }}>
												<DataTable
													id="2782732"
													key="8938232"
													rowData={rowData}
													showLoader={isLoading}
													columnData={lhsColData}
													hiddenColumns={[
														'id',
														'bulkLoadRecIdJfyApx',
														'bulkLoadRunIdJfyApx',
													]}
													allowPagination={false}
													enableInfinteScrolling={false}
													enableGlobalSearch={false}
													isSortingEnabled={false}
													enableGrouping={true}
													enableFilter={true}
													enableColumnSelection={true}
													enableReOrdering={true}
													isExportEnabled={false}
													showMoreOption={false}
													showColumnSelect={false}
													dataTableStyles={{
														width: '100%',
														height: '100%',
													}}
													datatableContarinerBoxStyles={{
														maxHeight: '100%',
														width: '100%',
													}}
												/>
											</div>
										</AccordionItemBody>
									</AccordionItem>
								) : (
									<></>
								)}
								{[recordDetails?.selectedRecord]?.length > 0 ? (
									<AccordionItem id="rhs-item-id" key="rhs-item-key">
										<AccordionItemHeader
											id="202"
											key="8"
											itemHeaderStyles={{ color: 'green' }}
										>
											RHS
										</AccordionItemHeader>
										<AccordionItemBody
											id="78378272"
											key="89768587"
											containerStyles={{
												width: '100%',
												padding: '15px',
											}}
										>
											<div style={{ width: '100%' }}>
												<DataTable
													id="736763"
													key="90328932"
													rowData={[recordDetails?.selectedRecord]}
													columnData={colData}
													hiddenColumns={[
														'id',
														'bulkLoadRecIdJfyApx',
														'bulkLoadRunIdJfyApx',
													]}
													showLoader={isLoading}
													allowPagination={false}
													enableGlobalSearch={false}
													enableInfinteScrolling={false}
													isSortingEnabled={false}
													enableGrouping={true}
													enableFilter={true}
													enableColumnSelection={true}
													enableReOrdering={true}
													isExportEnabled={false}
													showMoreOption={false}
													showColumnSelect={false}
													dataTableStyles={{
														width: '100%',
														height: '100%',
													}}
													datatableContarinerBoxStyles={{
														maxHeight: '100%',
														width: '100%',
													}}
												/>
											</div>
										</AccordionItemBody>
									</AccordionItem>
								) : (
									<></>
								)}
							</Accordion>
						</div>
					)}
				</Box>
			</ModalBody>
		</ModalWindows>
	);
});

export default TargetDetailsModal;

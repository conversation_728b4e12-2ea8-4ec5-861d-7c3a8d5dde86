/* eslint-disable @typescript-eslint/no-explicit-any */
/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { CSSProperties, ReactNode } from 'react';
import { Resolver, Mode, UseFormReturn } from 'react-hook-form';
import { IIconProps } from '@atomic/icon';
import { ArrowProps } from 'react-laag';
import { Layouts } from 'react-grid-layout';

export enum IFormMode {
	onBlur = 'onBlur',
	onChange = 'onChange',
	onSubmit = 'onSubmit',
	onTouched = 'onTouched',
	all = 'all',
}

export type IFormProps = {
	formId?: string;
	onSubmit?: (data: any, methods: UseFormReturn) => void;
	id?: string;
	defaultValues?: Record<string, unknown>;
	// eslint-disable-next-line @typescript-eslint/ban-types
	resolver?: Resolver<Record<string, unknown>, object>;
	validationTrigger?: Mode;
	formStyles?: Partial<CSSProperties>;
	ref?: any;
	renderWithSection?: boolean;
	layouts?: Layouts;
	header?: Header;
	columnLayout?: ColumnLayout;
	type?: Type;
	multiSectionsType?: MultiSectionsType;
	sectionOrientation?: SectionOrientation;
	summaryPage?: SummaryPage;
	validationType?: ValidationType;
	updateFormState?: Record<string, unknown>;
	mergeFormState?: Record<string, unknown>;
	fetchFormState?: string;
	formStateCallback?: any;
	customErrorList?: any;
	onSetCustomError?: any;
	backgroundColor?: string;
	children?: ReactNode;
	persistFormState?: boolean;
	decoratorStyles?: Partial<CSSProperties>;
	customDefaultValue?: any;
	onReset?: () => void;
	compositionData?: Record<string, any>;
};

export interface FormControlProps {
	name: string;
	validate?: () => any;
	required?: boolean;
	children?: React.ReactNode;
}
export interface FieldArrayProps {
	name: string;
	title: string;
	children: any;
}
export type FormClassLabel = 'form';

export interface CaptionMessageProps {
	error?: any;
	captionMessageType?: CaptionMessageType;
	captionIconStyles?: IIconProps;
	captionTextStyles?: CSSProperties;
	captionContainerStyles?: CSSProperties;
	captionArrowStyles?: ArrowProps;
	children?: React.ReactNode;
	overflowContainer?: boolean;
}

export enum CaptionMessageType {
	error = 'error',
	warning = 'warning',
	success = 'success',
	caption = 'caption',
}
export interface ControlWrapperProp {
	defaultValue?: any;
	onChange?: (e) => any;
	stateDataType?: StateDateType;
	eventKey?: EventKey;
	children?: React.ReactNode;
	returnEventOnChange?: boolean;
}

export type StateDateType = 'primitive' | 'array' | 'input';

export type EventKey = 'checked' | 'value';

export enum Header {
	enabled = 'enabled',
	disabled = 'disabled',
}

export enum ColumnLayout {
	flex = 'flex',
	twoColumn = 'two column',
	threeColumn = 'three column',
}

export enum Type {
	singleSection = 'single section',
	multiSection = 'multi section',
}

export enum MultiSectionsType {
	singlePage = 'single page',
	wizard = 'wizard',
	tab = 'tab',
}

export enum SummaryPage {
	enabled = 'enabled',
	disabled = 'disabled',
}

export enum SectionOrientation {
	horizontal = 'horizontal',
	vertical = 'vertical',
}

export enum ValidationType {
	inline = 'inline',
	guided = 'guided',
}

export enum FormType {
	tab = 'tab',
	accordion = 'accordion',
	section = 'section',
	wizard = 'wizard',
	conversation = 'conversation',
}

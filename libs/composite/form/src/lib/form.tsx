/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the <%= className %> component
import React, {
	FC,
	forwardRef,
	ReactElement,
	useImperativeHandle,
	useEffect,
	useRef,
	useCallback,
} from 'react';
import { configurable } from '@base/config-provider';
import { setForm, setMetaData } from '@base/utils-hooks';
import { defaultProps, metadata } from './metadata';
import { IFormProps } from './types';
import { FormProvider, useForm, UseFormReturn } from 'react-hook-form';
import useFormStyles from './useFormStyles';
import GridUi from '@base/grid-ui';

import _cloneDeep from 'lodash/cloneDeep';
import _deepMerge from 'lodash/merge';
import _get from 'lodash/get';
import Box, { BoxProps } from '@atomic/box';

const Form: FC<IFormProps> = forwardRef(
	(
		{
			defaultValues,
			formId,
			resolver,
			validationTrigger,
			children,
			onSubmit,
			formStyles,
			id,
			renderWithSection,
			layouts,
			updateFormState,
			mergeFormState,
			fetchFormState,
			formStateCallback,
			customErrorList,
			onSetCustomError,
			backgroundColor,
			persistFormState = false,
			customDefaultValue,
			onReset,
			compositionData,
			...rest
		},
		ref
	) => {
		// Framework generated code, below line registers the component
		// NOT TO BE DELETED
		setMetaData('Form', metadata);
		const editWizardIdRef = useRef();
		const customErrorRef = useRef(false);

		const formRef = useRef<HTMLFormElement | null>(null);

		const methods: UseFormReturn = useForm({
			defaultValues,
			resolver,
			mode: validationTrigger,
		});
		const handleSubmit = methods.handleSubmit(onSubmit as unknown as any);

		useImperativeHandle(ref, () => ({
			submit() {
				handleSubmit();
			},
		}));

		const handleSubmitEvent = (event: CustomEvent) => {
			const eventSourceFormId = event?.detail?.formId;
			if (eventSourceFormId && eventSourceFormId === formId) {
				handleSubmit();
			}
		};

		const retriveErrorFields = (errors, parent = '') => {
			const errorFields = [];

			// Iterate through the errors object
			for (const [key, value] of Object.entries(errors)) {
				const fieldName = parent ? `${parent}.${key}` : key;

				if (Array.isArray(value)) {
					// If it's an array, iterate over each element and recurse
					value.forEach((_, index) => {
						// Recurse for each item in the array, use dot notation instead of square brackets
						errorFields.push(
							...retriveErrorFields(value[index], `${fieldName}.${index}`)
						);
					});
				} else if (typeof value === 'object' && value !== null) {
					// If it's an object, check if it's an error object (contains 'message' or 'type')
					if ('message' in value || 'type' in value) {
						// It's an error object, so add the field name to the list
						errorFields.push(fieldName);
					} else {
						// Otherwise, recurse for nested fields
						errorFields.push(...retriveErrorFields(value, fieldName));
					}
				} else {
					// If it's not an object, it's a field with an error
					errorFields.push(fieldName);
				}
			}

			return errorFields;
		};

		const setNestedValues = (setValue, basePath, obj) => {
			for (const key in obj) {
				const path = basePath ? `${basePath}.${key}` : key;
				const value = obj[key];

				if (
					value &&
					typeof value === 'object' &&
					!Array.isArray(value) &&
					Object.keys(value)?.length > 0
				) {
					setNestedValues(setValue, path, value);
				} else {
					setValue(path, value);
				}
			}
		};

		const editFormState = (editFormState) => {
			if (editFormState && Object.keys(editFormState).length) {
				for (const property in editFormState) {
					const stateValue = methods.getValues(property);
					if (
						stateValue &&
						editFormState[property] !== null &&
						editFormState[property] !== undefined &&
						typeof stateValue == 'object' &&
						!Array.isArray(stateValue)
					) {
						//Done inorder to handle aggregate implementation.
						//if the value is {}, we apply the value instead of merging
						if (
							typeof editFormState[property] == 'object' &&
							!Array.isArray(editFormState[property]) &&
							Object.keys(editFormState[property]).length === 0
						) {
							methods.setValue(property, editFormState[property]);
						} else {
							//Deep merging the object to prevent data corruption
							const clonedState = _cloneDeep(stateValue);
							const mergedState = _deepMerge(
								{},
								clonedState,
								editFormState[property]
							);

							setNestedValues(methods.setValue, property, mergedState);
						}
					} else if (Array.isArray(editFormState[property])) {
						const clonedState = _cloneDeep(editFormState[property]);
						methods.setValue(property, clonedState);
					} else {
						methods.setValue(property, editFormState[property]);
					}
				}
			}
		};
		const handleNativeReset = useCallback(
			(e: Event) => {
				e.preventDefault();
				methods.reset();
				onReset?.();
			},
			[methods.reset]
		);

		useEffect(() => {
			const formEl = formRef.current;
			if (formEl) {
				formEl.addEventListener('reset', handleNativeReset);
			}

			window.addEventListener('triggerFormSubmit', handleSubmitEvent);
			return () => {
				window.removeEventListener('triggerFormSubmit', handleSubmitEvent);
				if (formEl) {
					formEl.removeEventListener('reset', handleNativeReset);
				}
			};
		}, []);

		setForm(formId, methods);
		const classes = useFormStyles(rest);
		useEffect(() => {
			if (updateFormState && Object.keys(updateFormState).length) {
				for (const property in updateFormState) {
					methods.setValue(property, updateFormState[property]);
				}
			}
			if (updateFormState && Object.keys(updateFormState).length == 0) {
				methods.reset();
			}
		}, [updateFormState]);

		useEffect(() => {
			if (fetchFormState && fetchFormState.length > 0) {
				formStateCallback?.(methods.getValues(fetchFormState));
			}
		}, [fetchFormState]);

		useEffect(() => {
			editFormState(mergeFormState);
		}, [mergeFormState]);

		useEffect(() => {
			if (customErrorList && Object.keys(customErrorList)?.length > 0) {
				customErrorRef.current = _cloneDeep(customErrorList);
				//clear all other external errors
				if (
					methods?.formState?.errors &&
					Object.keys(methods?.formState?.errors)?.length > 0
				) {
					const errorFields = retriveErrorFields(methods?.formState?.errors);
					const excludedFields = errorFields.filter(
						(field) => !Object.keys(customErrorList)?.includes(field)
					);
					if (excludedFields?.length > 0) {
						methods.clearErrors(excludedFields);
					}
				}

				//Set errors for the fields specified in the customErrorList
				if (Object.keys(customErrorList)?.length) {
					for (const property in customErrorList) {
						const type = customErrorList[property]?.type || 'custom';
						const message =
							customErrorList[property]?.message || customErrorList[property];
						methods.setError(property, {
							type,
							message,
						});
					}
					onSetCustomError?.();
				}
			}
			//Clear out the last backend validation error, this check will specifically lear the last outstanding wizard validation.
			//This can be extended for form as well.
			if (
				customErrorList &&
				Object.keys(customErrorList)?.length == 0 &&
				methods?.formState?.errors &&
				Object.keys(methods?.formState?.errors)?.length == 1
			) {
				const errorFields = retriveErrorFields(methods?.formState?.errors);
				const errorObject = _get(methods?.formState?.errors, errorFields[0]);
				if (errorObject.type === 'CustomWizardError') {
					methods.clearErrors();
					customErrorRef.current = false;
				}
			}
		}, [customErrorList]);

		useEffect(() => {
			if (customDefaultValue && Object.keys(customDefaultValue)?.length) {
				if (customDefaultValue?.['keepErrors'] === true) {
					const { keepErrors, ...data } = customDefaultValue;
					methods.reset(data, { keepErrors: true });
				} else {
					methods.reset(customDefaultValue);
				}
			}
		}, [customDefaultValue]);

		useEffect(() => {
			const wizardId = editWizardIdRef.current;
			if (
				customDefaultValue?.id &&
				customDefaultValue?.id !== wizardId &&
				mergeFormState &&
				Object.keys(mergeFormState)?.length > 0
			) {
				editWizardIdRef.current = customDefaultValue?.id;
				editFormState(mergeFormState);
			}
		}, [customDefaultValue?.id, mergeFormState]);

		useEffect(() => {
			if (
				compositionData &&
				typeof compositionData === 'object' &&
				Object.keys(compositionData).length === 2
			) {
				try {
					const { refreshKey, ...data } = compositionData;
					const rootKey = Object.keys(data)[0];
					const rootValue = compositionData[rootKey];

					// Function to find array in nested object and return its path
					const findArrayPath = (
						obj: any,
						currentPath: string = ''
					): string | null => {
						if (Array.isArray(obj)) {
							return currentPath;
						}

						if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
							for (const key in obj) {
								const newPath = currentPath ? `${currentPath}.${key}` : key;
								const result = findArrayPath(obj[key], newPath);
								if (result) {
									return result;
								}
							}
						}

						return null;
					};

					// Find the array path within the root value
					const arrayPath = findArrayPath(rootValue, rootKey);

					if (arrayPath) {
						// Get the array value using the found path
						const getNestedValue = (obj: any, path: string) => {
							return path
								.split('.')
								.reduce((current, key) => current?.[key], obj);
						};

						const arrayValue = getNestedValue(compositionData, arrayPath);
						if (Array.isArray(arrayValue)) {
							// Check if the arrayPath already has a value in the form state
							const existingValue = methods.getValues(arrayPath);

							if (!existingValue) {
								// No existing value, simply update the form state
								methods.setValue(arrayPath, arrayValue);
							} else {
								// Check if there are any errors for this arrayPath
								const hasErrors =
									customErrorRef.current &&
									Object.keys(customErrorRef.current).some(
										(errorKey) =>
											errorKey === arrayPath ||
											errorKey.startsWith(arrayPath + '.')
									);

								if (!hasErrors) {
									// No errors present, update the form state
									methods.setValue(arrayPath, arrayValue);
								} else {
									// Errors present - check nested paths and remove errors for changed values
									const nestedErrorKeys = Object.keys(
										customErrorRef.current
									)?.filter((errorKey) => errorKey.startsWith(arrayPath + '.'));

									// Finally update the form state at arrayPath with the arrayValue
									methods.setValue(arrayPath, arrayValue);

									//Setting the errors back for unchanged datatable fields.
									nestedErrorKeys.forEach((errorKey) => {
										const errorObject = _get(customErrorRef.current, errorKey);
										methods.setError(errorKey, errorObject);
									});
								}
							}
						}
					}
				} catch (exception) {
					console.log('Error while setting composition data', exception);
				}
			}
		}, [JSON.stringify(compositionData)]);

		const { height, ...formBoxProps } = formStyles;
		return (
			<FormProvider {...methods} {...{ persistFormState }}>
				<Box backgroundColor={backgroundColor} {...(formBoxProps as BoxProps)}>
					<form
						id={id}
						className={classes.form}
						onSubmit={handleSubmit}
						style={{ width: formStyles?.width }}
						ref={formRef}
					>
						{renderWithSection ? (
							<GridUi id={`GRID-${formId}`} type="main" layouts={layouts}>
								{children as unknown as ReactElement}
							</GridUi>
						) : (
							React.Children.map(children, (child: ReactElement) =>
								React.cloneElement(child as unknown as ReactElement, {
									style: formStyles,
									...rest,
									...child?.props,
								})
							)
						)}
					</form>
				</Box>
			</FormProvider>
		);
	}
);

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
Form.defaultProps = defaultProps;
Form.displayName = 'Form';
export default configurable(Form, 'Form');

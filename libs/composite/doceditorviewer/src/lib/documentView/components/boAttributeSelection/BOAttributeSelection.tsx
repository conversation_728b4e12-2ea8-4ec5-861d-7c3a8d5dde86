import { FC, useEffect, useState } from 'react';
import _noop from 'lodash/noop';
import { BOAttributeListing, BOListingUtils } from '../../../common/BOListing';
import { ModelRepoService } from './../../../apis';
import Text from '@atomic/text';
import { ApexTheme, DeviceTypes } from '@base/theme';
import { useTheme } from 'react-jss';
import { BOSelectionProps } from './types';
import Box from '@atomic/box';
import Button, { ButtonRadius, ButtonSizes, ButtonTypes } from '@atomic/button';
import Loader, { LoaderType } from './../../../common/loader';
import { useDocumentState } from '../../../documentInteractor';
import { getDisabledFields } from './helper';
import { DOCTYPE_FORM_MODE } from '../docTypeEditMenu/constants';
import { showToast, ToastType } from '@composite/toast';

const BOAttributeSelection: FC<BOSelectionProps> = ({
	onNextBtnClick,
	onBackBtnClick,
	nodeList,
	boSchemaId = '',
	schemaFields = [],
	mode,
}) => {
	const theme = useTheme<ApexTheme>();
	const [showLoader, setShowLoader] = useState(false);
	const [selectedFields, setSelectedFields] = useState<string[]>([]);
	const { documentInfo } = useDocumentState();

	const loadBO = async () => {
		setShowLoader(true);
		let response = await ModelRepoService.getBOLists(boSchemaId);
		response = response?.data?.[0];
		const disabledFields = getDisabledFields(schemaFields);
		const attrListData = BOListingUtils.prepareAttributeArray(
			response?.attribute.id,
			response?.fields,
			disabledFields,
			[],
			['File'],
			true,
			false,
			7
		);
		if (attrListData) {
			nodeList.set(attrListData);
		}
		const selectedFields = BOListingUtils.getSelectedFields(nodeList);
		setSelectedFields(selectedFields);
		setShowLoader(false);
	};

	useEffect(() => {
		if (nodeList.length == 0) {
			loadBO();
		} else {
			const selectedFields = BOListingUtils.getSelectedFields(nodeList);
			setSelectedFields(selectedFields);
		}
	}, [documentInfo?.docType?.value, boSchemaId]);

	const handleSelectionChange = () => {
		const selectedFields = BOListingUtils.getSelectedFields(nodeList);
		setSelectedFields(selectedFields);
	};

	const handleNextButtonCb = () => {
		const selectedFields = BOListingUtils.getSelectedFields(nodeList);
		if (
			mode === DOCTYPE_FORM_MODE.CREATE ||
			mode === DOCTYPE_FORM_MODE.PENDING_DOCTYPE
		) {
			const selectedFields = BOListingUtils.getSelectedFields(nodeList);
			if (selectedFields.length) {
				onNextBtnClick();
			} else {
				showToast({
					title: 'Error',
					description: 'Please select the fields to proceed',
					type: ToastType.Error,
					showIcon: true,
				});
			}
		} else {
			onNextBtnClick();
		}
	};

	return (
		<Loader
			show={showLoader}
			text="Loading Business Object"
			showBackground={false}
			type={LoaderType.InlineLoader}
		>
			<Text
				fontWeight={theme.fontWeights.semiBold}
				color={theme.colors.monochrome.body}
				fontSize={theme.fontSizes[DeviceTypes.Desktop].bSmall}
				marginBottom="12px"
			>
				Select the BO attributes
			</Text>
			<BOAttributeListing
				nodeList={nodeList}
				onChange={handleSelectionChange}
				showSelectAllFirstLevel={Boolean(nodeList.length)}
			/>

			<Box justifyContent={'space-between'} width="100%" paddingTop="16px">
				<Button
					id="backBtn"
					title="Back"
					onClick={onBackBtnClick}
					size={ButtonSizes.Small}
					radius={ButtonRadius.SemiRounded}
					buttonType={ButtonTypes.Secondary}
				/>
				<Button
					id="nextBtn"
					title="Next"
					onClick={handleNextButtonCb}
					size={ButtonSizes.Small}
					radius={ButtonRadius.SemiRounded}
					buttonType={ButtonTypes.Primary}
				/>
			</Box>
		</Loader>
	);
};

export default BOAttributeSelection;

import { useCallback, FC, useState, useEffect } from 'react';
import Text, { TextTypes } from '@atomic/text';
import { ApexTheme } from '@base/theme';
import { useTheme } from 'react-jss';
import {
	DocumentProcessModes,
	useDocumentState,
} from '../../../../documentInteractor';
import Box from '@atomic/box';
import Tabnavigation from '@atomic/tabnavigation';
import Tab, { TabsTypes, TabVariant } from '@atomic/segmenttabs';
import { focusSelectedField } from '../../../helper';
import { TrainingViewPanelProps } from './types';

import BoContainer from './BOContainer';
import AttributeListing from '../../../../common/attributeListing/attributeListing';
import Loader, { LoaderType } from '../../../../common/loader';
import { getDocType, buildAttributeArray } from './helper';

export const TrainingViewPanel: FC<TrainingViewPanelProps> = ({
	onSelectAttribute,
	trainingViewData,
}) => {
	const theme: ApexTheme = useTheme();
	const {
		documentInfo,
		selectedTableUUID,
		selectedTableHeader,
		selectedBOField,
		pageNumber,
		selectedRowId,
		documentError,
		errorMessage,
	} = useDocumentState();

	const { showLoader } = useDocumentState();
	const [showBOLoader, setShowBOLoader] = useState(false);
	const entityId = documentInfo.boSchemaId.value as any;
	const handleTableCellClick = useCallback(
		(boAttributeId: string, rowId: number, columnName: string) => {
			selectedTableUUID.set(boAttributeId);
			selectedBOField.set(boAttributeId);
			const tableId = documentInfo.extractedMeta.tables.keys.find(
				(tableId) =>
					documentInfo.extractedMeta.tables.nested(tableId).boAttributeId
						.value === boAttributeId
			);
			if (tableId) {
				const table = documentInfo.extractedMeta.tables.nested(tableId).get();
				const containsFilter =
					table?.finalFilteredRows && table?.finalFilteredRows != null;
				if (containsFilter) {
					rowId = table?.finalFilteredRows[rowId] || 0;
				}
				const selectedHeader = table.headers.find(
					(header) => header.display_name === columnName
				);
				selectedTableHeader.set(selectedHeader ? selectedHeader.uuid : '');
				selectedRowId.set(rowId);
				focusSelectedField(
					documentInfo.extractedMeta.fields.get(),
					documentInfo.extractedMeta.tables.get(),
					boAttributeId,
					pageNumber,
					rowId + 1
				);
			}
		},
		[]
	);

	const loadBO = async () => {
		setShowBOLoader(true);
		const docTypeResp = await getDocType(documentInfo.doctypeId.value);
		const { attributes, boName } = await buildAttributeArray(
			entityId,
			docTypeResp?.fields,
			6
		);
		trainingViewData.merge({
			boName: boName,
			boAttributesArray: attributes,
			customAttributes: docTypeResp?.customFields,
		});
		setShowBOLoader(false);
	};

	useEffect(() => {
		if (entityId && trainingViewData.boAttributesArray.length === 0) {
			loadBO();
		}
	}, [entityId, documentInfo.doctypeId.value]);

	return (
		<Loader
			type={LoaderType.InlineLoader}
			show={showBOLoader || showLoader.value}
			containerProps={{ height: 'calc( 100vh - 265px)' }}
		>
			<Tabnavigation
				variant={TabVariant.Default}
				type={TabsTypes.ValueOnly}
				isOverflow={false}
				tabChildrenProps={{
					overflowY: 'hidden',
					height: '100%',
					display: 'block',
				}}
				tabsAndContentContainerProps={{
					height: '100%',
					padding: '8px',
					overflowY: 'hidden',
				}}
			>
				<Tab
					title="Business Object"
					tabStyleProps={{ fontWeight: theme.fontWeights.bold }}
				>
					<Box flexDirection="column" width="100%" height="100%">
						{errorMessage?.value !== null &&
							errorMessage?.value !== undefined && (
								<Text
									text={errorMessage.value}
									color={theme.colors.monochrome.body}
									fontWeight={theme.fontWeights.semiBold}
									padding={'16px'}
								/>
							)}

						<BoContainer
							entityId={documentInfo.boSchemaId.value as any}
							onSelectAttribute={onSelectAttribute}
							onSelectCell={handleTableCellClick}
							trainingViewData={trainingViewData}
						/>
					</Box>
				</Tab>

				<Tab
					title="Custom attributes"
					tabStyleProps={{
						fontWeight: theme.fontWeights.bold,
					}}
				>
					<AttributeListing
						attributeList={trainingViewData.customAttributes}
						showValue={true}
						boInstanceData={documentInfo.customAttributeBoData}
					/>
				</Tab>
			</Tabnavigation>
		</Loader>
	);
};

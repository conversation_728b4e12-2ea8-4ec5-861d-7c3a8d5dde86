import { useCallback, useEffect, useState, FC, useMemo } from 'react';
import BOAttributes from './BOAttributes';
import {
	useDocumentState,
	DocumentProcessModes,
	DocumentInteractorModes,
} from '../../../../documentInteractor';
import Box from '@atomic/box';
import {
	BoContainerProps,
	MultiplicityTypes,
	NestedLevelInfoType,
} from './types';
import Header from './Header';
import {
	getCurrentBOAttributes,
	getCurrentBOInstanceData,
} from './helper';
import { useTrainingViewPanelStyles } from './style';
import { AttributeType } from './types';
import { showToast, ToastType } from '@composite/toast';

const BoContainer: FC<BoContainerProps> = ({
	entityId,
	onSelectAttribute,
	onSelectCell,
	trainingViewData,
}) => {
	const MAX_GENERATE_LEVEL = 7;
	const MAX_EXTRACT_LEVEL = 6;
	const { documentInfo, showLoader, disabled } = useDocumentState();

	const boInstanceData = documentInfo.boInstanceData;
	const mode = documentInfo.mode.value;
	const { boAttributesArray, nestedLevelInfo, boName } = trainingViewData.get();
	const currentBOAttributes = useMemo(() => {
		return getCurrentBOAttributes(boAttributesArray, nestedLevelInfo);
	}, [boAttributesArray, nestedLevelInfo]);

	const currentBOInstanceData = useMemo(() => {
		return getCurrentBOInstanceData(boInstanceData, nestedLevelInfo, mode);
	}, [boInstanceData.value, nestedLevelInfo, mode]);

	const classes = useTrainingViewPanelStyles();

	const handleLinkedItemClick = (field: AttributeType) => {
		if (
			mode === DocumentProcessModes.Generate &&
			nestedLevelInfo.length + 1 >= MAX_GENERATE_LEVEL
		) {
			showToast({
				description: `Generate supports upto ${MAX_GENERATE_LEVEL} Levels`,
				type: ToastType.Warning,
				showIcon: true,
				containerStyles: { whiteSpace: 'initial' },
				informationBoxProps: { marginTop: '2px' },
			});
			return;
		} else if (
			mode === DocumentProcessModes.Extraction &&
			nestedLevelInfo.length + 1 >= MAX_EXTRACT_LEVEL
		) {
			showToast({
				description: `Extraction supports upto ${MAX_EXTRACT_LEVEL} Levels`,
				type: ToastType.Warning,
				showIcon: true,
				containerStyles: { whiteSpace: 'initial' },
				informationBoxProps: { marginTop: '2px' },
			});
			return;
		}
		trainingViewData.nestedLevelInfo.merge([
			{
				name: field.name,
				id: field.id,
				label: field.label,
				multiplicity: field?.reference?.multiplicity as MultiplicityTypes,
			},
		]);
		setTimeout(() => {
			const element = document.getElementById('boHeaderScroll');
			if (element) {
				element.scrollLeft = element.scrollWidth;
			}
		}, 100);
	};

	const handleTableCellClick = (id: string, rowId: number, colName: string) => {
		onSelectCell(id, rowId, colName);
	};

	const changeRowCallback = (rowIndex: number) => {
		trainingViewData.nestedLevelInfo[nestedLevelInfo.length - 1]?.merge({
			index: rowIndex,
		});
	};

	const handleHeaderClick = (
		index: number,
		levelInfo?: NestedLevelInfoType
	) => {
		if (index == -1) {
			trainingViewData.nestedLevelInfo.set([]);
			return;
		}
		trainingViewData.nestedLevelInfo.set(
			JSON.parse(JSON.stringify(nestedLevelInfo.slice(0, index + 1)))
		);
	};

	const handleAddRowClick = () => {
		currentBOInstanceData.merge([{}] as any);

		trainingViewData.nestedLevelInfo[nestedLevelInfo.length - 1]?.merge({
			index: (currentBOInstanceData?.length as any) - 1,
		});
	};

	return (
		<Box
			display={'flex'}
			flexDirection={'column'}
			height="100%"
			className={classes.BOContainer}
		>
			<Header
				boName={boName}
				mode={mode}
				nestedLevelInfo={nestedLevelInfo}
				onHeaderClick={handleHeaderClick}
				onAddRowClick={handleAddRowClick}
				disabled={disabled.value}
			/>
			<BOAttributes
				boAttributes={currentBOAttributes}
				nestedLevelInfo={nestedLevelInfo}
				currentBOInstanceData={currentBOInstanceData}
				onLinkedItemClick={handleLinkedItemClick}
				setSelectedAttribute={onSelectAttribute}
				onTableCellClick={handleTableCellClick}
				rowCallback={changeRowCallback}
			/>
		</Box>
	);
};

export default BoContainer;

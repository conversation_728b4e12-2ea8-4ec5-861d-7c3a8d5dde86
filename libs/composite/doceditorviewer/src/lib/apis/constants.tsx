import { generateUUID } from '../common/utils';

export enum DOC_STATUS {
	NEW_TYPE = 'NEW_TYPE',
	PRE_PROCESSING_COMPLETED = 'PRE_PROCESSING_COMPLETED',
	PROCESSING_COMPLETED = 'PROCESSING_COMPLETED',
	C<PERSON><PERSON><PERSON>ICATION_COMPLETED = 'CLA<PERSON><PERSON>ICATION_COMPLETED',
	FORMAT_IDENTIFICATION_COMPLETED = 'FORMAT_IDENTIFICATION_COMPLETED',
	EXTRACTED_SUCCESSFULLY = 'EXTRACTED_SUCCESSFULLY',
	TECHNICAL_ERROR = 'TECHNICAL_ERROR',
	VALIDATION_FAILED = 'VALIDATION_FAILED',
	IN_REVIEW = 'IN_REVIEW',
	IN_EDIT = 'IN_EDIT',
	NEW_FORMAT = 'NEW_FORMAT',
	IGNORED = 'IGNORED',
	REJECTED = 'REJECTED',
	APPROVED = 'APPROVED',
	COMPLETED = 'COMPLETED',
	FAILED = 'FAILED',
	APPROVAL_FAILED = 'APPROVAL_FAILED',
	CHILD_ERROR = 'CHILD_ERROR',
	WARNING = 'WARNING',
	PENDING = 'PENDING',
	SELECT_DOC_TYPE = 'SELECT_DOC_TYPE',
	PENDING_DOCTYPE_MAPPING = 'PENDING_DOCTYPE_MAPPING',
}

export const STATUS_MAP = {
	ERROR: {
		statusList: [10, 11, 7, 8],
		name: 'ERROR',
		statusNameList: [
			DOC_STATUS.TECHNICAL_ERROR,
			DOC_STATUS.VALIDATION_FAILED,
			DOC_STATUS.IGNORED,
			DOC_STATUS.REJECTED,
		],
	},
	COMPLETED: {
		statusList: [6],
		name: 'COMPLETED',
		statusNameList: [DOC_STATUS.EXTRACTED_SUCCESSFULLY],
	},
	IN_QUEUE: {
		statusList: [1, 2, 3, 4, 5],
		name: 'IN_QUEUE',
		statusNameList: [
			DOC_STATUS.NEW_TYPE,
			DOC_STATUS.PRE_PROCESSING_COMPLETED,
			DOC_STATUS.PROCESSING_COMPLETED,
			DOC_STATUS.CLASSIFICATION_COMPLETED,
			DOC_STATUS.FORMAT_IDENTIFICATION_COMPLETED,
		],
	},
	REVIEW: {
		statusList: [9],
		name: 'REVIEW',
		statusNameList: [
			DOC_STATUS.IN_REVIEW,
			DOC_STATUS.IN_EDIT,
			DOC_STATUS.NEW_FORMAT,
		],
	},
};

export const DOC_STATUS_MAP: Record<number, DOC_STATUS> = {
	1: DOC_STATUS.NEW_TYPE,
	2: DOC_STATUS.PRE_PROCESSING_COMPLETED,
	3: DOC_STATUS.PROCESSING_COMPLETED,
	4: DOC_STATUS.CLASSIFICATION_COMPLETED,
	5: DOC_STATUS.FORMAT_IDENTIFICATION_COMPLETED,
	6: DOC_STATUS.EXTRACTED_SUCCESSFULLY,
	7: DOC_STATUS.TECHNICAL_ERROR,
	8: DOC_STATUS.VALIDATION_FAILED,
	9: DOC_STATUS.IN_REVIEW,
	10: DOC_STATUS.IGNORED,
	11: DOC_STATUS.REJECTED,
	12: DOC_STATUS.APPROVED,
	13: DOC_STATUS.COMPLETED,
	14: DOC_STATUS.FAILED,
	15: DOC_STATUS.APPROVAL_FAILED,
	16: DOC_STATUS.CHILD_ERROR,
	17: DOC_STATUS.WARNING,
	18: DOC_STATUS.PENDING,
	19: DOC_STATUS.SELECT_DOC_TYPE,
	20: DOC_STATUS.PENDING_DOCTYPE_MAPPING,
	21: DOC_STATUS.IN_EDIT,
	22: DOC_STATUS.NEW_FORMAT,
};

export const TRACE_ID = generateUUID();

export const FILTER_CONDITIONS = {
	IS_NOT: 'IS_NOT',
	IS: 'IS',
	CONTAINS: 'CONTAINS',
	IN: 'IN',
};

export enum SERVICES {
	MODEL_REPO = '/model-repo/rest/v1',
	COMPONENT_LIB = '/cls/api/v1',
	WORKFLOW = '/workflow/v1',
	PAM = '/pam',
	JIFFY_DRIVE = '/drive/v1',
	IAM = '/apexiam/v1',
	DOCUMENT_SERVICE = 'docproc/api/v1',
}

export const REQUIRED_DOC_INFO =
	'documentData=executionState,extractedData,extractedMeta,fileInfo,fileName,format,formatLabel,id,ocrEngine,selectedPages&fields=artifactsEditable,boInstanceData,boInstanceId,boSchemaId,boSchemaName,doctype,doctypeId,id,logTraceId,mode,namespace,parent,status,workflowId,docTypeModel,error';
export const DEFAULT_RECORD_COUNT_MAX = 40;
export const GROUPED_RECORDS_COUNT_MAX = 1000;
export const WRITE_PDF_TYPE = 'Generate PDF';
export const DIGITIZER_TYPE = 'DIGITIZER';

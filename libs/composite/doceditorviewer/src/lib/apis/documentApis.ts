import { useAPIService as editorService } from '@base/utils-hooks';
import { urlJoin } from 'url-join-ts';
import { ExtractedMeta } from './../documentInteractor/types';
import {
	DIGITIZER_TYPE,
	FILTER_CONDITIONS,
	SERVICES,
	STATUS_MAP,
	TRACE_ID,
	WRITE_PDF_TYPE,
} from './constants';
import {
	DEFAULT_RECORD_COUNT_MAX,
	GROUPED_RECORDS_COUNT_MAX,
	REQUIRED_DOC_INFO,
} from './constants';
import { StateMethods } from '@hookstate/core';

const getStatusFilter = (statusOfDocument?: string) => {
	if (
		statusOfDocument !== '' &&
		typeof statusOfDocument !== 'undefined' &&
		statusOfDocument in STATUS_MAP
	) {
		let statusNames: string[] = [];
		switch (statusOfDocument) {
			case STATUS_MAP.ERROR.name:
				statusNames = STATUS_MAP.ERROR.statusNameList;
				break;
			case STATUS_MAP.IN_QUEUE.name:
				statusNames = STATUS_MAP.IN_QUEUE.statusNameList;
				break;
			case STATUS_MAP.REVIEW.name:
				statusNames = STATUS_MAP.REVIEW.statusNameList;
				break;
			case STATUS_MAP.COMPLETED.name:
				statusNames = STATUS_MAP.COMPLETED.statusNameList;
				break;
		}
		if (statusNames.length !== 0) {
			return {
				parameter: 'status',
				condition: 'IN',
				value: statusNames,
			};
		}
	}
	return null;
};

const getStatusFilterWithStatusId = (statusOfDocument?: string) => {
	if (
		statusOfDocument !== '' &&
		typeof statusOfDocument !== 'undefined' &&
		statusOfDocument in STATUS_MAP
	) {
		let statusIds: number[] = [];
		switch (statusOfDocument) {
			case STATUS_MAP.ERROR.name:
				statusIds = STATUS_MAP.ERROR.statusList;
				break;
			case STATUS_MAP.IN_QUEUE.name:
				statusIds = STATUS_MAP.IN_QUEUE.statusList;
				break;
			case STATUS_MAP.REVIEW.name:
				statusIds = STATUS_MAP.REVIEW.statusList;
				break;
			case STATUS_MAP.COMPLETED.name:
				statusIds = STATUS_MAP.COMPLETED.statusList;
				break;
		}
		if (statusIds.length !== 0) {
			return {
				parameter: 'apex_document.status',
				condition: 'IN',
				value: statusIds,
			};
		}
	}
	return null;
};

const getDocTypeNameFilter = (searchKey?: string) => {
	if (searchKey && searchKey.trim() !== '') {
		return {
			parameter: 'doctype.name',
			condition: FILTER_CONDITIONS.CONTAINS,
			value: searchKey,
		};
	}
	return null;
};
const getDomainAppFilter = (selectedApp?: string | string[]) => {
	if (selectedApp && selectedApp === 'ALL') {
		return null;
	}
	return {
		parameter: 'doctype.domainAppInfo.domain_name',
		condition: Array.isArray(selectedApp)
			? FILTER_CONDITIONS.IN
			: FILTER_CONDITIONS.IS,
		value: Array.isArray(selectedApp)
			? selectedApp
			: selectedApp !== 'App' && selectedApp && selectedApp !== ''
			? `${selectedApp}`
			: null,
	};
};
export class DocumentServiceClass {
	private documentServiceURL!: string;
	private headers!: Record<string, string>;

	constructor() {
		this.documentServiceURL = SERVICES.DOCUMENT_SERVICE;
		this.headers = { logParams: JSON.stringify({ logTraceID: TRACE_ID }) };
	}

	getHeaders = (traceId: string) => {
		return { logParams: JSON.stringify({ logTraceID: traceId }) };
	};

	getDocumentTypes = async (
		statusOfDocument?: string,
		selectedApp?: string | string[],
		executionState?: string | null,
		startIndex?: number,
		endIndex?: number,
		searchKey?: string
	): Promise<any> => {
		const documentData: any = {
			filter: {
				operation: 'AND',
				data: [
					{
						parameter: 'singleDoc',
						condition: FILTER_CONDITIONS.IS,
						value: true,
					},
					{
						parameter: 'doctype.name',
						condition: FILTER_CONDITIONS.IS_NOT,
						value: DIGITIZER_TYPE,
					},
					{
						parameter: 'doctype.name',
						condition: FILTER_CONDITIONS.IS_NOT,
						value: WRITE_PDF_TYPE,
					},
				],
			},
			expectedResults: [
				{
					format: 'aggregation',
					aggregation: {
						method: 'count',
						distinct: true,
						aggregateOn: 'document.id',
						groupBy: 'doctype.name',
						includeZeroElements: false,
					},
					extraFields: [
						'doctype.name',
						'doctype.id',
						'doctype.is_domain_model',
						'doctype.description',
						'doctype.thumbnail_path',
					],
					paginationDetails: {
						startIndex: startIndex,
						endIndex: endIndex,
					},
					includePaginationDetails: true,
				},
			],
		};
		const domainAppFilter = getDomainAppFilter(selectedApp);
		if (domainAppFilter !== null) {
			documentData.filter.data.push(domainAppFilter);
		}

		const statusFilter = getStatusFilter(statusOfDocument);
		if (statusFilter !== null) {
			documentData.filter.data.push(statusFilter);
		}

		const docTypeFilter = getDocTypeNameFilter(searchKey);
		if (docTypeFilter !== null) {
			documentData.filter.data.push(docTypeFilter);
		}

		if (executionState) {
			documentData.filter.data.push({
				parameter: 'documentData.executionState',
				condition: 'IS',
				value: 'COMPLETED',
			});

			documentData.expectedResults[0].aggregation = {
				...documentData.expectedResults[0].aggregation,
				includeOnlyGoldenFiles: true,
			};
		}

		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'document/search/', '/'),
			documentData,
			this.headers
		);
	};

	getDocumentFormats = async (
		docType: string,
		statusOfDocument?: string,
		appName?: string,
		executionState?: string | null,
		startIndex?: number,
		endIndex?: number
	): Promise<any> => {
		const documentData: any = {
			filter: {
				operation: 'AND',
				data: [
					{
						parameter: 'singleDoc',
						condition: 'IS',
						value: true,
					},
					{
						parameter: 'doctype.name',
						condition: 'IS',
						value: docType,
					},
					{
						parameter: 'doctype.domainAppInfo.domain_name',
						condition: 'IS',
						value:
							appName !== 'App' && appName && appName !== ''
								? `${appName}`
								: null,
					},
				],
			},
			expectedResults: [
				{
					format: 'aggregation',
					extraFields: [
						'document_data.format.name',
						'document_data.format.description',
						'document_data.format.thumbnail',
					],
					aggregation: {
						method: 'count',
						distinct: true,
						aggregateOn: 'document.id',
						groupBy: 'document_data.format.label',
						includeZeroElements: false,
					},
					paginationDetails: {
						startIndex: startIndex,
						endIndex: endIndex,
					},
					includePaginationDetails: true,
				},
			],
		};
		const statusFilter = getStatusFilterWithStatusId(statusOfDocument);
		if (statusFilter !== null) {
			documentData.filter.data.push(statusFilter);
		}

		if (executionState) {
			documentData.filter.data.push({
				parameter: 'documentData.executionState',
				condition: 'IS',
				value: 'COMPLETED',
			});
			documentData.expectedResults[0].aggregation = {
				...documentData.expectedResults[0].aggregation,
				includeOnlyGoldenFiles: true,
			};
		}

		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'document/search/', '/'),
			documentData,
			this.headers
		);
	};

	getTrainedMultiDocTypeDocuments = async (
		startIndex = 0,
		endIndex?: number,
		domainAppName?: string
	): Promise<any> => {
		const documentData: any = {
			paginationDetails: {
				startIndex: startIndex,
				endIndex: endIndex,
			},
		};

		if (domainAppName) {
			documentData.filter = {
				doctype__domain_app_info__domain_name: domainAppName,
			};
		}

		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'document/multi-document-view/', '/'),
			documentData,
			this.headers
		);
	};

	getTemplateDocuments = async (
		templateType: string,
		format: string | null = ''
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`format/${format}/mode/${templateType}/base_document`,
				'/'
			),
			this.headers
		);
	};

	getDocuments = async (
		typeOfDocument: string,
		parentDocId: number | undefined = undefined,
		startIndex = 0,
		groupingEnabled?: boolean,
		format: string | null = '',
		endIndex?: number,
		executionState?: string | null,
		isProcessExecDocs?: boolean,
		filterQuery: any = {}
	): Promise<any> => {
		let documentData: any = {
			filter: {
				operation: 'AND',
				data: [
					{
						parameter: 'singleDoc',
						condition: FILTER_CONDITIONS.IS,
						value: true,
					},
					...filterQuery,
				],
			},
			expectedResults: [
				{
					format: 'list',
					data: [
						{
							data: 'id',
						},
						{
							data: 'documentData.fileName',
						},
						{
							data: 'documentData.executionState',
						},
						{
							data: 'doctype',
						},
						{
							data: 'status',
						},
						{
							data: 'mode',
						},
						{
							data: 'error',
						},
						{
							data: 'detailed_error',
						},
						{
							data: 'log_trace_id',
						},
						{
							data: 'documentData.format.label',
						},
						{
							data: 'workflowId',
						},
						{
							data: 'errorCount',
						},
					],
					paginationDetails: {
						startIndex,
						endIndex: groupingEnabled
							? startIndex + GROUPED_RECORDS_COUNT_MAX
							: endIndex || startIndex + DEFAULT_RECORD_COUNT_MAX,
					},
					sortOrder: {
						mode: 'DESCENDING',
						data: 'lastUpdatedDate',
					},
					includePaginationDetails: true,
				},
			],
		};

		if (typeOfDocument !== '') {
			documentData.filter.data.push({
				parameter: 'doctype.name',
				condition: FILTER_CONDITIONS.IS,
				value: typeOfDocument,
			});
		} else if (typeOfDocument === '') {
			documentData.filter.data.push({
				parameter: 'doctype.name',
				condition: FILTER_CONDITIONS.IS_NOT,
				value: WRITE_PDF_TYPE,
			});
			documentData.filter.data.push({
				parameter: 'doctype.name',
				condition: FILTER_CONDITIONS.IS_NOT,
				value: DIGITIZER_TYPE,
			});
		}

		if (format) {
			documentData.filter.data.push({
				parameter: 'documentData.format.label',
				condition: FILTER_CONDITIONS.IS,
				value: format === 'Unknown variants' ? null : format,
			});
		}

		if (executionState) {
			documentData = { ...documentData, includeOnlyGoldenFiles: true };
		}

		if (isProcessExecDocs) {
			documentData.filter.data.push({
				parameter: 'isProductionRun',
				condition: 'IS',
				value: true,
			});
		}

		if (parentDocId) {
			documentData.filter.data.push({
				parameter: 'parent_id',
				condition: 'IS',
				value: parentDocId,
			});
		}

		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'document/search/', '/'),
			documentData,
			this.headers
		);
	};

	getDocumentsList = async (
		docType: string,
		format: string | null = '',
		goldenFiles = false,
		isProcessExecDocs = false,
		startIndex = 0,
		filterQuery: any = {},
		endIndex?: number
	): Promise<any> => {
		let documentData: any = {
			filter: {
				operation: 'AND',
				data: [
					{
						parameter: 'singleDoc',
						condition: FILTER_CONDITIONS.IS,
						value: true,
					},
					...filterQuery,
				],
			},
			expectedResults: [
				{
					format: 'list',
					data: [
						{
							data: 'id',
						},
						{
							data: 'documentData.fileName',
						},
						{
							data: 'documentData.executionState',
						},
						{
							data: 'doctype',
						},
						{
							data: 'status',
						},
						{
							data: 'mode',
						},
						{
							data: 'documentData.format.label',
						},
						{
							data: 'lastUpdatedDate',
						},
					],
					paginationDetails: {
						startIndex,
						endIndex: endIndex || startIndex + DEFAULT_RECORD_COUNT_MAX,
					},
					includePaginationDetails: true,
					sortOrder: {
						mode: 'DESCENDING',
						data: 'lastUpdatedDate',
					},
				},
			],
		};

		if (goldenFiles) {
			documentData = { ...documentData, includeOnlyGoldenFiles: true };
		}

		if (docType) {
			documentData.filter.data.push({
				parameter: 'doctype.name',
				condition: FILTER_CONDITIONS.IS,
				value: docType,
			});
		}

		if (format) {
			documentData.filter.data.push({
				parameter: 'documentData.format.label',
				condition: FILTER_CONDITIONS.IS,
				value: format === 'Unknown variants' ? null : format,
			});
		}

		if (isProcessExecDocs) {
			documentData.filter.data.push({
				parameter: 'isProductionRun',
				condition: 'IS',
				value: true,
			});
		}

		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'document/search/', '/'),
			documentData,
			this.headers
		);
	};

	getDocumentInfo = async (docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`document/${docId}/?${REQUIRED_DOC_INFO}`
			),
			{},
			this.headers
		);
	};

	getDocumentCleansingInfo = async (docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`document/${docId}/cleansing-pattern`,
				'/'
			),
			{},
			this.headers
		);
	};

	getDocumentFieldExtractionRules = async (docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`document/${docId}/field-extraction-rule/`,
				'/'
			),
			{},
			this.headers
		);
	};

	updateFieldExtractionRule = async (
		extractionRuleId: number | undefined,
		data: any
	): Promise<any> => {
		if (extractionRuleId) {
			return editorService.call(
				'editors-with-appInstance-headers',
				'PATCH',
				urlJoin(
					this.documentServiceURL,
					`field-extraction-rule/${extractionRuleId}/`,
					'/'
				),
				data,
				this.headers
			);
		} else {
			return editorService.call(
				'editors-with-appInstance-headers',
				'POST',
				urlJoin(this.documentServiceURL, 'field-extraction-rule/', '/'),
				data,
				this.headers
			);
		}
	};

	getAvailableDocTypes = async (): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, 'docType/app/all/', '/'),
			{},
			this.headers
		);
	};

	getAvailableDomainDocTypes = async (): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, 'docType/domain/all/', '/'),
			{},
			this.headers
		);
	};

	getAllDocTypes = async (): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, 'docType/'),
			{},
			this.headers
		);
	};

	getAllDocTypesWithDomainModels = async (): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, 'docType/dropdown_values/', '/'),
			{},
			this.headers
		);
	};

	getDocTypeByName = async (docTypeName: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`docType/get_by_name/${docTypeName}/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getDocType = async (docTypeId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, `docType/${docTypeId}/`, '/'),
			{},
			this.headers
		);
	};

	getAvailableMlModels = async (): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, 'mlmodel/get/auditor/', '/'),
			{},
			this.headers
		);
	};

	getAllMlModels = async (): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, 'mlmodels/'),
			{},
			this.headers
		);
	};

	updateMlModel = async (data: Record<string, any>): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, `mlmodel/update/auditor/`, '/'),
			data,
			this.headers
		);
	};

	getPossibleFields = async (docId: number, segmentInfo: any): Promise<any> => {
		const data: any = {
			documentId: docId,
			fieldData: segmentInfo,
		};
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'fieldFinder/', '/'),
			data,
			this.headers
		);
	};

	getArtifact = async (
		type: string,
		docId: number,
		pageNo: number
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`document/files/${type}/${docId}/${pageNo}/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getAllArtifacts = async (type: string, docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, `document/files/${type}/${docId}/`, '/'),
			{},
			this.headers
		);
	};

	getPseudonyms = async (fieldUUID: string, format: string) => {
		const data = {
			tag: fieldUUID,
			format: format,
		};
		const response = await editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'pseudonym/getListForFormat/', '/'),
			data,
			this.headers
		);
		if (response.data.status) {
			const responseJSON = response.data.data;
			return responseJSON[fieldUUID]?.aliases || [];
		}
	};

	updateTableFilterRule = async (id: number, data: any) => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'PATCH',
			urlJoin(this.documentServiceURL, `table-filter-rule/${id}/`, '/'),
			data,
			this.headers
		);
	};

	updatePseudonyms = async (
		fieldUUID: string,
		pseudonyms: string[],
		format: string
	) => {
		const data: any = {
			format: format,
			tagData: {
				[fieldUUID]: {
					aliases: pseudonyms,
				},
			},
		};
		const response = await editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'pseudonym/createListForFormat/', '/'),
			data,
			this.headers
		);
		return response.data;
	};

	updateDocumentType = async (
		typeOfDocument: string,
		docIDs: number[],
		traceId: string,
		thumbnailPath = '',
		splitIdentifier = '',
		extractionMode = '',
		description?: string
	): Promise<any> => {
		const promises = docIDs.map((docId: number) => {
			return editorService.call(
				'editors-with-appInstance-headers',
				'POST',
				urlJoin(this.documentServiceURL, `document/${docId}/classify/`, '/'),
				{
					docType: typeOfDocument,
					thumbnailPath: thumbnailPath,
					splitIdentifier: splitIdentifier,
					extractionMode: extractionMode,
					...(description && { description: description }),
				},
				this.getHeaders(traceId)
			);
		});
		const response = await Promise.all(promises);
		return response;
	};

	updateDocumentTypeNewDoc = async (
		typeOfDocument: string,
		docIDs: number[],
		traceId: string,
		thumbnailPath = '',
		splitIdentifier = '',
		extractionMode = ''
	): Promise<any> => {
		const promises = docIDs.map((docId: number) => {
			return editorService.call(
				'editors-with-appInstance-headers',
				'POST',
				urlJoin(
					this.documentServiceURL,
					`document/${docId}/classifyNewType/`,
					'/'
				),
				{
					docType: typeOfDocument,
					thumbnailPath: thumbnailPath,
					splitIdentifier: splitIdentifier,
					extractionMode: extractionMode,
				},
				this.getHeaders(traceId)
			);
		});
		const response = await Promise.all(promises);
		return response;
	};

	fetchTableSchema = async (
		typeOfDocument: string,
		docID: number
	): Promise<any> => {
		const response = await editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`document/${docID}/getExtractedTableObjects/`,
				'/'
			),
			{
				docType: typeOfDocument,
			},
			this.headers
		);
		return response;
	};

	getAllCategories = async (): Promise<any> => {
		const response = await editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, `/format`, '/'),
			this.headers
		);
		return response;
	};

	updateOnSaveDocumentInfo = async (
		docID: number,
		extractedMeta: ExtractedMeta,
		boInstanceId?: string | null,
		boSchemaId?: string | null
	): Promise<any> => {
		const requestData: any =
			boInstanceId && boSchemaId
				? {
						boInstanceId,
						boSchemaId,
						documentData: {
							extractedMeta,
						},
				  }
				: {
						documentData: {
							extractedMeta,
						},
				  };
		const response = await editorService.call(
			'editors-with-appInstance-headers',
			'PATCH',
			urlJoin(this.documentServiceURL, `document/${docID}/`, '/'),
			requestData,
			this.headers
		);
		return response;
	};

	updateDocumentStatus = async (
		docID: number,
		status: string,
		executionState: string
	): Promise<any> => {
		const requestData: any = {
			status: status,
			documentData: {
				executionState: executionState,
			},
		};
		return editorService.call(
			'editors-with-appInstance-headers',
			'PATCH',
			urlJoin(this.documentServiceURL, `document/${docID}/`, '/'),
			requestData,
			this.headers
		);
	};

	updateDocumentInfo = async (docID: number, data: any): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'PATCH',
			urlJoin(this.documentServiceURL, `document/${docID}/`, '/'),
			data,
			this.headers
		);
	};

	getDocumentSchemaInfo = async (docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`document/${docId}/getDisplayNames/`,
				'/'
			),
			this.headers
		);
	};

	getFieldMlModels = async (models: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`field/mlmapper/?schema=${models}/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getTableMlModels = async (): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, `table/group/mlmapper/`, '/'),
			{},
			this.headers
		);
	};

	getAllDocumentsCount = async (data?: any): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, `document/count`, '/'),
			data || {},
			this.headers
		);
	};

	getDocumentCountBasedOnStatus = async (filterQuery: any[]): Promise<any> => {
		const queryData: Record<string, any> = {
			filter: {
				operation: 'AND',
				data: [
					{
						parameter: 'singleDoc',
						condition: 'IS',
						value: true,
					},
					{
						parameter: 'apex_document.is_domain_model',
						condition: 'IS',
						value: false,
					},
					...filterQuery,
				],
			},
			expectedResults: [
				{
					format: 'aggregation',
					extraFields: [],
					aggregation: {
						method: 'count',
						distinct: true,
						aggregateOn: 'document.id',
						groupBy: 'status',
						includeZeroElements: false,
					},
				},
			],
		};

		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'document/search/', '/'),
			queryData,
			this.headers
		);
	};

	getDocumentsByType = async (
		docType: string,
		status?: string
	): Promise<any> => {
		const documentData: any = {
			filter: {
				operation: 'AND',
				data: [
					{
						parameter: 'singleDoc',
						condition: FILTER_CONDITIONS.IS,
						value: true,
					},
					{
						parameter: 'doctype.name',
						condition: FILTER_CONDITIONS.IS,
						value: docType,
					},
				],
			},
			expectedResults: [
				{
					format: 'list',
					data: [
						{
							data: 'id',
						},
						{
							data: 'documentData.fileName',
						},
						{
							data: 'documentData.format.label',
						},
						{
							data: 'documentData.format.name',
						},
					],
					paginationDetails: {
						startIndex: 0,
						endIndex: 40,
					},
					sortOrder: {
						mode: 'DESCENDING',
						data: 'lastUpdatedDate',
					},
				},
			],
		};
		const statusFilter = getStatusFilter(status);
		if (statusFilter !== null) {
			documentData.filter.data.push(statusFilter);
		}

		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'document/search/', '/'),
			documentData,
			this.headers
		);
	};

	getThumbnail = async (docType: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, `doctype/${docType}/thumbnail`, '/'),
			{},
			this.headers
		);
	};

	getExpressionSuggestionsField = async (documentId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`transformation/${documentId}/docType/fields/variables/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getExpressionSuggestionsTable = async (
		documentId: number,
		tableUUID: string
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`transformation/${documentId}/docType/table/${tableUUID}/variables/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getExpressionSuggestionsTableRowFilter = async (
		documentId: number,
		tableUUID: string
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`document/${documentId}/table/${tableUUID}/table-filter-rule/variables`,
				'/'
			),
			{},
			this.headers
		);
	};

	getTransformationRuleField = async (
		docId: number,
		fieldUUID: string
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`transformation/getrule/${docId}/field/${fieldUUID}/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getAllTransformationRules = async (docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`transformation/get/document/${docId}/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getTransformationRuleTable = async (
		docId: number,
		columnIds: string[]
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(
				this.documentServiceURL,
				`transformation/getrule/${docId}/table/`,
				'/'
			),
			{ columnUUIDs: columnIds as any },
			this.headers
		);
	};

	getFilterRuleTable = async (
		docId: number,
		tableUUID: string
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`document/${docId}/table-filter-rule/${tableUUID}/`,
				'/'
			)
		);
	};

	getAllSiblings = async (docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, `document/${docId}/siblingsInfo/`, '/'),
			{},
			this.headers
		);
	};

	reClassifyDocuments = async (
		parentDocId: number,
		data: any,
		traceId: string
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(
				this.documentServiceURL,
				`document/${parentDocId}/reClassify/`,
				'/'
			),
			data,
			this.getHeaders(traceId)
		);
	};

	reClassifyDocumentsInitial = async (
		parentDocId: number,
		data: any,
		traceId: string
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(
				this.documentServiceURL,
				`document/${parentDocId}/reClassify/?initial=true`
			),
			data,
			this.getHeaders(traceId)
		);
	};

	changeToStaticText = async (data: any, traceId: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, `digitizer/changetostatictext/`, '/'),
			data,
			this.getHeaders(traceId)
		);
	};

	getDocumentChildrens = async (docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, `document/${docId}/children`, '/'),
			{},
			this.headers
		);
	};

	getAppInfo = async (): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`classification/appinfo/get_application_info_for_current_app`,
				'/'
			),
			{},
			this.headers
		);
	};

	setClassificationMode = async (mode: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(
				this.documentServiceURL,
				`classification/appinfo/change_app_classification_mode`,
				'/'
			),
			{ classification_mode: mode }
		);
	};

	deleteDocument = async (docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'DELETE',
			urlJoin(this.documentServiceURL, `document/${docId}`, '/'),
			{},
			this.headers
		);
	};

	deleteDocType = async (docTypeId: string | number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'DELETE',
			urlJoin(this.documentServiceURL, `delete/doctype/id/${docTypeId}`, '/'),
			{},
			this.headers
		);
	};

	deleteFormat = async (formatId: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'DELETE',
			urlJoin(this.documentServiceURL, `format/${formatId}`, '/'),
			{},
			this.headers
		);
	};

	getFormat = async (formatId: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, `format/${formatId}`, '/'),
			{},
			this.headers
		);
	};

	updateFormat = async (
		formatId: string,
		label: string,
		description: string,
		thumbnail: any = null,
		isStructuredDoc: boolean
	): Promise<any> => {
		const payload: Record<string, any> = {
			label: label,
			description: description,
			isStructured: isStructuredDoc,
		};
		if (thumbnail) {
			payload.thumbnail = thumbnail;
		}
		return editorService.call(
			'editors-with-appInstance-headers',
			'PATCH',
			urlJoin(this.documentServiceURL, `format/${formatId}`, '/'),
			payload,
			this.headers
		);
	};

	getAllDocTypeFormats = async (docTypeName: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`/doctype/${docTypeName}/format/all/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getAllDocTypeFormatsById = async (docTypeId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`/doctype/id/${docTypeId}/format/all/`,
				'/'
			),
			{},
			this.headers
		);
	};

	addSchema = async (uuid: string, description?: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'schema/addoreditwithid/', '/'),
			{ uuid: uuid, extra: { description: description } as any },
			this.headers
		);
	};

	deleteField = async (docId: number, fieldUUID: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(
				this.documentServiceURL,
				`document/${docId}/field/${fieldUUID}/clear/`,
				'/'
			),
			{},
			this.headers
		);
	};

	deleteTable = async (docId: number, tableUUID: string): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(
				this.documentServiceURL,
				`document/${docId}/table/${tableUUID}/clear/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getParentDocExecutionStatus = async (
		parentDocId: number,
		currentPageNumber: number
	): Promise<any> => {
		const data: any = {
			parentDocumentId: parentDocId,
			pageNumber: currentPageNumber,
			includeSelectDocTypeStatus: true,
		};
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'document/parent/executionstatus/', '/'),
			data,
			this.headers
		);
	};

	getDebugLogs = async (
		docId: number,
		showCompletLogs: boolean
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(this.documentServiceURL, `document/${docId}/logs/`, '/') +
				`?advanced=${showCompletLogs}`,
			{},
			this.headers
		);
	};

	getExecutionHistory = async (
		status?: string,
		paginationDetails?: { startIndex: number; endIndex: number },
		filterQuery?: any[]
	): Promise<any> => {
		const queryData: any = {
			filter: {
				operation: 'AND',
				data: [
					{
						parameter: 'singleDoc',
						condition: 'IS',
						value: true,
					},
					{
						parameter: 'apex_document.is_domain_model',
						condition: 'IS',
						value: false,
					},
					...filterQuery,
				],
			},
			expectedResults: [
				{
					format: 'list',
					data: [
						{
							data: 'id',
						},
						{
							data: 'documentData.fileName',
						},
						{
							data: 'documentData.executionState',
						},
						{
							data: 'status',
						},
						{
							data: 'doctype',
						},
						{
							data: 'documentData.format.label',
						},
						{
							data: 'domainAppInfo.domainName',
						},
						{
							data: 'createdDate',
						},
						{
							data: 'lastUpdatedDate',
						},
					],
					paginationDetails: paginationDetails
						? paginationDetails
						: {
								startIndex: 0,
								endIndex: 20,
						  },
					sortOrder: {
						mode: 'DESCENDING',
						data: 'lastUpdatedDate',
					},
					includePaginationDetails: true,
				},
			],
		};
		if (status === 'completed') {
			queryData.filter.data.push({
				parameter: 'status',
				condition: 'IN',
				value: STATUS_MAP.COMPLETED.statusNameList,
			});
		} else if (status === 'error') {
			queryData.filter.data.push({
				parameter: 'status',
				condition: 'IN',
				value: STATUS_MAP.ERROR.statusNameList,
			});
		} else if (status === 'inProgress') {
			queryData.filter.data.push({
				parameter: 'status',
				condition: 'NOT IN',
				value: STATUS_MAP.COMPLETED.statusNameList.concat(
					STATUS_MAP.ERROR.statusNameList
				),
			});
		}

		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, 'document/search/', '/'),
			queryData,
			this.headers
		);
	};

	getDocumentBOData = async (
		docTypeId: number,
		boInstanceId: string
	): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`doctype/${docTypeId}/getBORecord/${boInstanceId}/`,
				'/'
			),
			{},
			this.headers
		);
	};

	getDocumentFiles = async (docId: number, pageNo: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'POST',
			urlJoin(this.documentServiceURL, `document/${docId}/files/`, '/'),
			[
				{
					type: 'image',
					page_no: pageNo,
				},
				{
					type: 'docjson',
					page_no: pageNo,
				},
			] as any,
			this.headers
		);
	};

	getExtractableObjects = async (docId: number): Promise<any> => {
		return editorService.call(
			'editors-with-appInstance-headers',
			'GET',
			urlJoin(
				this.documentServiceURL,
				`document/${docId}/extractable-objects/`,
				'/'
			),
			{},
			this.headers
		);
	};
}

export default new DocumentServiceClass();

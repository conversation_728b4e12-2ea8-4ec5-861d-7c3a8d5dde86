/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the Table component
import React, {
	FC,
	useRef,
	useCallback,
	useEffect,
	useState,
	useMemo,
} from 'react';
import { configurable, useEditContext } from '@base/config-provider';
import { setMetaData, useCoreState } from '@base/utils-hooks';
import { defaultProps, metadata } from './metadata';
import {
	BulkAction,
	FilterOptionType,
	GridActionPositionType,
	GridRefType,
	ITableProps,
	TablePreference,
} from './types';
import { useTableStyles } from './style';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { InfiniteRowModelModule } from '@ag-grid-community/infinite-row-model';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
// Theme
import {
	CellClickedEvent,
	CellValueChangedEvent,
	ColDef,
	FilterModel,
	GridApi,
	GridReadyEvent,
	IGetRowsParams,
	IServerSideGetRowsParams,
	ModuleRegistry,
	RowDragEndEvent,
	RowNode,
} from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';
// React Grid Logic
import '@ag-grid-community/styles/ag-grid.css';
// Core CSS
import '@ag-grid-community/styles/ag-theme-quartz.css';
import {
	convertToAgGridColDefs,
	getLabelStyles,
	normalizeData,
	searchInGrid,
	getPreferenceData,
	sortData,
	getUserIdAndAppId,
	patchPreferenceDataLocally,
	getNestedValue,
	createUniqueId,
} from './helpers';
import Box from '@atomic/box';
import {
	DEFAULT_EXCEL_EXPORT_PARAMS,
	EXCEL_STYLES,
	COMPONENT_NAME,
	DATATABLE_COLUMN_REORDER,
	DATATABLE_COLUMN_RESIZE,
	TABLE_STYLE_PROPS_LIST,
} from './constants';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { frameworkComponets } from './CellRenderer/CellRendererMapper';
import Text from '@atomic/text';
import { BulkActionIntractor } from './BulkActionInteractor';
import { ApexTheme } from '@base/theme';
import { useTheme } from 'react-jss';
import { ExportTo } from './ExportTo/ExportTo';
import pdfMake from 'pdfmake';
import _debounce from 'lodash/debounce';
import { NoDataFound } from './NoDataFound';
import { SearchField } from './Search/Search';
import { PDF_MAKE_FONT_CONFIGURATIONS } from './helpers/pdfMakeFontConfig';
import { getActionCellConfigurations } from './helpers/actionCellConfigurations';
import {
	getServerDataConfig,
	PAGINATION_SIZE_SELECTOR,
} from './helpers/paginationCofigGenerator';
import { StatusBarModule } from '@ag-grid-enterprise/status-bar';
import { RangeSelectionModule } from '@ag-grid-enterprise/range-selection';
import { AdvancedFilterModule } from '@ag-grid-enterprise/advanced-filter';
import { ColumnSelector } from './ColumnSelector';
import { AddNewRow } from './AddNewRow';
import CustomLoader from '@atomic/custom-loader';
import CustomTooltip from './CustomTooltip/CustomTooltip';
import { ColumnFilter } from './ColumnFilter/ColumnFilter';
import {
	useGetPreferenceData,
	usePutPreferenceData,
	usePostPreferenceData,
} from './hooks/usePreferenceData';
import { UseMutationResult } from 'react-query';

ModuleRegistry.registerModules([
	ClientSideRowModelModule,
	InfiniteRowModelModule as any,
	ServerSideRowModelModule as any,
	ColumnsToolPanelModule,
	MenuModule,
	SetFilterModule,
	ExcelExportModule,
	StatusBarModule,
	RangeSelectionModule,
	AdvancedFilterModule,
]);
// eslint-disable-next-line @typescript-eslint/no-empty-interface
// First, create a custom status bar component

const Table: FC<ITableProps> = React.memo((props: ITableProps) => {
	// Framework generated code, below line registers the component
	// NOT TO BE DELETED
	setMetaData('Table', metadata as any);

	// Framework generated code, below line provides the contextProvider instance
	const { mode } = useEditContext();
	const { applicationId, currentUserId } = getUserIdAndAppId();
	const coreState = useCoreState();

	/**
	 * Table props
	 */
	const {
		id,
		dataTableStyles,
		gridActionPosition,
		columnData,
		pinnedColumnPosition,
		gridActions,
		isSortingEnabled,
		loadDataSource,
		totalRecordsCount,
		enableInfinteScrolling,
		onPaginationHandle,
		onRowClick,
		onCellValueChange,
		onRowDragEnd,
		enableFilter,
		bulkActions,
		enableBulkSelect,
		allowSingleRowSelection = false,
		showLabel,
		label,
		showHeader,
		fileNamingPreference,
		exportFileNameToSave,
		appName,
		dateFormat,
		currency,
		timeFormat,
		fieldBindings,
		allowServersideFilter,
		enableGlobalSearch,
		isExportEnabled,
		canAddNewRow,
		onAddNewRow,
		allowColumnResizing,
		filterOption = null,
		alternateRowHighlight,
		backgroundColor,
		padding,
		tableHeight,
		formStylesEnabled,
		onRowSelectionChange,
		onBulkSelectChange,
		enableAdvancedFilter,
		enableColumnSelection,
		enableColumnFilter = false,
		tableContarinerBoxStyles,
		exportCSVFileCallback,
		dynamicState,
		globalVariables,
		onClick,
		isEditable = false,
		isVerticalKeyValueGrid = false,
		showFooter = true,
		isServerSideSortingEnabled = false,
		defaultSelectedRows,
		enableSelectAll,
		onSelectAllChange,
		rowData,
		enableServerSideRender = true,
		enablePagination = true,
		errorFieldName = '',
		forceReloadPaginationHandle = false,
		uploadDetails,
		isServerSideDownload,
		isGeneratedApp,
		treeData,
		autoGroupColumnDef,
		groupDefaultExpanded,
		actionHeaderName,
		isServerSideGroup,
		getServerSideGroupKey,
		isServerSideGroupOpenByDefault,
		canAddNewColumn,
		allowRowDrag = false,
		suppressMovableColumns = false,
		baseUrl,
		hiddenColumns,
		groupingOption,
		...restProps
	} = props;
	/**
	 * Configuring font settings in the pdfMake library to ensure proper PDF generation.
	 */
	useEffect(() => {
		pdfMake.fonts = PDF_MAKE_FONT_CONFIGURATIONS;
	}, []);

	// Use a deep memoization hook to compute and memoize table style properties and column definitions.
	// The memoization will re-run whenever the 'props' change.

	const tableStyleProps = useMemo(() => {
		// Build tableStyleProps by reducing TABLE_STYLE_PROPS_LIST,
		// picking properties from 'props' if available.
		const tableCssProps = TABLE_STYLE_PROPS_LIST.reduce(
			(prevValue, currentValue) => {
				return restProps[currentValue]
					? { ...prevValue, [currentValue]: restProps[currentValue] }
					: prevValue; // If prop not present, return unchanged accumulator
			},
			{}
		);
		return tableCssProps;
	}, [restProps]); // Dependencies: Re-run memoization when 'props' changes

	// Create a default column definition for a table, including styles and tooltip configuration.
	// The column is set to be resizable and utilizes specific header and cell styles from the defined table styles.
	const tableClasses = useTableStyles(tableStyleProps);
	/** */
	// get preference data using API
	const getPreferenceDataHook = useGetPreferenceData(
		applicationId,
		id,
		currentUserId,
		enableColumnFilter,
		baseUrl
	);

	const postPreferenceDataHook = usePostPreferenceData(
		currentUserId,
		getPreferenceDataHook,
		baseUrl
	);

	const putPreferenceDataHook: UseMutationResult = usePutPreferenceData(
		applicationId,
		id,
		currentUserId,
		getPreferenceDataHook,
		postPreferenceDataHook,
		baseUrl
	);

	const rowClassRules = {
		[tableClasses.rowError]: (params) => {
			return (
				params.data &&
				params.data[errorFieldName] &&
				typeof params.data[errorFieldName] === 'object' &&
				Object.keys(params.data[errorFieldName]).length > 0
			);
		}, // Apply error style only to rows with errors
	};
	const onPaginationChangeCallback = useCallback(
		async (config, request) => onPaginationHandle?.(config, request),
		[
			JSON.stringify(dynamicState),
			JSON.stringify(globalVariables),
			forceReloadPaginationHandle,
		]
	);
	//
	const onLoadDataSrouceMemoizedCallback = useCallback(
		async (config) => loadDataSource?.(config),
		[
			JSON.stringify(dynamicState),
			JSON.stringify(globalVariables),
			forceReloadPaginationHandle,
		]
	);

	const [actionHeaderNameValue, setActionHeaderNameValue] =
		useState(actionHeaderName);
	/**
	 * Column definitions
	 */
	const columnDefs = useMemo(() => {
		// Prepare action columns separately (specific to grid actions)
		const actionColumns = getActionCellConfigurations(
			gridActions,
			gridActionPosition,
			actionHeaderNameValue,
			{
				isSortingEnabled: false,
				enableMenu: false,
				isEditMode: mode,
				datatableId: id,
				showFilterInColumn: false,
				rightAlignStyle: false,
				canAddNewColumn,
				groupingOption: [],
				hiddenColumns: [],
				enableActions:
					gridActions?.length > 0 &&
					gridActionPosition === GridActionPositionType.Left,
				lastColumnIndex: columnData.length - 1,
				currentColumnIndex:
					gridActionPosition === 'left' ? 0 : columnData.length - 1,
				showMicroToolbar: true,
				setActionHeaderNameValue,
			}
		) as unknown as ColDef[];

		// Convert provided column data to AG Grid column definitions
		const columns = convertToAgGridColDefs(
			columnData,
			{
				pinnedColumnPosition: Number(pinnedColumnPosition),
				isSortingEnabled,
				dateFormat,
				currency,
				timeFormat,
				fieldBindings,
				allowServersideFilter,
				allowColumnResizing,
				enableFilter: filterOption
					? filterOption === FilterOptionType['Filter only']
					: enableFilter,
				tableClasses,
				enableAdvancedFilter,
				isEditable,
				isVerticalKeyValueGrid,
				errorFieldName,
				uploadDetails,
				mode,
				id,
				canAddNewColumn,
				enableActions:
					gridActions?.length > 0 &&
					gridActionPosition === GridActionPositionType.Left,
				groupingOption,
				hiddenColumns,
			} // Ensure pinnedColumnPosition is treated as a number
		);

		// Merge action columns based on gridActionPosition
		const columnDefs =
			gridActionPosition === 'left'
				? [...actionColumns, ...columns] // Action columns on the left
				: [...columns, ...actionColumns]; // Action columns on the right
		return columnDefs;
	}, [
		allowColumnResizing,
		allowServersideFilter,
		columnData,
		currency,
		dateFormat,
		enableFilter,
		fieldBindings,
		filterOption,
		isSortingEnabled,
		pinnedColumnPosition,
		timeFormat,
		tableClasses,
		enableAdvancedFilter,
		isEditable,
		isVerticalKeyValueGrid,
		errorFieldName,
		uploadDetails,
		mode,
		id,
		canAddNewColumn,
		gridActions,
		gridActionPosition,
		actionHeaderNameValue,
		groupingOption,
		hiddenColumns,
	]);

	const defaultColDef: ColDef = {
		tooltipComponentParams: { errorFieldName, isVerticalKeyValueGrid },
		tooltipComponent: CustomTooltip,
	};
	/**
	 * Grid references
	 */
	const gridRef = useRef<GridRefType>(null);
	const gridApiRef = useRef<GridApi>(null);
	const gridParentRef = useRef(null);
	const pageSizeRef = useRef(20); // Store page size in a ref
	const isManualChange = useRef(false);
	const totalRowCountRef = useRef(0);
	const filterModelRef = useRef<FilterModel>(null);
	/**
	 * State variables
	 */
	const [query, setQuery] = useState('');
	const [selectedRows, setSelectedRows] = useState([]);
	const [parentWidth, setParentWidth] = useState(0);
	const [gridPreferenceData, setGridPreferenceData] =
		useState<TablePreference>(null);
	const [initialPreference, setInitialPreference] =
		useState<TablePreference>(null);

	// // Infinite row model data source configuration
	/**
	 * Data source configuration
	 */
	const dataSource = useMemo(
		() => ({
			getRows: async (params: IGetRowsParams) => {
				const gridApi = gridApiRef.current;
				// Show the loading overlay
				if (gridApi) {
					try {
						const { startRow } = params;
						gridApi.hideOverlay();
						if (params.startRow === 0) {
							// Show loading overlay while fetching data
							gridApi.showLoadingOverlay();
						}
						const config = getServerDataConfig(
							params,
							setFilterModel,
							filterModelRef.current
						);
						if (!isServerSideSortingEnabled) {
							delete config.sort;
						}

						//gridApi.showLoadingOverlay();
						const response = await onLoadDataSrouceMemoizedCallback?.(config);
						const { apiData, headers } = response;
						const normalizedResponse = normalizeData(apiData, columnData);
						let filteredData = searchInGrid(query, normalizedResponse);
						if (!isServerSideSortingEnabled) {
							filteredData = sortData(filteredData, params.sortModel);
						}
						(gridApi as any).__errorOccurred = false;
						if (!filteredData.length) {
							params.successCallback([], 0);
							gridApi.showNoRowsOverlay(); // Show the "No data found" message
						} else {
							if (headers['x-jiffy-total-count']) {
								totalRowCountRef.current = Number(
									headers['x-jiffy-total-count']
								);
							}
							const totalCount = headers['x-jiffy-total-count']
								? Number(headers['x-jiffy-total-count'])
								: totalRowCountRef.current;
							params.successCallback(
								filteredData,
								query ? filteredData.length : totalCount ?? -1
							);
							gridApi.hideOverlay(); // Hide the overlay if there is data
						}
					} catch (error) {
						(gridApi as any).__errorOccurred = true;
						params.successCallback([], 0);
						gridApi.showNoRowsOverlay(); // Show error message
						console.log('error', error);
					} finally {
						//gridApi.hideOverlay();
					}
				}
			},
		}),
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[query, onLoadDataSrouceMemoizedCallback]
	);
	/**
	 * Server data source configuration
	 */
	const serverDataSource = useMemo(
		() => ({
			getRows: async (params: IServerSideGetRowsParams) => {
				const gridApi = gridApiRef.current;

				if (gridApi) {
					const config = getServerDataConfig(
						params.request,
						setFilterModel,
						filterModelRef.current
					);
					if (!isServerSideSortingEnabled) {
						delete config.sort;
					}

					// Show loading overlay while fetching data
					gridApi.showLoadingOverlay();

					try {
						const { startRow, endRow } = params.request;
						const paginationConfig = {
							currentPage: gridApi.paginationGetCurrentPage(),
							currentSize: gridApi.paginationGetPageSize(),
						};

						patchPreferenceDataLocally(
							id,
							paginationConfig,
							enableColumnFilter
						);
						setGridPreferenceData((prevGridPreferenceData) => ({
							...prevGridPreferenceData,
							...paginationConfig,
						}));

						const response = await onPaginationChangeCallback?.(
							config,
							params.request
						);
						const { apiData, headers } = response;
						const normalizedResponse = normalizeData(apiData, columnData);
						// Filter the data based on the search query
						let filteredData = searchInGrid(query, normalizedResponse);
						// Client-side sorting logic
						if (!isServerSideSortingEnabled) {
							filteredData = sortData(filteredData, params.request.sortModel);
						}

						(params.api as any).__errorOccurred = false;
						// If no data is found, show the "No Rows" overlay
						if (filteredData.length === 0) {
							params.success({
								rowData: [],
								rowCount: 0,
							});
							gridApi.showNoRowsOverlay(); // Show "No data found"
						} else {
							if (headers['x-jiffy-total-count']) {
								totalRowCountRef.current = Number(
									headers['x-jiffy-total-count']
								);
							}
							const totalCount = headers['x-jiffy-total-count']
								? Number(headers['x-jiffy-total-count'])
								: totalRowCountRef.current;
							// Update row count
							let rowCount = query ? filteredData.length : totalCount ?? -1;
							//when API returns less rows than expected, set the row count, based on the actual row count,
							// so that ag-grid doesn't trigger no more API calls
							const expectedRowCount = endRow - startRow;
							if (filteredData.length < expectedRowCount) {
								rowCount = startRow + filteredData.length;
							}
							// Call success callback after setting the data
							params.success({
								rowData: filteredData,
								rowCount: rowCount,
							});
							if (rowCount === 0) {
								gridApi.showNoRowsOverlay();
							} else {
								gridApi.hideOverlay();
							}
						}
					} catch (error) {
						// In case of error, set the error flag and show the error message
						(params.api as any).__errorOccurred = true;
						params.success({
							rowData: [],
							rowCount: undefined, // or known count
						});
						gridApi.showNoRowsOverlay();
						console.log('error', error);
					}
				}
			},
		}),
		[query, onPaginationChangeCallback, isServerSideSortingEnabled]
	);
	/**
	 * Grid ready callback
	 */
	const onGridReady = useCallback((params: GridReadyEvent) => {
		gridApiRef.current = params.api;
		/**
		 * Pagination configuration
		 */
		sessionStorage.setItem(
			`datatableProperties-pagination-${id}`,
			JSON.stringify({})
		);
		setParentWidth(gridParentRef.current.offsetWidth);
		const checkboxCol = params.api
			.getAllDisplayedColumns()
			.find((col) => col.getColId() === 'ag-Grid-ControlsColumn');

		if (checkboxCol) {
			params.api.setColumnPinned(checkboxCol.getColId(), 'left');
		}

		// Apply column state and filter model from preference data
		const savedGridPreference = getPreferenceData(
			id,
			getPreferenceDataHook.data,
			enableColumnFilter
		);
		const { columnState, filterModel } = savedGridPreference;
		setGridPreferenceData((prevGridPreferenceData) => ({
			...prevGridPreferenceData,
			...savedGridPreference,
		}));
		if (columnState) {
			const actionColumn = columnState.find((col) => col.colId === 'action');
			if (actionColumn) {
				actionColumn.pinned = gridActionPosition;
			}
			//So if you define hide: true, but the column tool panel is already open/rendered, it may override your hide: true
			// so we need to check if the column is in the hiddenColumns array and if it is, we need to set the hide to true
			let modifiedColumnStateWithHide = columnState;
			if (hiddenColumns && hiddenColumns.length > 0) {
				modifiedColumnStateWithHide = columnState.map((col) => {
					if (hiddenColumns && hiddenColumns.includes(col.colId)) {
						return { ...col, hide: true };
					}
					return col;
				});
			}
			params.api.applyColumnState({
				state: modifiedColumnStateWithHide,
				applyOrder: true,
			});
		}
		if (filterModel) {
			params.api.setFilterModel(filterModel); // Restore filters
		}
		//set initial preference to compare the changes in the grid to show save button in filter preference
		const initialPreferenceBuildObj = {
			currentPage: params.api.paginationGetCurrentPage(),
			currentSize: params.api.paginationGetPageSize(),
		};
		if (params.api.getColumnState()) {
			initialPreferenceBuildObj['columnState'] = params.api.getColumnState();
		}
		if (params.api.getFilterModel()) {
			initialPreferenceBuildObj['filterModel'] = params.api.getFilterModel();
		}

		setInitialPreference({
			...savedGridPreference,
			...initialPreferenceBuildObj,
		});
	}, []);
	/**
	 * Effect to clear old data when the table is re-rendered
	 */
	useEffect(() => {
		if (gridApiRef.current) {
			gridApiRef.current.setRowData?.([]); // Clears old data
		}
		return () => {
			if (gridApiRef.current) {
				gridApiRef.current.setRowData?.([]); // Clears old data
			}
		};
	}, [enableInfinteScrolling]);

	useEffect(() => {
		setActionHeaderNameValue(actionHeaderName);
	}, [actionHeaderName]);

	const setFilterModel = useCallback((filterModel: FilterModel) => {
		filterModelRef.current = filterModel;
	}, []);

	const handleCellValueChanged = useCallback(
		(event: CellValueChangedEvent<any, any>) => {
			onCellValueChange?.({
				...event.data,
				datatableCell: {
					cellName: event?.colDef?.field,
					cellValue: event.newValue,
					api: gridRef.current.api,
				},
			});
		},
		[onCellValueChange]
	);

	const handleRowDragEnd = useCallback(
		(event: RowDragEndEvent<any, any>) => {
			onRowDragEnd?.({ api: event.api });
		},
		[onRowDragEnd]
	);

	const handleCellClick = useCallback(
		(event: CellClickedEvent<any, any>) => {
			if (event.colDef.field === 'action') {
				// Prevent any further action on click
				event.event.stopPropagation(); // Stops the event from propagating
				return false; // Optionally, return false to prevent default behavior
			}
			onRowClick?.({
				...event.data,
				datatableCell: {
					cellName: event?.colDef?.field,
					cellValue: event.value,
					type: event.colDef.type,
					api: isEditable ? gridRef.current.api : null,
				},
			});
			setTimeout(() => {
				onClick?.(event.data);
			}, 100);
		},
		[onRowClick, onClick, isEditable]
	);
	const enableBulkAction = enableBulkSelect || bulkActions?.length > 0;

	const handleSelectionChange = useCallback(
		(event) => {
			if (event?.api) {
				let selectedRows = event.api.getSelectedRows();

				if (isGeneratedApp && enablePagination) {
					// Special handling for row selection in paginated tables generated by ui-renderer to ensure proper selection state across pages
					// Filter to only include nodes from first page to current page
					const selectedNodes = event.api.getSelectedNodes();
					const currentPage = event.api.paginationGetCurrentPage();
					const pageSize = event.api.paginationGetPageSize();
					const maxIndex = (currentPage + 1) * pageSize; // Include current page
					const filteredNodes = selectedNodes.filter((node) => {
						const rowIndex = node.rowIndex;
						return rowIndex >= 0 && rowIndex < maxIndex; // From first page (0) to current page
					});
					// Get data from filtered nodes
					selectedRows = filteredNodes.map((node) => node.data);
				}

				const selectedIds = selectedRows.map((item) => item.id);
				setSelectedRows(selectedRows);
				onRowSelectionChange?.(selectedIds);
				if (enableBulkAction) {
					onBulkSelectChange?.(selectedIds);
				}
				if (enableSelectAll) {
					const selectionState = event.api.getServerSideSelectionState();
					onSelectAllChange?.(selectionState);
				}
			}
		},
		[
			enableBulkAction,
			onRowSelectionChange,
			onBulkSelectChange,
			onSelectAllChange,
			enableSelectAll,
			enablePagination,
		]
	);

	const handleBulkActionClick = useCallback(
		async (action: BulkAction) => {
			if (gridRef?.current) {
				const selectedItemList = gridRef.current.api.getSelectedRows();

				await action?.onClick(
					selectedItemList,
					selectedItemList?.map((item) => item.id)
				);
				if (enableServerSideRender) {
					gridRef.current.api.refreshServerSide({ purge: true });
				}
				gridRef.current.api.deselectAll();
			}
		},
		[enableServerSideRender, gridRef]
	);
	/**
	 * Search change callback
	 */
	const onSearchChange = useCallback((value) => {
		setQuery(value);
		// Trigger AG Grid to refresh the data
		gridRef.current.api.onFilterChanged();
	}, []);

	const handleSearch = _debounce((value) => {
		onSearchChange(value);
	}, 500);
	/**
	 * Pagination change callback
	 */
	const handlePaginationChange = () => {
		if (gridApiRef.current) {
			const api = gridApiRef.current;
			// get all selected rows and update the selected rows when page size changes
			const selectedRows = gridApiRef.current.getSelectedRows();
			const selectedIds = selectedRows.map((item) => item.id);
			setSelectedRows(selectedRows);
			enableBulkAction && onBulkSelectChange?.(selectedIds);
			const currentPageSize = api.paginationGetPageSize();

			if (currentPageSize !== pageSizeRef.current && !enableInfinteScrolling) {
				// Page size has changed
				pageSizeRef.current = currentPageSize;
				// Prevent duplicate calls
				isManualChange.current = true;
				// Reset to the first page and trigger API refresh
				api.paginationGoToPage(0);
				if (enableServerSideRender) {
					api.setGridOption('paginationPageSize', currentPageSize); //USR-503: changing page size based on the page size selector
					api.setGridOption('cacheBlockSize', currentPageSize);
					api.refreshServerSide({ purge: true });
				}

				isManualChange.current = false;
			}
		}
	};

	const theme: ApexTheme = useTheme();
	const filterEnabled = filterOption
		? filterOption === FilterOptionType['Filter only']
		: enableFilter;

	const getMainMenuItems = (params) => {
		const defaultMenuItems = params.defaultItems;

		// Filter out "Choose Columns" and "Reset Columns"
		return defaultMenuItems.filter(
			(item) =>
				item !== 'columnChooser' && // "Choose Columns"
				item !== 'resetColumns' // "Reset Columns"
		);
	};

	const persistColumnState = useCallback(() => {
		if (!gridApiRef.current) return;
		const columnState = gridApiRef.current.getColumnState();
		setGridPreferenceData((prevGridPreferenceData) => ({
			...prevGridPreferenceData,
			columnState,
		}));
		patchPreferenceDataLocally(id, { columnState }, enableColumnFilter);
	}, []);

	const onColumnMoved = useCallback((event) => {
		persistColumnState();
		if (mode && event?.source === 'uiColumnMoved') {
			coreState?.activeElement?.set({ id, componentName: COMPONENT_NAME });
			if (event?.finished) {
				const updatingColumn = columnData?.find(
					(column) => column.key === event?.column?.colDef?.field
				);
				if (!updatingColumn?.id) {
					return;
				}
				const targetPosition = event?.toIndex;
				try {
					updatingColumn &&
						coreState.event.set({
							type: DATATABLE_COLUMN_REORDER,
							data: {
								datatableId: id,
								columnId: updatingColumn?.id,
								position: {
									col: targetPosition,
								},
							},
						});
				} catch {
					return;
				}
			}
		}
	}, []);

	const onColumnResized = useCallback((event) => {
		persistColumnState();
		if (mode && event?.source === 'uiColumnResized') {
			coreState?.activeElement?.set({ id, componentName: COMPONENT_NAME });
			if (event?.finished) {
				const updatingColumn = columnData?.find(
					(column) => column.key === event?.column?.colDef?.field
				);
				if (!updatingColumn?.id) {
					return;
				}
				const payload = {
					columnId: updatingColumn?.id,
					width: Math.floor(event?.column?.actualWidth),
				};
				try {
					coreState.event.set({
						type: DATATABLE_COLUMN_RESIZE,
						data: payload,
					});
				} catch {
					return;
				}
			}
		}
	}, []);

	const onFilterChanged = useCallback(() => {
		if (!gridRef.current) return;
		// handling client side row model, no row overlay when filter is applied
		if (!enableServerSideRender) {
			if (gridRef.current.api.getDisplayedRowCount() === 0) {
				gridRef.current.api.showNoRowsOverlay();
			} else {
				gridRef.current.api.hideOverlay();
			}
		}
		const filterModel = gridRef.current.api.getFilterModel();
		setGridPreferenceData((prevGridPreferenceData) => ({
			...prevGridPreferenceData,
			filterModel,
		}));
		patchPreferenceDataLocally(id, { filterModel }, enableColumnFilter);
	}, []);

	const onFirstDataRendered = (params) => {
		if (defaultSelectedRows?.length > 0) {
			const nodesToSelect: RowNode[] = [];
			params.api.forEachNode((node: RowNode) => {
				if (node && defaultSelectedRows.includes(node.id)) {
					nodesToSelect.push(node);
				}
			});
			params.api.setNodesSelected({ nodes: nodesToSelect, newValue: true });
		}
	};
	const rowGroupingProps = useMemo(
		() => ({
			...(treeData ? { treeData: treeData } : {}),
			...(autoGroupColumnDef ? { autoGroupColumnDef: autoGroupColumnDef } : {}),
			...(groupDefaultExpanded
				? { groupDefaultExpanded: groupDefaultExpanded }
				: {}),
			...(isServerSideGroup ? { isServerSideGroup: isServerSideGroup } : {}),
			...(getServerSideGroupKey
				? { getServerSideGroupKey: getServerSideGroupKey }
				: {}),
			...(isServerSideGroupOpenByDefault
				? { isServerSideGroupOpenByDefault: isServerSideGroupOpenByDefault }
				: {}),
		}),
		[
			treeData,
			autoGroupColumnDef,
			groupDefaultExpanded,
			isServerSideGroup,
			getServerSideGroupKey,
			isServerSideGroupOpenByDefault,
		]
	);
	const RectAGMemoziedTable = useMemo(
		() => (
			<div
				className={`ag-theme-quartz ${tableClasses.tableContainer}`}
				style={{
					height: tableContarinerBoxStyles?.height || '400px',
					...dataTableStyles,
				}}
			>
				<AgGridReact
					key={id}
					ref={gridRef as any}
					rowData={rowData}
					onGridReady={onGridReady}
					columnDefs={columnDefs}
					onColumnVisible={persistColumnState}
					onColumnMoved={onColumnMoved}
					onSortChanged={persistColumnState}
					onColumnResized={onColumnResized}
					onFilterChanged={onFilterChanged}
					defaultColDef={defaultColDef}
					excelStyles={EXCEL_STYLES}
					defaultExcelExportParams={DEFAULT_EXCEL_EXPORT_PARAMS}
					scrollbarWidth={12}
					tooltipShowDelay={0}
					animateRows={true}
					blockLoadDebounceMillis={500}
					getRowId={(params) => params.data.id}
					onCellClicked={handleCellClick}
					onCellValueChanged={handleCellValueChanged}
					components={frameworkComponets}
					headerHeight={50}
					rowHeight={48}
					quickFilterText={query}
					onSelectionChanged={handleSelectionChange}
					onPaginationChanged={handlePaginationChange}
					suppressContextMenu={true}
					noRowsOverlayComponent={NoDataFound}
					paginationPageSizeSelector={PAGINATION_SIZE_SELECTOR}
					loadingOverlayComponent={CustomLoader}
					rowClassRules={rowClassRules}
					//enableAdvancedFilter={enableAdvancedFilter && filterEnabled}
					suppressCellFocus={!isEditable} // changing this to allow cell focus, otherwise the cell edit wont work on blur
					suppressPaginationPanel={!showFooter}
					singleClickEdit={true}
					enableCellTextSelection={true}
					getMainMenuItems={getMainMenuItems}
					onFirstDataRendered={onFirstDataRendered}
					groupDisplayType={'multipleColumns'}
					pagination={enablePagination}
					rowDragManaged={allowRowDrag}
					onRowDragEnd={handleRowDragEnd}
					suppressMovableColumns={suppressMovableColumns}
					// paginationPageSize={20}
					{...(enableColumnSelection && {
						sideBar: {
							//hiddenByDefault: true,
							toolPanels: [
								{
									id: 'columns',
									labelDefault: 'Columns',
									labelKey: 'columns',
									iconKey: 'columns',
									toolPanel: 'agColumnsToolPanel',
									toolPanelParams: {
										suppressRowGroups: true, // Removes row grouping drag-and-drop
										suppressValues: true, // Removes values section
										suppressPivots: true, // Removes pivot section
										suppressPivotMode: true, // Hides pivot mode toggle
									},
								},
							],
							defaultToolPanel: null,
							hiddenByDefault: true,
						},
					})}
					{...(Boolean(alternateRowHighlight) && {
						getRowStyle: (params: any) => {
							// Check if the row index is even
							if (params.node.rowIndex % 2 === 0) {
								return { backgroundColor: theme.colors.monochrome.bg }; // Highlight even rows
							}
							return null;
						},
					})}
					{...(enableServerSideRender && {
						...(enableInfinteScrolling
							? {
									rowModelType: 'infinite',
									datasource: dataSource,
									pagination: false,
									paginationPageSize: 100,
									cacheBlockSize: 100,
									maxBlocksInCache: 20,
									//infiniteInitialRowCount: 20,
									statusBar: !showFooter
										? {}
										: {
												statusPanels: [
													{ statusPanel: 'agTotalRowCountComponent' },
												],
										  },
							  }
							: {
									rowModelType: 'serverSide',
									pagination: true,
									paginationPageSize: 20,
									serverSideDatasource: serverDataSource,
									cacheBlockSize: 20,
									maxBlocksInCache: 10,
							  }),
					})}
					{...(enableBulkAction
						? {
								rowSelection: {
									mode: allowSingleRowSelection ? 'singleRow' : 'multiRow',
									headerCheckbox: enableSelectAll,
									groupSelects: 'descendants',
									isRowSelectable: (rowNode) => !rowNode?.data?.disabled,
								},
						  }
						: {
								rowSelection: {
									checkboxes: false,
								},
						  })}
					{...rowGroupingProps}
				/>
			</div>
		),
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[
			columnDefs,
			dataSource,
			enableBulkAction,
			enableInfinteScrolling,
			serverDataSource,
			enableAdvancedFilter,
			filterEnabled,
			enableColumnSelection,
			enablePagination,
			enableServerSideRender,
			rowData,
			enableSelectAll,
			enableBulkSelect,
			hiddenColumns,
		]
	);

	const isSearchEnabled = filterOption
		? filterOption === (FilterOptionType['Search only'] as any)
		: (!enableFilter || !allowServersideFilter) && enableGlobalSearch;
	const showDatatableHeaders = showLabel || enableBulkAction || showHeader;

	return (
		<Box
			data-component="composite/table"
			id={id}
			ref={gridParentRef}
			backgroundColor={
				backgroundColor ||
				(formStylesEnabled ? 'transparent' : theme.colors.monochrome.offWhite)
			}
			display="flex"
			flexDirection="column"
			padding={padding || '0px 16px'}
			margin={'16px 0px'}
		>
			{showDatatableHeaders && (
				<Box
					id={'datatable-headers'}
					// className={classes.datatableHeaders}
					justifyContent="space-between"
					gap="8px"
					marginBottom="16px"
					display="flex"
					alignItems="center"
					minHeight={'40px'}
				>
					{showLabel && (
						<Text
							className={tableClasses.labelText}
							inlineTargetId={id}
							{...getLabelStyles(props as any)}
						>
							{label}
						</Text>
					)}
					{enableBulkAction && selectedRows.length > 0 && (
						<BulkActionIntractor
							bulkActions={bulkActions}
							selectedRowCount={selectedRows?.length}
							gridRef={gridRef}
							handleBulkActionClick={handleBulkActionClick}
							theme={theme}
							parentWidth={parentWidth}
						/>
					)}
					{!selectedRows.length && showHeader && (
						<Box marginLeft={'auto'}>
							{canAddNewRow && (
								<AddNewRow gridRef={gridRef} onAddNewRow={onAddNewRow} />
							)}
							{isSearchEnabled && (
								<SearchField onChange={handleSearch} name={'Search'} />
							)}
							{/* {filterEnabled && <FilterSelector gridRef={gridRef} />} */}
							{enableColumnFilter && (
								<ColumnFilter
									dataTableId={id}
									gridRef={gridRef}
									gridPreferenceData={gridPreferenceData}
									initialPreference={initialPreference}
									savedPreferences={getPreferenceDataHook.data}
									tableClasses={tableClasses}
									putPreferenceDataHook={putPreferenceDataHook}
								/>
							)}
							{enableColumnSelection && <ColumnSelector gridRef={gridRef} />}
							{isExportEnabled && (
								<ExportTo
									theme={theme}
									gridRef={gridRef}
									fileNamingPreference={fileNamingPreference}
									exportFileNameToSave={exportFileNameToSave}
									appName={appName}
									exportCSVFileCallback={exportCSVFileCallback}
									isServerSideDownload={isServerSideDownload}
									isGeneratedApp={isGeneratedApp}
								/>
							)}
						</Box>
					)}
				</Box>
			)}

			{RectAGMemoziedTable}
		</Box>
	);
});

Table.defaultProps = defaultProps;
Table.displayName = 'Table';

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
export default configurable(Table, 'Table');

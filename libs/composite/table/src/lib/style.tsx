/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { ApexTheme, DeviceTypes } from '@base/theme';
import { DEFAULT_CURSOR_STYLE, ITableProps } from './types';

import { createUseStyles } from 'react-jss';
import { getValue } from './helpers';
import { PlacementType } from '@atomic/listview';

export const useTableStyles = createUseStyles<
	'tableContainer' | 'labelText' | 'rowError',
	Partial<ITableProps>
>((theme: ApexTheme) => ({
	'@global': {
		':root': {
			'--ag-font-family': `${theme.fontFamily}, sans-serif !important`,
		},
		'.ag-theme-quartz': {
			fontFamily: `${theme.fontFamily}, sans-serif !important`, // Replace with your desired font
			'--ag-advanced-filter-join-pill-color': `${theme.colors.danger[300]} !important`,
			'--ag-advanced-filter-column-pill-color': theme.colors.green[300],
			'--ag-advanced-filter-option-pill-color': theme.colors.yellow[300],
			'--ag-advanced-filter-value-pill-color': theme.colors.blue[300],
		},
		'.ag-header-cell-menu-button': {
			opacity: 0,
			visibility: 'hidden',
			transition: 'opacity 0.3s ease, visibility 0s 0.3s',
		},
		'.ag-header-icon': {
			opacity: 0,
			visibility: 'hidden',
			transition: 'opacity 0.3s ease, visibility 0s 0.3s',
			marginLeft: 'var(--ag-grid-size)',
		},
		// Show icons when hovering over the header cell
		'.ag-header-cell:hover .ag-header-cell-menu-button': {
			opacity: 1,
			visibility: 'visible',
			transition: 'opacity 0.3s ease, visibility 0s 0s',
		},
		'.ag-header-cell:hover .ag-header-icon': {
			opacity: 1,
			visibility: 'visible',
			transition: 'opacity 0.3s ease, visibility 0s 0s',
		},
		// Keep icons visible when filter is applied
		'.ag-header-cell .ag-header-cell-filtered .ag-header-cell-menu-button': {
			opacity: 1,
			visibility: 'visible !important',
			transition: 'none',
		},
		// Keep icons visible when filter is applied
		'.ag-header-cell .ag-filter-active .ag-header-cell-menu-button': {
			opacity: 1,
			visibility: 'visible !important',
			transition: 'none',
		},
		'.ag-header-cell .ag-header-cell-filtered .ag-header-icon': {
			opacity: 1,
			visibility: 'visible !important',
			transition: 'none',
		},
		// Ensure icons remain visible when the menu is focused or active
		'.ag-header-cell:focus-within .ag-header-cell-menu-button': {
			opacity: 1,
			visibility: 'visible',
			transition: 'opacity 0.3s ease, visibility 0s 0s',
		},
		'.ag-header-cell:focus-within .ag-header-icon': {
			opacity: 1,
			visibility: 'visible',
			transition: 'opacity 0.3s ease, visibility 0s 0s',
		},
		'.ag-cell-expandable': {
			alignItems: 'center !important',
		},
	},
	tableContainer: ({
		headerBackgroundColor,
		headerHoverColor,
		headerFontColor,
		headerFontHoverColor,
		headerBorderStyle,
		headerBorderColor,
		hederBorderWidth,
		headerBorderRadius,
		headerTitleCase,
		borderColor,
		borderStyle,
		borderWidth,
		innerBorderRadius,
		rowBackgroundColor,
		rowHoverColor,
		isMinimal,
		cursorStyle,
	}) => ({
		'&.ag-theme-quartz': {
			fontFamily: `${theme.fontFamily} !important`,
			fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
			'--ag-font-family': `${theme.fontFamily}, sans-serif !important`,
			'--ag-active-color': `${theme.colors.primary.default} !important`, // Your custom active color
			'--ag-header-column-resize-handle-height': '100%',
			'--ag-row-hover-color': `${
				rowHoverColor?.length
					? rowHoverColor
					: isMinimal
					? theme.colors.monochrome.white
					: theme.colors.monochrome.bg
			} !important`,
			'.ag-header-cell-menu-button': {
				opacity: 0,
				visibility: 'hidden',
				transition: 'opacity 0.3s ease, visibility 0s 0.3s',
			},
			'.ag-header-icon': {
				opacity: 0,
				visibility: 'hidden',
				transition: 'opacity 0.3s ease, visibility 0s 0.3s',
				marginLeft: 'var(--ag-grid-size)',
			},
			'.ag-header-cell:hover .ag-header-cell-menu-button': {
				opacity: 1,
				visibility: 'visible',
				transition: 'opacity 0.3s ease, visibility 0s 0s',
			},
			'.ag-header-cell:hover .ag-header-icon': {
				opacity: 1,
				visibility: 'visible',
				transition: 'opacity 0.3s ease, visibility 0s 0s',
			},
		},

		/* WebKit-based browsers (Chrome, Safari) */
		'&.ag-theme-quartz .ag-body-vertical-scroll-viewport::-webkit-scrollbar, .ag-theme-quartz .ag-body-horizontal-scroll-viewport::-webkit-scrollbar':
			{
				width: '10px !important', // Vertical scrollbar width
				height: '10px !important', // Horizontal scrollbar height
			},
		'&.ag-theme-quartz .ag-body-vertical-scroll-viewport::-webkit-scrollbar-thumb, .ag-theme-quartz .ag-body-horizontal-scroll-viewport::-webkit-scrollbar-thumb':
			{
				backgroundColor: 'rgba(100, 100, 100, 0.5) !important',
				borderRadius: '10px !important', // Border radius for the thumb
			},
		'&.ag-theme-quartz .ag-body-vertical-scroll-viewport::-webkit-scrollbar-thumb:hover, .ag-theme-quartz .ag-body-horizontal-scroll-viewport::-webkit-scrollbar-thumb:hover':
			{
				backgroundColor: 'rgba(100, 100, 100, 0.8) !important',
			},
		'&.ag-theme-quartz .ag-body-vertical-scroll-viewport::-webkit-scrollbar-track, .ag-theme-quartz .ag-body-horizontal-scroll-viewport::-webkit-scrollbar-track':
			{
				backgroundColor: 'rgba(200, 200, 200, 0.2) !important',
			},
		// '--ag-checkbox-background-color': theme.colors.monochrome.offWhite,
		// '--ag-checkbox-checked-color': theme.colors.primary.default,
		// '--ag-checkbox-unchecked-color': theme.colors.monochrome.line,
		// '--ag-checkbox-indeterminate-color': 'red',
		'& .ag-header-cell': {
			paddingRight: '0px !important',
			backgroundColor: `${
				getValue(headerBackgroundColor, theme) ?? theme.colors.primary[100]
			} !important`,
			color: getValue(headerFontColor, theme) ?? theme.colors.monochrome.ash,
			borderRadius: headerBorderRadius || '0',
			...(headerBorderColor &&
				hederBorderWidth && {
					borderWidth: hederBorderWidth,
					borderColor: getValue(headerBorderColor, theme),
					borderStyle: headerBorderStyle || 'solid',
				}),
			letterSpacing: theme.letterSpacings[DeviceTypes.Desktop].bMedium,
			fontWeight: theme.fontWeights.bold,
			textTransform: headerTitleCase || 'none',
			'&:hover': {
				background: `${
					getValue(headerHoverColor, theme) || theme.colors.monochrome.offWhite
				} !important`,
				color:
					getValue(headerFontHoverColor, theme) ?? theme.colors.monochrome.body,
				'--ag-header-column-resize-handle-width': '4px',
				'& .ag-header-cell-resize': {
					'&:after': {
						backgroundColor: theme.colors.primary.default,
					},

					//	width:15
				},
			},
		},

		'& .ag-root-wrapper': {
			borderColor,
			borderStyle,
			borderWidth,
			borderRadius: innerBorderRadius || '0',
		},

		'& .ag-checkbox-input-wrapper': {
			fontSize: 22,
			height: 20,
			width: 20,
			borderRadius: 4,
			backgroundColor: `${theme.colors.monochrome.offWhite} !important`,
			boxShadow: 'none !important',
		},

		'& .ag-cell': {
			backgroundColor: rowBackgroundColor?.length ? rowBackgroundColor : 'none',
			'&:hover': {
				backgroundColor: rowHoverColor?.length
					? getValue(rowHoverColor, theme)
					: isMinimal
					? theme.colors.monochrome.white
					: theme.colors.monochrome.bg,
				color: theme.colors.monochrome.body,
				cursor: cursorStyle?.length ? cursorStyle : DEFAULT_CURSOR_STYLE,
			},
		},
		'& .ag-row': {
			backgroundColor: rowBackgroundColor?.length
				? getValue(rowBackgroundColor, theme)
				: isMinimal
				? theme.colors.monochrome.offWhite
				: theme.colors.monochrome.offWhite,
		},
		'& .ag-row-hover': {
			color: theme.colors.monochrome.body,
			cursor: cursorStyle?.length ? cursorStyle : DEFAULT_CURSOR_STYLE,
		},
		'& .action-cell-class': {
			display: 'flex',
			justifyContent: 'center',
		},
		'& .ag-status-bar': {
			minHeight: '48px',
			alignItems: 'center',
			paddingRight: 'calc(var(--ag-grid-size) * 2)',
		},
		'& .ag-selection-checkbox': {
			marginTop: '3px',
		},
		'.ag-input-field-input': {
			'&:focus': {
				borderColor: 'revert',
				boxShadow: 'none',
			},
		},
		'& .ag-input-field-input': {
			'&:focus': {
				boxShadow: 'none !important',
				borderColor: 'var(--ag-border-color) !important',
			},
		},
		'& .ag-picker-field-wrapper': {
			'&:focus-within': {
				boxShadow: 'none !important',
				borderColor: 'var(--ag-border-color) !important',
			},
		},
		'& .ag-header-cell-resize': {
			width: 4,
		},
		'& .ag-header-cell-filter-button': {
			//visibility: 'hidden !important',
		},
		'& .ag-header-cell-label-right-align': {
			justifyContent: 'end',
			marginRight: 5,
		},
		// Keep icons visible when filter is applied
		'& .ag-header-cell-filtered .ag-header-cell-menu-button': {
			opacity: 1,
			visibility: 'visible !important',
			transition: 'none',
		},
		// Keep icons visible when filter is applied
		'& .ag-filter-active .ag-header-cell-menu-button': {
			opacity: 1,
			visibility: 'visible !important',
			transition: 'none',
		},
	}),
	labelText: (props) => ({
		padding: props?.labelPadding || '0px',
		fontFamily: theme.fontFamily,
		display: ' block',
		width: '100%',
		whiteSpace: 'nowrap',
		overflow: 'hidden',
		textOverflow: 'ellipsis',
		fontWeight: props?.labelBold
			? theme.fontWeights.bold
			: theme.fontWeights.regular,
	}),
	cellError: (props) => ({
		borderRadius: '4px',
		borderRight: `1px solid ${theme.colors.danger[400]} !important`,
		borderColor:
			props?.cellErrorBorderColor || `${theme.colors.danger[400]} !important`,
		backgroundColor:
			props?.cellErrorBackgroundColor ||
			`${theme.colors.danger[100]} !important`,
	}),
	rowError: (props) => ({
		backgroundColor:
			props?.rowErrorBackgroundColor ||
			`${theme.colors.danger[100]} !important`,
	}),
	closeIcon: {
		fontWeight: theme.fontWeights.bold,
	},
}));

export const getComponentStyles = (theme: ApexTheme) => ({
	inputStyles: {
		color: theme.colors.monochrome.body,
		fontSize: theme.fontSizes.desktop.bXSmall,
		cursor: 'pointer !important',
		height: '30px',
		width: '180px',
		textOverflow: 'ellipsis',
		overflow: 'hidden !important',
		paddingRight: '42px !important',
		paddingLeft: '10px !important',
	},
	filterListviewStyles: {
		listviewContainerStyles: {
			width: '160px',
			height: '90px',
			borderRadius: '8px',
			boxShadow: '0px 2px 8px 0px #00000040',
			padding: '8px',
			gap: '10px',
			letterSpacing: '.25px',
		},
		placement: PlacementType.BottomEnd,
		possiblePlacements: [PlacementType.BottomEnd],
		triggerOffset: 2,
		triggerElementStyles: { width: 'fit-content' },
		auto: true,
		showArrow: false,
	},
	saveFilterListStyles: {
		listviewContainerStyles: {
			width: '160px',
			height: '90px',
			borderRadius: '8px',
			boxShadow: '0px 2px 8px 0px #00000040',
			padding: '8px',
			gap: '10px',
			letterSpacing: '.25px',
		},
		placement: PlacementType.BottomEnd,
		possiblePlacements: [PlacementType.BottomEnd],
		triggerOffset: 2,
		triggerElementStyles: { width: 'fit-content' },
		auto: true,
		showArrow: false,
	},
});

import React from 'react';
import NoRecordFound from '../Assets/norecords.png';
import Box from '@atomic/box';
import { textStyles } from './styles';
import Text from '@atomic/text';
import { ApexTheme } from '@base/theme';
import { useTheme } from 'react-jss';
import Image from '@atomic/image';

export const NoDataFound = (props: any) => {
	const theme: ApexTheme = useTheme();
	const apiFailed = props.api.__errorOccurred;
	return (
		<Box flexDirection="column">
			<Image
				src={NoRecordFound}
				width={73}
				height={54}
				alt={'No records found'}
				containerBoxProps={{ alignItems: 'center' }}
			/>
			<Text {...textStyles(theme, apiFailed)}>
				{' '}
				{apiFailed
					? 'Unable to fetch data. Please try again.'
					: 'No records found'}
			</Text>
		</Box>
	);
};

import {
	ColumnVO,
	FilterModel,
	IGetRowsParams,
	IServerSideGetRowsRequest,
} from '@ag-grid-community/core';
import { AG_GRID_FILTER_OPTIONS } from '../constants';
import { isUndefined } from 'lodash';

export const PAGINATION_SIZE_SELECTOR = [10, 20, 50, 100];

const toLocalISOString = (date) => {
	const pad = (n) => n.toString().padStart(2, '0');
	const year = date.getFullYear();
	const month = pad(date.getMonth() + 1); // Months are zero-based
	const day = pad(date.getDate());
	const hours = pad(date.getHours());
	const minutes = pad(date.getMinutes());
	const seconds = pad(date.getSeconds());
	const ms = date.getMilliseconds().toString().padStart(3, '0');
	return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${ms}`;
};

export const convertFilterModel = (filterModel: FilterModel) => {
	let queryParams: string[] = [];

	Object.keys(filterModel).forEach((key) => {
		const { conditions, filterType } = filterModel[key];

		if (conditions && Array.isArray(conditions)) {
			// Handling multiple conditions with an operator
			const conditionStrings = conditions
				.map((condition: any) => {
					const filterValue = AG_GRID_FILTER_OPTIONS[condition.type]; // Access the filter type dynamically from each condition
					if (filterValue) {
						if (filterType === 'date') {
							const formattedDateFrom = new Date(condition.dateFrom)
								.toISOString()
								.split('T')[0];
							return `${key}:${filterValue}=${formattedDateFrom}`;
						} else {
							return `${key}:${filterValue}=${condition.filter}`;
						}
					}

					return '';
				})
				.filter((str: string) => str !== ''); // Ensure we filter out any empty strings

			// Join the conditions with the operator (AND/OR)
			if (conditionStrings.length > 0) {
				queryParams = [...queryParams, ...conditionStrings];
			}
		} else {
			// Handling a single filter (non-array conditions)
			const { type, filter } = filterModel[key];
			const filterValue = AG_GRID_FILTER_OPTIONS[type];
			if (filterValue) {
				if (filterType === 'date') {
					const { dateFrom, dateTo } = filterModel[key];
					const fromDate = toLocalISOString(new Date(dateFrom)).split('T')[0];
					const toDate = dateTo
						? toLocalISOString(new Date(dateTo)).split('T')[0]
						: '';
					queryParams.push(
						`${key}:${filterValue}=${fromDate}${toDate ? ',' + toDate : ''}`
					);
				} else {
					queryParams.push(`${key}:${filterValue}=${filter}`);
				}
			}
		}
	});

	return `&${queryParams.join('&')}`;
};

function convertToNormalFilterModel(advancedFilterModel) {
	const normalFilterModel: any = {};
	if (!isUndefined(advancedFilterModel.conditions)) {
		// If conditions is an array, process normally
		if (Array.isArray(advancedFilterModel.conditions)) {
			advancedFilterModel.conditions.forEach((condition) => {
				if (condition && condition.colId) {
					if (!normalFilterModel[condition.colId]) {
						normalFilterModel[condition.colId] = {
							filterType: condition.filterType, // Default filter type as 'text'
							operator: advancedFilterModel.type || 'AND', // Default operator 'AND'
							conditions: [],
						};
					}

					normalFilterModel[condition.colId]?.conditions.push({
						filterType: condition.filterType, // Default filter type as 'text'
						type: condition.type, // Condition's operator (e.g., 'contains', 'equals')
						filter: condition.filter, // The filter value itself
					});
				}
			});
		} else if (advancedFilterModel.conditions) {
			// If conditions is a single object (no array), handle it separately
			const condition = advancedFilterModel.conditions;

			if (condition && condition.colId) {
				if (!normalFilterModel[condition.colId]) {
					normalFilterModel[condition.colId] = {
						filterType: condition.filterType, // Default filter type as 'text'
						conditions: [],
					};
				}

				normalFilterModel[condition.colId]?.conditions.push({
					filterType: condition.filterType, // Default filter type as 'text'
					type: condition.type, // Condition's operator (e.g., 'contains', 'equals')
					filter: condition.filter, // The filter value itself
				});
			}
		}
	} else {
		normalFilterModel[advancedFilterModel?.colId] = {
			filterType: advancedFilterModel.filterType, // Default filter type as 'text'
			type: advancedFilterModel.type, // Condition's operator (e.g., 'contains', 'equals')
			filter: advancedFilterModel.filter, // The filter value itself
		};
	}

	return normalFilterModel;
}

export const getServerDataConfig = (
	params: IGetRowsParams | IServerSideGetRowsRequest,
	setFilterModel: (filterModel: FilterModel) => void,
	filterModelRef: FilterModel
) => {
	const { startRow, endRow, sortModel, filterModel } = params;
	let queryParams: string;
	let sortKey: string;
	let fetchCount = false;
	if (sortModel.length) {
		const { sort, colId } = sortModel[0];
		sortKey = sort === 'desc' ? `-${colId}` : colId;
	}
	if (filterModel && Object.keys(filterModel).length) {
		queryParams = convertFilterModel(filterModel);
	}
	const pageSizeCal = endRow - startRow;
	const pageSize = pageSizeCal || 20;
	const currentPage = startRow ? Math.floor(startRow / pageSize) : 0;
	if (filterModel && filterModel !== filterModelRef && currentPage === 0) {
		fetchCount = true;
		setFilterModel(filterModel);
	}
	return {
		page: currentPage,
		size: pageSize,
		...(sortKey ? { sort: sortKey } : {}),
		...(queryParams ? { queryParams } : {}),
		...(fetchCount ? { fetchCount } : {}),
	};
};

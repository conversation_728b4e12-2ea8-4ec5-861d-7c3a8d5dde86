export const WorkStationProps = {
	allowPagination: false,
	alternateRowHighlight: false,
	attributeId: 'cqb84jime8pv8race12g',
	boName: 'account',
	canAddNewColumn: true,
	columnData: [
		{
			bold: 'true',
			color: '#285847',
			dataType: 'Singlelinetext',
			displayType: 'text',
			enableHyperlink: 'false',
			frozen: 'true',
			hoverColor: '',
			id: 'cqfkdu5ofvmig6d94qv0',
			key: 'accountNumber',
			label: 'Account Number',
			microEditable: '',
			name: 'Account Number',
			openInSameTab: '',
			showRowGroupingValue: 'false',
			type: 'var',
		},
		{
			dataType: 'Singlelinetext',
			frozen: 'true',
			id: 'cqfkdu5ofvmig6d94qug',
			key: 'name',
			label: 'Account Name',
			microEditable: '',
			name: 'Account Name',
			type: 'var',
			width: '526',
		},
		{
			dataType: 'Singlelinetext',
			id: 'cqfkdu5ofvmig6d94qvg',
			key: 'repCode',
			label: 'Rep Code',
			microEditable: '',
			name: 'Rep Code',
			type: 'var',
			width: '184',
		},
		{
			dataType: 'Singlelinetext',
			id: 'cqfkdu5ofvmig6d94r10',
			key: 'registrationType.name',
			label: 'Registration Type',
			microEditable: '',
			name: 'Registration Type',
			type: 'var',
			width: '324',
		},
		{
			dataType: 'Singlelinetext',
			id: 'cqfkdu5ofvmig6d94r1g',
			key: 'registrationType.code',
			label: 'Registration Code',
			microEditable: '',
			name: 'Registration Code',
			type: 'var',
		},
		{
			dataType: 'Singlelinetext',
			id: 'crpp1jsu2tr4af819pb0',
			key: 'primaryOwner.owner.legalAddress.state.name',
			label: 'State',
			microEditable: 'true',
			type: 'var',
			width: '195',
			name: 'State',
		},
		{
			dataType: 'Singlelinetext',
			id: 'cqtjutvdhkeu2brb8gu0',
			key: 'investmentObjective',
			label: 'Investment Objective',
			microEditable: 'true',
			type: 'var',
			name: 'Investment Objective',
		},
		{
			dataType: 'Singlelinetext',
			id: 'cqtjv27dhkeu2brb8gv0',
			key: 'riskTolerance',
			label: 'Risk Tolerance',
			microEditable: 'true',
			type: 'var',
			name: 'Risk Tolerance',
		},
		{
			dataType: 'Singlelinetext',
			formattingRules: {
				rules: [
					{
						id: '71f7f644-148d-4c92-9c32-2bef988f819f',
						criteria: 'stringIsExactly',
						type: 'Tag',
						value: 'Open',
						value2: '',
						style: {
							primaryColor: '#eaf9de',
							fontColor: '#008a00',
						},
					},
					{
						id: '1b88a5aa-bfd0-4d90-8813-50c19933da9c',
						criteria: 'stringIsExactly',
						type: 'Tag',
						value: 'Restricted',
						value2: '',
						style: {
							primaryColor: '#fff2f1',
							fontColor: '#ab1400',
						},
					},
					{
						id: 'b7801929-7caa-496d-96eb-2791af80af3c',
						criteria: 'stringIsExactly',
						type: 'Tag',
						value: 'Inactive',
						value2: '',
						style: {
							primaryColor: '#fff8e1',
							fontColor: '#fff8e1',
						},
					},
					{
						id: '7df52df4-be62-4472-a432-3d63683d76c7',
						criteria: 'stringIsExactly',
						type: 'Tag',
						value: 'Approved',
						value2: '',
						style: {
							primaryColor: '#f5f5f5',
							fontColor: '#285847',
						},
					},
					{
						id: '915fc7f0-2889-417f-940c-0b8dab986df9',
						criteria: 'stringBeginsWith',
						type: 'Tag',
						value: 'Closed account with no positions',
						value2: '',
						style: {
							primaryColor: '#f4f4f5',
							fontColor: '#535862',
						},
					},
				],
			},
			id: 'cqfkdu5ofvmig6d94r0g',
			key: 'accountStatus',
			label: 'Account Status',
			microEditable: '',
			name: 'Account Status',
			type: 'var',
			width: '189',
		},
		{
			dataType: 'Currency',
			id: 'cqfkdu5ofvmig6d94r20',
			key: 'dailyBalances.endingMarketValue',
			label: 'Account Value',
			microEditable: 'true',
			type: 'var',
			width: '214',
			name: 'Account Value',
		},
		{
			dataType: 'Currency',
			id: 'cqfkdu5ofvmig6d94r2g',
			key: 'dailyBalances.endingCashBalance',
			label: 'Cash Value',
			microEditable: '',
			name: 'Cash Value',
			type: 'var',
			width: '162',
		},
		{
			dataType: 'Currency',
			id: 'cqtjvlndhkeu2brb8h00',
			key: 'dailyBalances.endingMoneyMarketBalance',
			label: 'MMF Balance',
			microEditable: 'true',
			type: 'var',
			width: '189',
			name: 'MMF Balance',
		},
		{
			dataType: 'Currency',
			id: 'cqfkdu5ofvmig6d94r00',
			key: 'dailyBalances.endingMarginBalance',
			label: 'Margin Balance',
			microEditable: 'true',
			type: 'var',
			name: 'Margin Balance',
		},
		{
			dataType: 'Currency',
			id: 'cqfkdu5ofvmig6d94r3g',
			key: 'dailyBalances.maintenanceCall',
			label: 'House Surplus',
			microEditable: '',
			name: 'House Surplus',
			type: 'var',
		},
		{
			dataType: 'Currency',
			id: 'cqfkdu5ofvmig6d94r30',
			key: 'dailyBalances.fedCall',
			label: 'Open Current Fed Call',
			microEditable: '',
			name: 'Open Current Fed Call',
			type: 'var',
		},
	],
	cursorStyle: 'default',
	dataTableStyles: {
		width: '100%',
	},
	enableFilter: true,
	enableInfinteScrolling: true,
	fieldBindedUuids: '',
	filterOption: 'filterOnly',
	formProps: {
		boName: 'account',
		formFields: [
			{
				label: 'Name',
				name: 'name',
				type: 'Singlelinetext',
			},
			{
				label: 'Account Number',
				name: 'accountNumber',
				type: 'Singlelinetext',
			},
			{
				label: 'Rep Code',
				name: 'repCode',
				type: 'Singlelinetext',
			},
			{
				label: 'Margin Balance',
				name: 'marginBalance',
				type: 'Currency',
			},
			{
				label: 'Account Status',
				name: 'accountStatus',
				type: 'Singlelinetext',
			},
			{
				label: 'Name',
				name: 'name',
				type: 'Singlelinetext',
			},
			{
				label: 'Code',
				name: 'code',
				type: 'Singlelinetext',
			},
			{
				label: 'Ending Market Value',
				name: 'endingMarketValue',
				type: 'Currency',
			},
			{
				label: 'Ending Cash Balance',
				name: 'endingCashBalance',
				type: 'Currency',
			},
			{
				label: 'Fed Call',
				name: 'fedCall',
				type: 'Currency',
			},
			{
				label: 'Maintenance Call',
				name: 'maintenanceCall',
				type: 'Currency',
			},
		],
	},
	formStylesEnabled: true,
	gridActionPosition: 'left',
	headerFontColor: '#73767CFF',
	isDeleteable: false,
	isEditable: false,
	isSortingEnabled: true,
	label: 'Accounts List',
	lookupValue: 'id',
	microEditable: 'true',
	name: 'Datatable2',
	nestedFields:
		'Datatable2.id,Datatable2.accountNumber,Datatable2.name,Datatable2.repCode,Datatable2.name,Datatable2.code,Datatable2.,Datatable2.,Datatable2.,Datatable2.accountStatus,Datatable2.,Datatable2.endingCashBalance,Datatable2.,Datatable2.,Datatable2.maintenanceCall,Datatable2.fedCall',
	pinnedColumnPosition: '1',
	rowHoverColor: '#F6FCFF',
	showLabel: false,
	tableHeight: '400px',
	gridActions: [],
	bulkActions: [],
	allowServersideFilter: true,
	appName: 'workstation',
	isServerSideSortingEnabled: true,
	showLoader: false,
	errorMessage: null,
	totalRecordsCount: '200',
	cardWidth: '',
	rowData: [
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 916552.65,
				endingCashBalance: -7030.04,
				endingMarginBalance: 0,
				endingMarketValue: 909522.61,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a45f5c0-7dc0-11ef-8c0f-f7b077c168ba',
				maintenanceCall: 0,
			},
			id: '2f25c8d0-5a1c-11ef-a352-d34e52e3537a',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO FLORIDA ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a90d4556-7a74-11ef-aacf-27182156c35d',
				mailingAddress: null,
				owner: {
					id: 'df33dcc0-7a7a-11ef-af07-376b0e0f5edf',
					legalAddress: {
						id: 'eb41151e-7a7a-11ef-af07-87742c2ec9fa',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 271427.76,
				endingCashBalance: -14668.72,
				endingMarginBalance: 0,
				endingMarketValue: 256758.82,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a4ab07e-7dc0-11ef-8c0f-8ffe372cc074',
				maintenanceCall: 0,
			},
			id: '301f0c06-5a1c-11ef-a352-c7fb17a658b1',
			investmentObjective: null,
			name: 'NEW YORK STATE OFFICE OF THE COMPTROLLER',
			primaryOwner: {
				id: 'a910fe4e-7a74-11ef-aacf-aba0dd9dbf90',
				mailingAddress: null,
				owner: {
					id: 'e1a117ac-7a7a-11ef-af07-17411552990c',
					legalAddress: {
						id: 'eb4b7568-7a7a-11ef-af07-77c7607ac77b',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 268397.02,
				endingCashBalance: -41463.52,
				endingMarginBalance: 0,
				endingMarketValue: 226929.12,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a477274-7dc0-11ef-8c0f-9fdc7680c7fa',
				maintenanceCall: 0,
			},
			id: '2f56af2c-5a1c-11ef-a352-6389ee9b474e',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO DELAWARE ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a90e1c06-7a74-11ef-aacf-374f90ea17a5',
				mailingAddress: null,
				owner: {
					id: 'df2bc27e-7a7a-11ef-af07-fb5336cd121b',
					legalAddress: {
						id: 'eb434bea-7a7a-11ef-af07-a7f3ae6873ef',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 185091.34,
				endingCashBalance: -4015.14,
				endingMarginBalance: 0,
				endingMarketValue: 181075.95,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a4184e0-7dc0-11ef-8c0f-ef5de463c71a',
				maintenanceCall: 0,
			},
			id: '2c75f39e-5a1c-11ef-9f68-c74a44f8714a',
			investmentObjective: null,
			name: 'PENNSYLVANIA UNCLAIMED PROPERT ESCHEATMENT ACCOUNT ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a88383de-7a74-11ef-aacf-d375de4dd14d',
				mailingAddress: null,
				owner: {
					id: 'e104e99a-7a7a-11ef-af07-c32fae5fccc6',
					legalAddress: {
						id: 'eaa9227c-7a7a-11ef-af07-c7e6fb24f920',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 112715.78,
				endingCashBalance: -554.04,
				endingMarginBalance: 0,
				endingMarketValue: 112161.74,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a4512e0-7dc0-11ef-8c0f-7f4526e3f2e1',
				maintenanceCall: 0,
			},
			id: '2ed4906e-5a1c-11ef-a352-8b0b58f4afdf',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO NEVADA ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a90bbfb0-7a74-11ef-aacf-b7f05f48ef8f',
				mailingAddress: null,
				owner: {
					id: 'e1090764-7a7a-11ef-af07-372e21de702c',
					legalAddress: {
						id: 'eb3cb226-7a7a-11ef-af07-5f0ac97edfdd',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 161423.73,
				endingCashBalance: -78489,
				endingMarginBalance: 0,
				endingMarketValue: 82934.73,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: 'cb59bd9a-8570-11ef-b2d6-cb12254c154f',
				maintenanceCall: 0,
			},
			id: '********-5a1c-11ef-a352-e3aef54990fa',
			investmentObjective: null,
			name: 'AXOS NON-MARKETABLE SECURITIES',
			primaryOwner: {
				id: 'a91204a6-7a74-11ef-aacf-cfc1b0df989c',
				mailingAddress: null,
				owner: {
					id: 'e2efaf92-7a7a-11ef-af07-efb24fd03786',
					legalAddress: {
						id: 'eb4e3758-7a7a-11ef-af07-535513f985ee',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO01',
			riskTolerance: 'Aggressive',
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 39122.92,
				endingCashBalance: -824.89,
				endingMarginBalance: 0,
				endingMarketValue: 38298.03,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a480950-7dc0-11ef-8c0f-0304ad5235a2',
				maintenanceCall: 0,
			},
			id: '2f866f32-5a1c-11ef-a352-6f4befc6153a',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO GEORGIA ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a90ed088-7a74-11ef-aacf-ef437d37ea53',
				mailingAddress: null,
				owner: {
					id: 'e19f6588-7a7a-11ef-af07-af253e6bf68a',
					legalAddress: {
						id: 'eb454daa-7a7a-11ef-af07-7f020e3424dc',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 29552.25,
				endingCashBalance: -1014.05,
				endingMarginBalance: 0,
				endingMarketValue: 28531.82,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: 'cb61515e-8570-11ef-b2d6-8764de842da1',
				maintenanceCall: 0,
			},
			id: '2f31778e-5a1c-11ef-a352-2b0dc62529f5',
			investmentObjective: null,
			name: 'STATE OF CALIFORNIA STATE CONTROLLERS OFFICE UNCLAIMED PROPERTY DIVISION',
			primaryOwner: {
				id: 'a90d9542-7a74-11ef-aacf-ef8acdbebc96',
				mailingAddress: null,
				owner: {
					id: 'e2424f14-7a7a-11ef-af07-b32d946e7b95',
					legalAddress: {
						id: 'eb41cb62-7a7a-11ef-af07-b3891587ebb7',
						state: {
							id: '160daa80-6a7e-11ef-bc2a-3f0d070822f7',
							name: 'California',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'Z001',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 26287.11,
				endingCashBalance: -30.04,
				endingMarginBalance: 0,
				endingMarketValue: 26257.07,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a517ef4-7dc0-11ef-8c0f-1332151e69c3',
				maintenanceCall: 0,
			},
			id: '30d70ed2-5a1c-11ef-a352-8ba8a07c2885',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO MINNESOTA ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a98df5ac-7a74-11ef-aacf-836aaaa9c40b',
				mailingAddress: null,
				owner: {
					id: 'e1a2e5fa-7a7a-11ef-af07-5f159e442be3',
					legalAddress: {
						id: 'ebcca5ca-7a7a-11ef-af07-37dbb9b125c8',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 28200.45,
				endingCashBalance: -2185.52,
				endingMarginBalance: 0,
				endingMarketValue: 26014.93,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a48eabe-7dc0-11ef-8c0f-ef9389f3940e',
				maintenanceCall: 0,
			},
			id: '2fd3f5b8-5a1c-11ef-a352-eb2910d062e7',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO HAWAII ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a90fe2fc-7a74-11ef-aacf-43065903f6ad',
				mailingAddress: null,
				owner: {
					id: 'e38d9914-7a7a-11ef-af07-bbd22a2ba385',
					legalAddress: {
						id: 'eb48572a-7a7a-11ef-af07-b3443bb7fffe',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 21775.64,
				endingCashBalance: -25.85,
				endingMarginBalance: 0,
				endingMarketValue: 21749.79,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a4641e2-7dc0-11ef-8c0f-8fae5c662c80',
				maintenanceCall: 0,
			},
			id: '2f2c7d10-5a1c-11ef-a352-3302f1130f2e',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO CONNECTICUT ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a90d8070-7a74-11ef-aacf-571d78aeaaaf',
				mailingAddress: null,
				owner: {
					id: 'ddeac158-7a7a-11ef-af07-6fa1478b93f5',
					legalAddress: {
						id: 'eb41935e-7a7a-11ef-af07-d361c0c109bc',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 19976.16,
				endingCashBalance: -443.57,
				endingMarginBalance: 0,
				endingMarketValue: 19531.56,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a42b4fa-7dc0-11ef-8c0f-07314d3a2061',
				maintenanceCall: 0,
			},
			id: '2d3de746-5a1c-11ef-9f68-8b1b7796b336',
			investmentObjective: null,
			name: 'NEW JERSEY UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a8856208-7a74-11ef-aacf-ef7e7322bce2',
				mailingAddress: null,
				owner: {
					id: 'ddec7c46-7a7a-11ef-af07-379e2b8c83ed',
					legalAddress: {
						id: 'eaae67b4-7a7a-11ef-af07-5fb92a442bb5',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 19303.98,
				endingCashBalance: 0,
				endingMarginBalance: 0,
				endingMarketValue: 19303.98,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a52f9b4-7dc0-11ef-8c0f-23e3053ddc70',
				maintenanceCall: 0,
			},
			id: '2f75e68a-5a1c-11ef-a352-671fc01baf17',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO WYOMING ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a90e9050-7a74-11ef-aacf-e3d6ce785b58',
				mailingAddress: null,
				owner: {
					id: 'e384ffc0-7a7a-11ef-af07-637c9a90b481',
					legalAddress: {
						id: 'eb4497e8-7a7a-11ef-af07-631392be1377',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 209525,
				endingCashBalance: -194195.59,
				endingMarginBalance: 0,
				endingMarketValue: 15329.41,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: 'cb5e8b54-8570-11ef-b2d6-bb9d3311798b',
				maintenanceCall: 0,
			},
			id: '2fa48d50-5a1c-11ef-a352-e3aaa8f72125',
			investmentObjective: null,
			name: 'AXOS CLEARING HOUSE ACCOUNT',
			primaryOwner: {
				id: 'a90f4450-7a74-11ef-aacf-53485071b183',
				mailingAddress: null,
				owner: {
					id: 'dfd42a90-7a7a-11ef-af07-8b9732b29882',
					legalAddress: {
						id: 'eb469494-7a7a-11ef-af07-fb16d020ba39',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO13',
			riskTolerance: 'Aggressive',
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 9097.63,
				endingCashBalance: -302.53,
				endingMarginBalance: 0,
				endingMarketValue: 8795.1,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a4a18d0-7dc0-11ef-8c0f-e72726a00cab',
				maintenanceCall: 0,
			},
			id: '3010f6f2-5a1c-11ef-a352-************',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO COLORADO ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a910c9ba-7a74-11ef-aacf-63fe3ce1945b',
				mailingAddress: null,
				owner: {
					id: 'e38a2c66-7a7a-11ef-af07-1b447ed94eac',
					legalAddress: {
						id: 'eb4adfd6-7a7a-11ef-af07-bb8db0c1fe2e',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 6480.6,
				endingCashBalance: 0,
				endingMarginBalance: 0,
				endingMarketValue: 6480.6,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a53dc26-7dc0-11ef-8c0f-1febdeeed8b5',
				maintenanceCall: 0,
			},
			id: '307679b4-5a1c-11ef-a352-6beb********',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO OKLAHOMA ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a9124d76-7a74-11ef-aacf-d36e9a6b632a',
				mailingAddress: null,
				owner: {
					id: 'ddf092f4-7a7a-11ef-af07-3b1157181ac4',
					legalAddress: {
						id: 'eb4f099e-7a7a-11ef-af07-177a0aa0df52',
						state: {
							id: '160e107e-6a7e-11ef-bc2a-575f9ea98338',
							name: 'Oklahoma',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 5445.6,
				endingCashBalance: -395.17,
				endingMarginBalance: 0,
				endingMarketValue: 5050.43,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a40a214-7dc0-11ef-8c0f-0374df1d1eea',
				maintenanceCall: 0,
			},
			id: '2e3abe76-5a1c-11ef-9f68-6fd079f27ef0',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO NORTH DAKOTA ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a88806f2-7a74-11ef-aacf-0f1840f0f19a',
				mailingAddress: null,
				owner: {
					id: 'e19fa868-7a7a-11ef-af07-e34d2b08ceba',
					legalAddress: {
						id: 'eab5d828-7a7a-11ef-af07-27ba419cb569',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 4561.64,
				endingCashBalance: -179.9,
				endingMarginBalance: 0,
				endingMarketValue: 4381.74,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a4ed3ac-7dc0-11ef-8c0f-a30ac8434fe5',
				maintenanceCall: 0,
			},
			id: '3078bbd4-5a1c-11ef-a352-2beb14ada1af',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO ARIZONA ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a9125636-7a74-11ef-aacf-c72f7e3da051',
				mailingAddress: null,
				owner: {
					id: 'e2ec2390-7a7a-11ef-af07-b74cc6e65ac9',
					legalAddress: {
						id: 'eb4f228a-7a7a-11ef-af07-d704df8e9c65',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 4273,
				endingCashBalance: -497.32,
				endingMarginBalance: 0,
				endingMarketValue: 3775.68,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a4cc3dc-7dc0-11ef-8c0f-076431741fb2',
				maintenanceCall: 0,
			},
			id: '304bc1d8-5a1c-11ef-a352-8b589471673d',
			investmentObjective: null,
			name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO MONTANA ATTN TAX DEPARTMENT',
			primaryOwner: {
				id: 'a911acf4-7a74-11ef-aacf-fbcbe6e49288',
				mailingAddress: null,
				owner: {
					id: 'ddf3d478-7a7a-11ef-af07-1bc938d5aa36',
					legalAddress: {
						id: 'eb4d6364-7a7a-11ef-af07-a367dc757736',
						state: {
							id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
							name: 'Nebraska',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
		{
			accountNumber: '********',
			accountStatus: 'Open',
			dailyBalances: {
				endingBalance: 2663.26,
				endingCashBalance: -303.91,
				endingMarginBalance: 0,
				endingMarketValue: 2359.35,
				endingMoneyMarketBalance: 0,
				fedCall: 0,
				id: '2a46da44-7dc0-11ef-8c0f-d3702f3b27e8',
				maintenanceCall: 0,
			},
			id: '2f332674-5a1c-11ef-a352-cb670b6b5762',
			investmentObjective: null,
			name: 'TEXAS COMPTROLLER OF PUBLIC ACCOUNTS UNCLAIMED PROPERTY DIVISION',
			primaryOwner: {
				id: 'a90d99ac-7a74-11ef-aacf-5316212848e0',
				mailingAddress: null,
				owner: {
					id: 'e19e95b8-7a7a-11ef-af07-cb1494306d2a',
					legalAddress: {
						id: 'eb41d7ba-7a7a-11ef-af07-e3998509b578',
						state: {
							id: '160e2708-6a7e-11ef-bc2a-13bd1cfb5ce8',
							name: 'Texas',
						},
					},
				},
			},
			registrationType: {
				code: 'ERR-DEP-ACC',
				id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
				name: 'AXOS ERROR/DEPOSIT ACCTS',
			},
			relatedAccounts: [],
			repCode: 'OO03',
			riskTolerance: null,
		},
	],
	apiHandler: {
		_services: {
			baseModel: {
				baseUrl:
					'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai',
				headers: {
					Accept: 'application/json',
					'Content-Type': 'application/json',
				},
				target: 'platform',
				auth: 'auth',
				enableTraceId: true,
			},
			'app-instance-lib-headers': {
				baseUrl: 'https://platform-firstparty.platform-editor.cluster.jiffy.ai',
				headers: {
					Accept: 'application/json',
					'Content-Type': 'application/json',
				},
				target: 'platform',
				auth: 'auth',
				enableTraceId: true,
			},
			'editors-with-appInstance-headers': {
				baseUrl: 'https://platform-firstparty.platform-editor.cluster.jiffy.ai',
				headers: {
					Accept: 'application/json',
					'Content-Type': 'application/json',
				},
				target: 'platform',
				auth: 'auth',
				enableTraceId: true,
			},
			appModel: {
				baseUrl:
					'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/platform/model-repo/rest/v1/tenant/25b1a56a-3063-48f7-b250-cb19ba3241a8/app/48948481-a706-4124-b8df-67d7a5154eb2',
				headers: {},
				target: 'platform',
				auth: 'auth',
				enableTraceId: true,
			},
			apiModel: {
				baseUrl:
					'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/api/jiffy/defaultInternalService',
				headers: {},
				target: 'platform',
				auth: 'auth',
				enableTraceId: true,
			},
			workflowModel: {
				baseUrl:
					'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/platform/workflow/v1',
				headers: {},
				target: 'platform',
				auth: 'auth',
				enableTraceId: true,
			},
			domainAppModel: {
				baseUrl:
					'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/api/domain',
				headers: {},
				target: 'platform',
				auth: 'auth',
				enableTraceId: true,
			},
			drive: {
				baseUrl:
					'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/platform/drive/v1',
				headers: {},
				target: 'platform',
				auth: 'auth',
				enableTraceId: true,
			},
			oms: {
				baseUrl:
					'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/platform/oms/api/v1',
				headers: {},
				target: 'platform',
				auth: 'auth',
				enableTraceId: true,
			},
		},
		_token: '',
		_instanceId: 'fe341faa-c81a-4478-adcc-e24354f2a811',
		_genAppAuthService: {
			authConfig: {
				baseUrl:
					'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/platform/apexiam/v1/auth/',
				redirectUri: 'http://localhost:3000/login',
				clientId: '48948481-a706-4124-b8df-67d7a5154eb2',
				tenantId: 'platform-firstparty',
				logoutUrl:
					'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/platform/auth/realms/platform-firstparty/protocol/openid-connect/logout',
				grantType: 'authorization_code',
				tenantUuid: '25b1a56a-3063-48f7-b250-cb19ba3241a8',
				scope: ['profile', 'openid'],
				responseType: 'code',
				codeChallengeMethod: 'S256',
				state: 'h4u8fF2okGBio38uE',
			},
			auth: 'JIFFY_AUTH',
			authService: {
				idToken:
					'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJNOEpidUpMaW9Ma2lwS2VhQnhxRHhoOE1TUUtvWWdDX2RzLXJYZjlURElJIn0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PFybTGifUYrGJBvT1Ir0o-IzXAgiPr63uZWVDAs8Cjb1KBneeGfWHxwBfMNrAoGN_iMhZXTKBaMPyvdNUZL9QZJU7Ni4jiXbobk1-RQVfgH7efwApAdNy5YKyYHvD5IG3jt5kR-0JrF1IZMpIAEK1CLvLKP1g-JRbh8OrPOz7dbm-Ls-GCgvCjtjkGqmr1SdCfUtgiJKzpj2ZyrEPTrirA-qKW5nEd6iBabTtfQQ8G9L_EICHyrjvrrnYET0502j3U8IMVaBliQcIx38BpdtP4Vt6cG9K-IRJbVlGwmk9BjzjnZzxX-MAmfN206TWKWroC55x8NoXwyZSmcYDj0-Cg',
				accessToken:
					'**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
				userInfo: {},
				expires_in: '7200',
				refreshToken:
					'eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJjOWI1Y2RhMi04YWFkLTQ3NjktYjI1NS05Y2E3MDY3OGZhZjkifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ylfVLlSK-5e71jW2uAGWpBfLZq_X1at05ytS0xJlfkvZi_PsLbvGmxorP2fOr_4C98Vaw4y17xizmsyQwHRtqQ',
				authType: 'login',
				authConfig: {
					baseUrl:
						'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/platform/apexiam/v1/auth/',
					redirectUri: 'http://localhost:3000/login',
					clientId: '48948481-a706-4124-b8df-67d7a5154eb2',
					tenantId: 'platform-firstparty',
					logoutUrl:
						'https://jiffy.develop.platform-firstparty.platform-editor.cluster.jiffy.ai/platform/auth/realms/platform-firstparty/protocol/openid-connect/logout',
					grantType: 'authorization_code',
					tenantUuid: '25b1a56a-3063-48f7-b250-cb19ba3241a8',
					scope: ['profile', 'openid'],
					responseType: 'code',
					codeChallengeMethod: 'S256',
					state: 'h4u8fF2okGBio38uE',
				},
			},
			idleTimeout: '0',
			logoutPage: {},
		},
	},
	componentlabel: 'Datatable2',
	componentShortName: 'Datatable',
	parentId: 'cqfk9c44os4bvhvr5qag',
	position: 0,
	parentWidth: 1555,
	datatableContarinerBoxStyles: {
		width: '100%',
		height: 565,
	},
	containerDirection: 'row',
	alignment: 'flex-start flex-start',
};

export const rowData = [
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 916552.65,
			endingCashBalance: -7030.04,
			endingMarginBalance: 0,
			endingMarketValue: 909522.61,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a45f5c0-7dc0-11ef-8c0f-f7b077c168ba',
			maintenanceCall: 0,
		},
		id: '2f25c8d0-5a1c-11ef-a352-d34e52e3537a',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO FLORIDA ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a90d4556-7a74-11ef-aacf-27182156c35d',
			mailingAddress: null,
			owner: {
				id: 'df33dcc0-7a7a-11ef-af07-376b0e0f5edf',
				legalAddress: {
					id: 'eb41151e-7a7a-11ef-af07-87742c2ec9fa',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 271427.76,
			endingCashBalance: -14668.72,
			endingMarginBalance: 0,
			endingMarketValue: 256758.82,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a4ab07e-7dc0-11ef-8c0f-8ffe372cc074',
			maintenanceCall: 0,
		},
		id: '301f0c06-5a1c-11ef-a352-c7fb17a658b1',
		investmentObjective: null,
		name: 'NEW YORK STATE OFFICE OF THE COMPTROLLER',
		primaryOwner: {
			id: 'a910fe4e-7a74-11ef-aacf-aba0dd9dbf90',
			mailingAddress: null,
			owner: {
				id: 'e1a117ac-7a7a-11ef-af07-17411552990c',
				legalAddress: {
					id: 'eb4b7568-7a7a-11ef-af07-77c7607ac77b',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 268397.02,
			endingCashBalance: -41463.52,
			endingMarginBalance: 0,
			endingMarketValue: 226929.12,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a477274-7dc0-11ef-8c0f-9fdc7680c7fa',
			maintenanceCall: 0,
		},
		id: '2f56af2c-5a1c-11ef-a352-6389ee9b474e',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO DELAWARE ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a90e1c06-7a74-11ef-aacf-374f90ea17a5',
			mailingAddress: null,
			owner: {
				id: 'df2bc27e-7a7a-11ef-af07-fb5336cd121b',
				legalAddress: {
					id: 'eb434bea-7a7a-11ef-af07-a7f3ae6873ef',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 185091.34,
			endingCashBalance: -4015.14,
			endingMarginBalance: 0,
			endingMarketValue: 181075.95,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a4184e0-7dc0-11ef-8c0f-ef5de463c71a',
			maintenanceCall: 0,
		},
		id: '2c75f39e-5a1c-11ef-9f68-c74a44f8714a',
		investmentObjective: null,
		name: 'PENNSYLVANIA UNCLAIMED PROPERT ESCHEATMENT ACCOUNT ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a88383de-7a74-11ef-aacf-d375de4dd14d',
			mailingAddress: null,
			owner: {
				id: 'e104e99a-7a7a-11ef-af07-c32fae5fccc6',
				legalAddress: {
					id: 'eaa9227c-7a7a-11ef-af07-c7e6fb24f920',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 112715.78,
			endingCashBalance: -554.04,
			endingMarginBalance: 0,
			endingMarketValue: 112161.74,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a4512e0-7dc0-11ef-8c0f-7f4526e3f2e1',
			maintenanceCall: 0,
		},
		id: '2ed4906e-5a1c-11ef-a352-8b0b58f4afdf',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO NEVADA ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a90bbfb0-7a74-11ef-aacf-b7f05f48ef8f',
			mailingAddress: null,
			owner: {
				id: 'e1090764-7a7a-11ef-af07-372e21de702c',
				legalAddress: {
					id: 'eb3cb226-7a7a-11ef-af07-5f0ac97edfdd',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 161423.73,
			endingCashBalance: -78489,
			endingMarginBalance: 0,
			endingMarketValue: 82934.73,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: 'cb59bd9a-8570-11ef-b2d6-cb12254c154f',
			maintenanceCall: 0,
		},
		id: '********-5a1c-11ef-a352-e3aef54990fa',
		investmentObjective: null,
		name: 'AXOS NON-MARKETABLE SECURITIES',
		primaryOwner: {
			id: 'a91204a6-7a74-11ef-aacf-cfc1b0df989c',
			mailingAddress: null,
			owner: {
				id: 'e2efaf92-7a7a-11ef-af07-efb24fd03786',
				legalAddress: {
					id: 'eb4e3758-7a7a-11ef-af07-535513f985ee',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO01',
		riskTolerance: 'Aggressive',
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 39122.92,
			endingCashBalance: -824.89,
			endingMarginBalance: 0,
			endingMarketValue: 38298.03,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a480950-7dc0-11ef-8c0f-0304ad5235a2',
			maintenanceCall: 0,
		},
		id: '2f866f32-5a1c-11ef-a352-6f4befc6153a',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO GEORGIA ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a90ed088-7a74-11ef-aacf-ef437d37ea53',
			mailingAddress: null,
			owner: {
				id: 'e19f6588-7a7a-11ef-af07-af253e6bf68a',
				legalAddress: {
					id: 'eb454daa-7a7a-11ef-af07-7f020e3424dc',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 29552.25,
			endingCashBalance: -1014.05,
			endingMarginBalance: 0,
			endingMarketValue: 28531.82,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: 'cb61515e-8570-11ef-b2d6-8764de842da1',
			maintenanceCall: 0,
		},
		id: '2f31778e-5a1c-11ef-a352-2b0dc62529f5',
		investmentObjective: null,
		name: 'STATE OF CALIFORNIA STATE CONTROLLERS OFFICE UNCLAIMED PROPERTY DIVISION',
		primaryOwner: {
			id: 'a90d9542-7a74-11ef-aacf-ef8acdbebc96',
			mailingAddress: null,
			owner: {
				id: 'e2424f14-7a7a-11ef-af07-b32d946e7b95',
				legalAddress: {
					id: 'eb41cb62-7a7a-11ef-af07-b3891587ebb7',
					state: {
						id: '160daa80-6a7e-11ef-bc2a-3f0d070822f7',
						name: 'California',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'Z001',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 26287.11,
			endingCashBalance: -30.04,
			endingMarginBalance: 0,
			endingMarketValue: 26257.07,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a517ef4-7dc0-11ef-8c0f-1332151e69c3',
			maintenanceCall: 0,
		},
		id: '30d70ed2-5a1c-11ef-a352-8ba8a07c2885',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO MINNESOTA ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a98df5ac-7a74-11ef-aacf-836aaaa9c40b',
			mailingAddress: null,
			owner: {
				id: 'e1a2e5fa-7a7a-11ef-af07-5f159e442be3',
				legalAddress: {
					id: 'ebcca5ca-7a7a-11ef-af07-37dbb9b125c8',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 28200.45,
			endingCashBalance: -2185.52,
			endingMarginBalance: 0,
			endingMarketValue: 26014.93,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a48eabe-7dc0-11ef-8c0f-ef9389f3940e',
			maintenanceCall: 0,
		},
		id: '2fd3f5b8-5a1c-11ef-a352-eb2910d062e7',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO HAWAII ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a90fe2fc-7a74-11ef-aacf-43065903f6ad',
			mailingAddress: null,
			owner: {
				id: 'e38d9914-7a7a-11ef-af07-bbd22a2ba385',
				legalAddress: {
					id: 'eb48572a-7a7a-11ef-af07-b3443bb7fffe',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 21775.64,
			endingCashBalance: -25.85,
			endingMarginBalance: 0,
			endingMarketValue: 21749.79,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a4641e2-7dc0-11ef-8c0f-8fae5c662c80',
			maintenanceCall: 0,
		},
		id: '2f2c7d10-5a1c-11ef-a352-3302f1130f2e',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO CONNECTICUT ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a90d8070-7a74-11ef-aacf-571d78aeaaaf',
			mailingAddress: null,
			owner: {
				id: 'ddeac158-7a7a-11ef-af07-6fa1478b93f5',
				legalAddress: {
					id: 'eb41935e-7a7a-11ef-af07-d361c0c109bc',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 19976.16,
			endingCashBalance: -443.57,
			endingMarginBalance: 0,
			endingMarketValue: 19531.56,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a42b4fa-7dc0-11ef-8c0f-07314d3a2061',
			maintenanceCall: 0,
		},
		id: '2d3de746-5a1c-11ef-9f68-8b1b7796b336',
		investmentObjective: null,
		name: 'NEW JERSEY UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a8856208-7a74-11ef-aacf-ef7e7322bce2',
			mailingAddress: null,
			owner: {
				id: 'ddec7c46-7a7a-11ef-af07-379e2b8c83ed',
				legalAddress: {
					id: 'eaae67b4-7a7a-11ef-af07-5fb92a442bb5',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 19303.98,
			endingCashBalance: 0,
			endingMarginBalance: 0,
			endingMarketValue: 19303.98,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a52f9b4-7dc0-11ef-8c0f-23e3053ddc70',
			maintenanceCall: 0,
		},
		id: '2f75e68a-5a1c-11ef-a352-671fc01baf17',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO WYOMING ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a90e9050-7a74-11ef-aacf-e3d6ce785b58',
			mailingAddress: null,
			owner: {
				id: 'e384ffc0-7a7a-11ef-af07-637c9a90b481',
				legalAddress: {
					id: 'eb4497e8-7a7a-11ef-af07-631392be1377',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 209525,
			endingCashBalance: -194195.59,
			endingMarginBalance: 0,
			endingMarketValue: 15329.41,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: 'cb5e8b54-8570-11ef-b2d6-bb9d3311798b',
			maintenanceCall: 0,
		},
		id: '2fa48d50-5a1c-11ef-a352-e3aaa8f72125',
		investmentObjective: null,
		name: 'AXOS CLEARING HOUSE ACCOUNT',
		primaryOwner: {
			id: 'a90f4450-7a74-11ef-aacf-53485071b183',
			mailingAddress: null,
			owner: {
				id: 'dfd42a90-7a7a-11ef-af07-8b9732b29882',
				legalAddress: {
					id: 'eb469494-7a7a-11ef-af07-fb16d020ba39',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO13',
		riskTolerance: 'Aggressive',
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 9097.63,
			endingCashBalance: -302.53,
			endingMarginBalance: 0,
			endingMarketValue: 8795.1,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a4a18d0-7dc0-11ef-8c0f-e72726a00cab',
			maintenanceCall: 0,
		},
		id: '3010f6f2-5a1c-11ef-a352-************',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO COLORADO ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a910c9ba-7a74-11ef-aacf-63fe3ce1945b',
			mailingAddress: null,
			owner: {
				id: 'e38a2c66-7a7a-11ef-af07-1b447ed94eac',
				legalAddress: {
					id: 'eb4adfd6-7a7a-11ef-af07-bb8db0c1fe2e',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 6480.6,
			endingCashBalance: 0,
			endingMarginBalance: 0,
			endingMarketValue: 6480.6,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a53dc26-7dc0-11ef-8c0f-1febdeeed8b5',
			maintenanceCall: 0,
		},
		id: '307679b4-5a1c-11ef-a352-6beb********',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO OKLAHOMA ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a9124d76-7a74-11ef-aacf-d36e9a6b632a',
			mailingAddress: null,
			owner: {
				id: 'ddf092f4-7a7a-11ef-af07-3b1157181ac4',
				legalAddress: {
					id: 'eb4f099e-7a7a-11ef-af07-177a0aa0df52',
					state: {
						id: '160e107e-6a7e-11ef-bc2a-575f9ea98338',
						name: 'Oklahoma',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 5445.6,
			endingCashBalance: -395.17,
			endingMarginBalance: 0,
			endingMarketValue: 5050.43,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a40a214-7dc0-11ef-8c0f-0374df1d1eea',
			maintenanceCall: 0,
		},
		id: '2e3abe76-5a1c-11ef-9f68-6fd079f27ef0',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO NORTH DAKOTA ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a88806f2-7a74-11ef-aacf-0f1840f0f19a',
			mailingAddress: null,
			owner: {
				id: 'e19fa868-7a7a-11ef-af07-e34d2b08ceba',
				legalAddress: {
					id: 'eab5d828-7a7a-11ef-af07-27ba419cb569',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 4561.64,
			endingCashBalance: -179.9,
			endingMarginBalance: 0,
			endingMarketValue: 4381.74,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a4ed3ac-7dc0-11ef-8c0f-a30ac8434fe5',
			maintenanceCall: 0,
		},
		id: '3078bbd4-5a1c-11ef-a352-2beb14ada1af',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO ARIZONA ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a9125636-7a74-11ef-aacf-c72f7e3da051',
			mailingAddress: null,
			owner: {
				id: 'e2ec2390-7a7a-11ef-af07-b74cc6e65ac9',
				legalAddress: {
					id: 'eb4f228a-7a7a-11ef-af07-d704df8e9c65',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 4273,
			endingCashBalance: -497.32,
			endingMarginBalance: 0,
			endingMarketValue: 3775.68,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a4cc3dc-7dc0-11ef-8c0f-076431741fb2',
			maintenanceCall: 0,
		},
		id: '304bc1d8-5a1c-11ef-a352-8b589471673d',
		investmentObjective: null,
		name: 'UNCLAIMED PROPERTY ESCHEATMENT ACCOUNT FBO MONTANA ATTN TAX DEPARTMENT',
		primaryOwner: {
			id: 'a911acf4-7a74-11ef-aacf-fbcbe6e49288',
			mailingAddress: null,
			owner: {
				id: 'ddf3d478-7a7a-11ef-af07-1bc938d5aa36',
				legalAddress: {
					id: 'eb4d6364-7a7a-11ef-af07-a367dc757736',
					state: {
						id: '160df2e2-6a7e-11ef-bc2a-539f615828c7',
						name: 'Nebraska',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 2663.26,
			endingCashBalance: -303.91,
			endingMarginBalance: 0,
			endingMarketValue: 2359.35,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a46da44-7dc0-11ef-8c0f-d3702f3b27e8',
			maintenanceCall: 0,
		},
		id: '2f332674-5a1c-11ef-a352-cb670b6b5762',
		investmentObjective: null,
		name: 'TEXAS COMPTROLLER OF PUBLIC ACCOUNTS UNCLAIMED PROPERTY DIVISION',
		primaryOwner: {
			id: 'a90d99ac-7a74-11ef-aacf-5316212848e0',
			mailingAddress: null,
			owner: {
				id: 'e19e95b8-7a7a-11ef-af07-cb1494306d2a',
				legalAddress: {
					id: 'eb41d7ba-7a7a-11ef-af07-e3998509b578',
					state: {
						id: '160e2708-6a7e-11ef-bc2a-13bd1cfb5ce8',
						name: 'Texas',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 2663.26,
			endingCashBalance: -303.91,
			endingMarginBalance: 0,
			endingMarketValue: 2359.35,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a46da44-7dc0-11ef-8c0f-d3702f3b27e8',
			maintenanceCall: 0,
		},
		id: '2f332674-5a1c-11ef-a352-cb670b6b576k',
		investmentObjective: null,
		name: 'TEXAS COMPTROLLER OF PUBLIC ACCOUNTS UNCLAIMED PROPERTY DIVISION',
		primaryOwner: {
			id: 'a90d99ac-7a74-11ef-aacf-5316212848e0',
			mailingAddress: null,
			owner: {
				id: 'e19e95b8-7a7a-11ef-af07-cb1494306d2a',
				legalAddress: {
					id: 'eb41d7ba-7a7a-11ef-af07-e3998509b578',
					state: {
						id: '160e2708-6a7e-11ef-bc2a-13bd1cfb5ce8',
						name: 'Texas',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
	{
		accountNumber: '********',
		accountStatus: 'Open',
		dailyBalances: {
			endingBalance: 2663.26,
			endingCashBalance: -303.91,
			endingMarginBalance: 0,
			endingMarketValue: 2359.35,
			endingMoneyMarketBalance: 0,
			fedCall: 0,
			id: '2a46da44-7dc0-11ef-8c0f-d3702f3b27e8',
			maintenanceCall: 0,
		},
		id: '2f332674-5a1c-11ef-a352-cb670b6b576v',
		investmentObjective: null,
		name: 'TEXAS COMPTROLLER OF PUBLIC ACCOUNTS UNCLAIMED PROPERTY DIVISION',
		primaryOwner: {
			id: 'a90d99ac-7a74-11ef-aacf-5316212848e0',
			mailingAddress: null,
			owner: {
				id: 'e19e95b8-7a7a-11ef-af07-cb1494306d2a',
				legalAddress: {
					id: 'eb41d7ba-7a7a-11ef-af07-e3998509b578',
					state: {
						id: '160e2708-6a7e-11ef-bc2a-13bd1cfb5ce8',
						name: 'Texas',
					},
				},
			},
		},
		registrationType: {
			code: null,
			id: '6ab1b4e6-75b4-11ef-bb36-db13bfecff81',
			name: 'AXOS ERROR/DEPOSIT ACCTS',
		},
		relatedAccounts: [],
		repCode: 'OO03',
		riskTolerance: null,
	},
];

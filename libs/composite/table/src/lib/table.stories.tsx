import { Story, Meta } from '@storybook/react';
import Table from './table';
import { defaultProps } from './metadata';
import { rowData, WorkStationProps } from './data/workstation.example.model';
import {
	WealthDomainProps,
	largeRowData as WealthRowData,
} from './data/wealth.example.model';
import {
	rowData as RegressionData,
	RegressionTableProps,
} from './data/regression.example.model';
import {
	rowData as RegressionData2,
	RegressionTableProps2,
} from './data/regression.example.model.2';

import { DownloadOptionType, ITableProps } from './types';
import {
	accountRowData,
	addressRowData,
	boEntityData1,
	verticalKeyValueRowData,
	getEnumAttributeData,
	errorRowData,
	boEntityData2,
	enumRowData,
} from './data/boEntity.data';
import { v4 as generateUUID } from 'uuid';
import Button from '@atomic/button';
import { useCallback, useState } from 'react';
import Iconpicker from '@composite/iconpicker';
import { IconCodes } from '@atomic/icon';
import Listview from '@atomic/listview';
import Colorpicker, { ColorPickerType } from '@composite/colorpicker';
import Box from '@atomic/box';
import { APIProvider } from '@base/utils-hooks';

export default {
	component: Table,
	title: 'Composite/Table',
	argTypes: {
		id: { type: 'number' },
	},
} as Meta;

const Template: Story<ITableProps> = (args) => {
	return (
		<APIProvider>
			<Table
				{...args}
				{...(WorkStationProps as any)}
				loadDataSource={(params) => {
					const { page, size, sort, queryParams } = params;
					return {
						apiData: rowData.slice(page * size, (page + 1) * size),
						headers:
							page === 0 ? { 'x-jiffy-total-count': rowData.length } : {},
					};
				}}
			></Table>
		</APIProvider>
	);
};

export const WorkstationModelStory = Template.bind({});
WorkstationModelStory.args = {
	...defaultProps,
};

const WealthDomainModelTemplate: Story<ITableProps> = (args) => {
	return (
		<APIProvider>
			<Table
				{...args}
				{...(WealthDomainProps as any)}
				onPaginationHandle={(params) => {
					const { page, size, sort, queryParams } = params;
					return {
						apiData: WealthRowData.slice(page * size, (page + 1) * size),
						headers: { 'x-jiffy-total-count': WealthRowData.length },
					};
				}}
			></Table>
		</APIProvider>
	);
};

export const WealthDomainModelStory = WealthDomainModelTemplate.bind({});
WealthDomainModelStory.args = {
	...defaultProps,
};

const RegressionAppModelTemplate: Story<ITableProps> = (args) => {
	return (
		<APIProvider>
			<Table
				{...args}
				{...(RegressionTableProps as any)}
				onPaginationHandle={() => {
					return {
						apiData: RegressionData,
						headers: { 'x-jiffy-total-count': 100 },
					};
				}}
			/>
		</APIProvider>
	);
};

export const RegressionAppModelTemplateStory = RegressionAppModelTemplate.bind(
	{}
);
RegressionAppModelTemplateStory.args = {
	...defaultProps,
};

const RegressionAppModelTemplate2: Story<ITableProps> = (args) => {
	return (
		<APIProvider>
			<Table
				{...args}
				{...(RegressionTableProps2 as any)}
				onPaginationHandle={() => {
					return {
						apiData: RegressionData2,
						headers: { 'x-jiffy-total-count': 100 },
					};
				}}
			/>
		</APIProvider>
	);
};

export const RegressionAppModelTemplateStory2 =
	RegressionAppModelTemplate2.bind({});
RegressionAppModelTemplateStory2.args = {
	...defaultProps,
};

const TableTemplate: Story<ITableProps> = (args) => {
	const [rowData, setRowData] = useState<Record<string, any>[]>(accountRowData);
	const [boEntityData, setBoEntityData] =
		useState<Record<string, any>>(boEntityData1);
	const [isAccount, setIsAccount] = useState(true);
	const [forceReloadPaginationHandle, setForceReloadPaginationHandle] =
		useState(false);
	const insertBoData = async (row) => {
		return Promise.resolve({ ...row, id: generateUUID() });
	};

	const baseUrl = 'https://integrationtest.jiffy.ai';
	const tenantId = '76851fa5-219c-4c7f-b39a-2a078b71b688';
	const uploadDetails = {
		baseUrl: baseUrl,
		tenantId: tenantId,
	};

	const columnData = (boEntityData?.fields || [])
		.map((field) => {
			const isLink = field?.type?.toLowerCase() === 'link';
			const isComposition = field?.association?.type === 'COMPOSITION';
			const enumData = (getEnumAttributeData(field) || []).map(
				(item, index) => ({
					id: index,
					label: item,
					value: item,
				})
			);
			const isEnumType = enumData.length > 0;
			return {
				key: field.name || '',
				color: '#73767C',
				name: field.label || field.name || '',
				type: field.type?.toLowerCase() === 'link' ? 'Link' : field.type || '',
				dataType: isEnumType
					? 'Dropdown'
					: field.type?.toLowerCase() === 'link'
					? 'Link'
					: field.type,
				filterDisabled: isLink,
				dataTypeListData: enumData,
				linkDetails: isLink
					? {
							boId: field?.association?.attribute?.targetId,
							isComposition: isComposition,
							isArray: ['ONE_TO_MANY', 'MANY_TO_MANY'].includes(
								field?.association?.multiplicity
							),
					  }
					: undefined,
				...(field.name === 'id' && { width: 100 }),
				...(field.name === 'id' && {
					filterOptions: [
						'equals',
						'notEqual',
						'startsWith',
						'endsWith',
						'blank',
						'notBlank',
					],
				}),
				isFileArray:
					field?.type?.toLowerCase() === 'file' && field.array ? true : false,
				isArray: !!field.array,
				isReadOnly: !!field?.computed,
			};
		})
		.filter((item) => item !== undefined);

	const handleCellValueChange = async (row) => {
		const data = row;
		const gridAPi = row.datatableCell.api;
		try {
			const response = await insertBoData(row);
			// If success, clear any previous errors
			// for new row insert we are insering a new row without any id, after getting the response we are removing the row with undefined id and adding the new row with the response id

			if (!row.id) {
				// Get the first row index of the current page
				const currentPage = gridAPi.paginationGetCurrentPage();
				const pageSize = gridAPi.paginationGetPageSize();
				const firstRowIndex = currentPage * pageSize;

				gridAPi.applyServerSideTransaction({
					remove: [{ id: undefined }], // Remove the old row
				});

				gridAPi.applyServerSideTransaction({
					addIndex: firstRowIndex,
					add: [{ ...data, ...response }], // Add the new row with the rowId from repsonse
				});
			}

			data.error = null;
		} catch (error) {
			// If error, store error information
			const errorFields = data.errorFields || {};
			data.errorFields = {
				...errorFields,
				[row.datatableCell.cellName]: error.errors,
			};
		}
		gridAPi.applyServerSideTransaction({
			update: [data],
		});
		return;
	};

	const handleClick = () => {
		setRowData(isAccount ? addressRowData : accountRowData);
		setBoEntityData(isAccount ? boEntityData2 : boEntityData1);
		setIsAccount(!isAccount);
		setForceReloadPaginationHandle((prev) => !prev);
	};

	const loadData = useCallback(
		(params) => {
			const { page, size, sort, queryParams } = params;
			return {
				apiData: rowData.slice(page * size, (page + 1) * size),
				headers: page === 0 ? { 'x-jiffy-total-count': rowData.length } : {},
			};
		},
		[rowData]
	);

	return (
		<APIProvider>
			<>
				<Button onClick={handleClick} title={'Click me'} />
				<Table
					{...args}
					label={'Account Onboarding'}
					id={'123456'}
					columnData={columnData}
					onRowClick={(row) => {}}
					onCellValueChange={(row) => handleCellValueChange(row)}
					onPaginationHandle={loadData}
					forceReloadPaginationHandle={forceReloadPaginationHandle}
					canAddNewRow={true}
					uploadDetails={uploadDetails}
					isGeneratedApp={true}
				></Table>
			</>
		</APIProvider>
	);
};

export const TableStory = TableTemplate.bind({});
TableStory.args = {
	enableColumnFilter: true,
	errorFieldName: 'errorFields',
	allowPagination: true,
	alternateRowHighlight: false,
	canAddNewColumn: true,
	dataTableStyles: {
		width: '100%',
		height: 'calc(90vh - 50px)',
		minHeight: '200px',
		fontWeight: 400,
		color: '#73767C',
	},
	downloadOption: DownloadOptionType['Pdf & Excel'] as any,
	enableInfinteScrolling: false,
	fileNamingPreference: 'AUTOMATIC_FILE_NAMING',
	filterOption: 'filterOnly', // Possible options based on context
	isEditable: true,
	isDeleteable: true,
	showFooter: true,
	showHeader: true,
	showLabel: true,
	allowServersideFilter: true,
	enableFilter: true,
	enableAdvancedFilter: false,
	// filter
	gridActions: [
		{
			iconCode: 'icon_-Bd_View',
			label: 'View',
			text: 'Sample text',
			showLabel: true,
			withLabel: true,
			componentlabel: 'Component',
		},
		{
			iconCode: 'icon_-Bd_Edit',
			label: 'Edit',
			text: 'Sample text',
			showLabel: true,
			withLabel: true,
			componentlabel: 'Component',
		},
	],
	bulkActions: [],
	labelBold: true,
	// headerBackgroundColor: '#fbfacb',
	// headerHoverColor: '#bd10e0',
	// headerFontColor: '#d0021b',
	// borderColor: '#E9E9EA',
	// rowHoverColor: '#E7F7EE',
	// headerFontHoverColor: '#7ed321',
	// innerBorderRadius: "100px 100px 100px 100px",
};

const ClientSideRowDataTemplate: Story<ITableProps> = (args) => {
	const [boEntityData, setBoEntityData] =
		useState<Record<string, any>>(boEntityData1);
	useState(false);
	const columnData = (boEntityData?.fields || [])
		.map((field) => {
			const isLink = field?.type?.toLowerCase() === 'link';
			const isComposition = field?.association?.type === 'COMPOSITION';
			const enumData = (getEnumAttributeData(field) || []).map(
				(item, index) => ({
					id: index,
					label: item,
					value: item,
				})
			);
			const isEnumType = enumData.length > 0;
			return {
				key: field.name || '',
				color: '#73767C',
				name: field.label || field.name || '',
				type: field.type?.toLowerCase() === 'link' ? 'Link' : field.type || '',
				dataType: isEnumType
					? 'Dropdown'
					: field.type?.toLowerCase() === 'link'
					? 'Link'
					: field.type,
				filterDisabled: isLink,
				dataTypeListData: enumData,
				linkDetails: isLink
					? {
							boId: field?.association?.attribute?.targetId,
							isComposition: isComposition,
							isArray: ['ONE_TO_MANY', 'MANY_TO_MANY'].includes(
								field?.association?.multiplicity
							),
					  }
					: undefined,
				...(field.name === 'id' && { width: 100 }),
				...(field.name === 'id' && {
					filterOptions: [
						'equals',
						'notEqual',
						'startsWith',
						'endsWith',
						'blank',
						'notBlank',
					],
				}),
				isFileArray:
					field?.type?.toLowerCase() === 'file' && field.array ? true : false,
				isArray: !!field.array,
				isReadOnly: !!field?.computed,
			};
		})
		.filter((item) => item !== undefined);
	return (
		<APIProvider>
			<>
				<Table
					{...args}
					label={'Account Onboarding'}
					id={'123456'}
					columnData={columnData}
					rowData={accountRowData}
				></Table>
			</>
		</APIProvider>
	);
};

export const ClientSideRowDataStory = ClientSideRowDataTemplate.bind({});
ClientSideRowDataStory.args = {
	enableServerSideRender: false,
	allowPagination: false,
	enablePagination: false,
	enableInfinteScrolling: true,
	alternateRowHighlight: false,
	dataTableStyles: {
		width: '100%',
		height: 'calc(90vh - 50px)',
		minHeight: '200px',
		fontWeight: 400,
		color: '#73767C',
	},
	downloadOption: DownloadOptionType['Pdf & Excel'] as any,
	fileNamingPreference: 'AUTOMATIC_FILE_NAMING',
	filterOption: 'searchOnly', // Possible options based on context
	isEditable: true,
	isDeleteable: true,
	showFooter: true,
	showHeader: true,
	showLabel: true,
	allowServersideFilter: true,
	enableFilter: true,
	enableAdvancedFilter: false,
	bulkActions: [],
	labelBold: true,
};

const LoadingErrorTableTemplate: Story<ITableProps> = (args) => {
	const [rowData, setRowData] = useState<Record<string, any>[]>(accountRowData);
	const [boEntityData, setBoEntityData] =
		useState<Record<string, any>>(boEntityData1);
	const [forceReloadPaginationHandle, setForceReloadPaginationHandle] =
		useState(false);
	const baseUrl = 'https://integrationtest.jiffy.ai';
	const tenantId = '76851fa5-219c-4c7f-b39a-2a078b71b688';
	const uploadDetails = {
		baseUrl: baseUrl,
		tenantId: tenantId,
	};

	const columnData = (boEntityData?.fields || [])
		.map((field) => {
			const isLink = field?.type?.toLowerCase() === 'link';
			const isComposition = field?.association?.type === 'COMPOSITION';
			const enumData = (getEnumAttributeData(field) || []).map(
				(item, index) => ({
					id: index,
					label: item,
					value: item,
				})
			);
			const isEnumType = enumData.length > 0;
			return {
				key: field.name || '',
				color: '#73767C',
				name: field.label || field.name || '',
				type: field.type?.toLowerCase() === 'link' ? 'Link' : field.type || '',
				dataType: isEnumType
					? 'Dropdown'
					: field.type?.toLowerCase() === 'link'
					? 'Link'
					: field.type,
				filterDisabled: isLink,
				dataTypeListData: enumData,
				linkDetails: isLink
					? {
							boId: field?.association?.attribute?.targetId,
							isComposition: isComposition,
							isArray: ['ONE_TO_MANY', 'MANY_TO_MANY'].includes(
								field?.association?.multiplicity
							),
					  }
					: undefined,
				...(field.name === 'id' && { width: 100 }),
				...(field.name === 'id' && {
					filterOptions: [
						'equals',
						'notEqual',
						'startsWith',
						'endsWith',
						'blank',
						'notBlank',
					],
				}),
				isFileArray:
					field?.type?.toLowerCase() === 'file' && field.array ? true : false,
				isArray: !!field.array,
				isReadOnly: !!field?.computed,
			};
		})
		.filter((item) => item !== undefined);

	const loadData = useCallback(
		(params) => {
			if (params.queryParams) {
				throw new Error('test');
			} else {
				const { page, size, sort, queryParams } = params;
				return {
					apiData: rowData.slice(page * size, (page + 1) * size),
					headers: page === 0 ? { 'x-jiffy-total-count': rowData.length } : {},
				};
			}
		},
		[rowData]
	);

	return (
		<APIProvider>
			<>
				<Table
					{...args}
					label={'Account Onboarding'}
					id={'123456'}
					columnData={columnData}
					onRowClick={(row) => {}}
					// onPaginationHandle={loadData}
					loadDataSource={loadData}
					forceReloadPaginationHandle={forceReloadPaginationHandle}
					canAddNewRow={true}
					uploadDetails={uploadDetails}
				></Table>
			</>
		</APIProvider>
	);
};

export const LoadingErrorTableStory = LoadingErrorTableTemplate.bind({});
LoadingErrorTableStory.args = {
	enableColumnFilter: true,
	errorFieldName: 'errorFields',
	allowPagination: true,
	alternateRowHighlight: false,
	canAddNewColumn: true,
	dataTableStyles: {
		width: '100%',
		height: 'calc(90vh - 50px)',
		minHeight: '200px',
		fontWeight: 400,
		color: '#73767C',
	},
	downloadOption: DownloadOptionType['Pdf & Excel'] as any,
	enableInfinteScrolling: true,
	fileNamingPreference: 'AUTOMATIC_FILE_NAMING',
	filterOption: 'filterOnly', // Possible options based on context
	isEditable: true,
	isDeleteable: true,
	showFooter: true,
	showHeader: true,
	showLabel: true,
	allowServersideFilter: true,
	enableFilter: true,
	enableAdvancedFilter: false,
	gridActions: [
		{
			iconCode: 'icon_-Bd_View',
			label: 'View',
			text: 'Sample text',
			showLabel: true,
			withLabel: true,
			componentlabel: 'Component',
		},
		{
			iconCode: 'icon_-Bd_Edit',
			label: 'Edit',
			text: 'Sample text',
			showLabel: true,
			withLabel: true,
			componentlabel: 'Component',
		},
	],
	bulkActions: [],
	labelPadding: '0px 0px 0px 100px',
	labelBold: true,
};

const VerticalKeyValueTableTemplate: Story<ITableProps> = (args) => {
	const headerData = [
		{
			key: 'attribute',
			name: 'Attribute',
			type: 'Singlelinetext',
			dataType: 'Singlelinetext',
			filterDisabled: false,
			color: '#73767C',
		},
		{
			key: 'value',
			name: 'Value',
			type: 'Singlelinetext',
			dataType: 'Singlelinetext',
			filterDisabled: false,
			color: '#73767C',
		},
	];

	// const baseUrl = window._env_?.Config?.BASE_URL || '';
	const baseUrl = 'https://integrationtest.jiffy.ai/platform';
	// const tenantId = window._env_?.Config?.AUTH_CONFIG?.TENANT_ID || '';
	const tenantId = '76851fa5-219c-4c7f-b39a-2a078b71b688';
	const uploadDetails = {
		baseUrl: baseUrl,
		tenantId: tenantId,
	};

	const rowData = verticalKeyValueRowData;
	const insertBoData = async (row) => {
		throw errorRowData;
	};
	const handleCellValueChange = async (row) => {
		const data = row;
		try {
			const response = await insertBoData(row);
			// If success, clear any previous errors
			data.error = null;
		} catch (error) {
			// If error, store error information
			const errorFields = data.errorFields || {};
			data.errorFields = {
				...errorFields,
				[row.datatableCell.cellName]: error.errors,
			};
		}
		const gridAPi = row.datatableCell.api;
		gridAPi.applyServerSideTransaction({
			update: [data],
		});
		return;
	};

	return (
		<APIProvider>
			<Table
				{...args}
				id={'123456'}
				label={'Account (Account Name)'}
				isVerticalKeyValueGrid={true}
				columnData={headerData}
				onCellValueChange={(row) => handleCellValueChange(row)}
				rowData={rowData}
				uploadDetails={uploadDetails}
			></Table>
		</APIProvider>
	);
};

export const VerticalKeyValueTableStory = VerticalKeyValueTableTemplate.bind(
	{}
);
VerticalKeyValueTableStory.args = {
	enableServerSideRender: false,
	enablePagination: false,
	errorFieldName: 'errorFields',
	allowPagination: false,
	alternateRowHighlight: false,
	canAddNewColumn: false,
	dataTableStyles: {
		width: '100%',
		height: 'calc(90vh - 60px)',
		minHeight: '200px',
		fontWeight: 400,
		color: '#73767C',
	},
	downloadOption: DownloadOptionType['Pdf & Excel'] as any,
	enableInfinteScrolling: false,
	fileNamingPreference: 'AUTOMATIC_FILE_NAMING',
	filterOption: 'filterOnly', // Possible options based on context
	allowServersideFilter: true,
	enableFilter: true,
	isEditable: true,
	isDeleteable: true,
	showFooter: false,
	showHeader: true,
	showLabel: true,
	enableAdvancedFilter: true,
	gridActions: [],
	bulkActions: [],
	labelPadding: true,
	labelBold: true,
	headerBackgroundColor: '#E9E9EA',
	headerFontColor: '#73767C',
	borderColor: '#E9E9EA',
	rowHoverColor: '#E7F7EE',
};

const RowDragTableTemplate: Story<ITableProps> = (args) => {
	const rowData = enumRowData;
	const headerData = [
		{
			key: 'optionValue',
			name: 'Value',
			type: 'Singlelinetext',
			dataType: 'Singlelinetext',
			color: '#73767C',
			rowDrag: true,
		},
		{
			key: 'optionLabel',
			name: 'Label',
			type: 'Singlelinetext',
			dataType: 'Singlelinetext',
			color: '#73767C',
		},
		{
			key: 'orgUnitCode',
			name: 'Org Unit Code',
			type: 'Singlelinetext',
			dataType: 'Singlelinetext',
			color: '#73767C',
		},
		{
			key: 'color',
			name: 'Color',
			type: 'Singlelinetext',
			dataType: 'Singlelinetext',
			color: '#73767C',
			cellRenderer: CustomColorPicker,
			isReadOnly: true,
		},
		{
			key: 'icon',
			name: 'Icon',
			type: 'Singlelinetext',
			dataType: 'Singlelinetext',
			color: '#73767C',
			cellRenderer: CustomIconPicker,
			isReadOnly: true,
		},
		{
			key: 'status',
			name: 'Status',
			type: 'Singlelinetext',
			dataType: 'Singlelinetext',
			color: '#73767C',
		},
	];

	return (
		<APIProvider>
			<Table
				{...args}
				id={'123456'}
				label={'Account (Account Name)'}
				columnData={headerData}
				rowData={rowData}
				allowRowDrag={true}
			></Table>
		</APIProvider>
	);
};

export const RowDragTableStory = RowDragTableTemplate.bind({});
RowDragTableStory.args = {
	isSortingEnabled: false,
	enableServerSideRender: false,
	enablePagination: false,
	errorFieldName: 'errorFields',
	allowPagination: false,
	alternateRowHighlight: false,
	canAddNewColumn: false,
	dataTableStyles: {
		width: '100%',
		height: 'calc(90vh - 60px)',
		minHeight: '200px',
		fontWeight: 400,
		color: '#73767C',
	},
	downloadOption: DownloadOptionType['Pdf & Excel'] as any,
	enableInfinteScrolling: true,
	fileNamingPreference: 'AUTOMATIC_FILE_NAMING',
	// filterOption: 'filterOnly', // Possible options based on context
	allowServersideFilter: true,
	enableFilter: false,
	isEditable: true,
	isDeleteable: true,
	showFooter: false,
	showHeader: true,
	showLabel: true,
	enableAdvancedFilter: true,
	gridActions: [],
	bulkActions: [],
	labelPadding: true,
	labelBold: true,
	headerBackgroundColor: '#E9E9EA',
	headerFontColor: '#73767C',
	borderColor: '#E9E9EA',
	rowHoverColor: '#E7F7EE',
};

const CustomIconPicker = (props) => {
	const updateIconHandler = useCallback(
		(icon) => {
			props.api.applyTransaction({ update: [{ ...props.data, icon: icon }] });
		},
		[props.api, props.data]
	);

	return (
		<Iconpicker
			icon={IconCodes[props.value]}
			selectedIconCode={updateIconHandler}
			iconProps={{ height: '40px' }}
			showArrow={false}
		/>
	);
};

const CustomColorPicker = (props) => {
	const [isOpen, setIsOpen] = useState(false);
	const handleColorChange = useCallback(
		(color) => {
			props.api.applyTransaction({ update: [{ ...props.data, color: color }] });
		},
		[props.api, props.data]
	);
	const colorPickerProps = {
		colorPickerType: ColorPickerType.Expanded,
		singleColorSize: 32,
		colorPickerTitle: 'Color Picker',
		singleColorBoxProps: {},
	};

	return (
		<Listview
			triggerElement={
				<Box width="100%" height="100%" flexGrow={1}>
					<Box
						id="color-picker-trigger"
						borderRadius="4px"
						width="30px"
						height="30px"
						backgroundColor={props.value}
					></Box>
				</Box>
			}
			auto={true}
			showArrow={false}
			triggerOffset={15}
			listviewContainerStyles={{
				width: 'fit-content',
				padding: 0,
			}}
			triggerElementStyles={{
				display: 'flex',
				width: 'fit-content',
			}}
			isMenuOpen={isOpen}
			isMenuOpenCallBack={(value) => {
				setIsOpen(value);
			}}
			{...props.listviewProps}
		>
			<Colorpicker {...colorPickerProps} onClick={handleColorChange} />
		</Listview>
	);
};

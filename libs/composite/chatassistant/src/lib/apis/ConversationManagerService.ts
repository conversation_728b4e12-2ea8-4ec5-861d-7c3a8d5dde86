import { ApiService } from '../../../../../base/utils-hooks/src/lib/apiService/apiService';

export class ConversationManagerService {
	private apiServiceInstance!: ApiService;

	init(apiServiceInstance: ApiService) {
		this.apiServiceInstance = apiServiceInstance;
	}

	getConversations = async (query: string): Promise<any> => {
		return this.apiServiceInstance.call(
			'conversation',
			'get',
			`/conversations?${query}`
		);
	};

	getConversationItems = async (conversationId: string): Promise<any> => {
		return this.apiServiceInstance.call(
			'conversation',
			'get',
			`/conversations/${conversationId}/items`
		);
	};
}

export default new ConversationManagerService();

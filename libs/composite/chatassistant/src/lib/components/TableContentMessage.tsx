import Table from '@composite/table';
import { getWealthDomainProps } from '../table';
import { v4 as uuid_v4 } from 'uuid';
import SimpleTextMessage from './SimpleTextMessage';
const TableContentMessage = ({
	message,
	classes,
	isFullscreen,
	handleNavigationMessage,
}: any) => {
	const { headers = [], rows = [], types = [] } = message.structured_data;
	const columnData: any[] = headers?.map((header: any, index: number) => ({
		dataType: types?.[index] ?? 'Singlelinetext',
		id: uuid_v4(),
		key: header,
		label: header,
		name: header,
		type: 'var',
	}));
	const rowData: any[] = rows.map((row: any) => {
		return columnData?.reduce(
			(all: any, column: any, index: number) => ({
				...all,

				[column.key]: row[index],
			}),
			{ id: uuid_v4() }
		);
	});
	return (
		<div className={classes.messageWrapper}>
			<SimpleTextMessage
				message={message}
				classes={classes}
				handleNavigationMessage={handleNavigationMessage}
			/>
			<div className={classes.tableWrapper}>
				<Table
					{...getWealthDomainProps(
						isFullscreen,
						isFullscreen,
						false,
						isFullscreen ? '518px' : '320px',
						columnData
					)}
					onPaginationHandle={() => {
						return {
							apiData: rowData,
							headers: {
								'x-jiffy-total-count': rowData.length,
							},
						};
					}}
				></Table>
			</div>
		</div>
	);
};
export default TableContentMessage;

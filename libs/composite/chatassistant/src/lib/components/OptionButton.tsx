import { OptionButtonProps } from '../types';
import SimpleTextMessage from './SimpleTextMessage';

const OptionButton = ({
	key,
	classes,
	handleSendMessage,
	message,
	handleNavigationMessage,
}: OptionButtonProps) => {
	const options = message?.options ?? [];
	return (
		<div className={classes.messageWrapper}>
			<SimpleTextMessage
				message={message}
				classes={classes}
				handleNavigationMessage={handleNavigationMessage}
			/>
			<div className={classes.messageWrapper}>
				{options.map((option: any) => (
					<button
						key={key}
						className={classes.optionButtonStyle}
						onClick={() => handleSendMessage(option.action)}
					>
						{option.option}
					</button>
				))}
			</div>
		</div>
	);
};
export default OptionButton;

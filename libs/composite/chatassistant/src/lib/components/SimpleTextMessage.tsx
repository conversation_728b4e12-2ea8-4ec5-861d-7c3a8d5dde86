import { SimpleTextMessageProps } from '../types';

const SimpleTextMessage = ({
	message,
	classes,
	handleNavigationMessage,
}: SimpleTextMessageProps) => {
	const text = message?.structured_data?.html ?? message.text;
	const onClick = (event: any) => {
		// Define your click handler logic here
		event.preventDefault();
		if (message?.navigate?.[event.target?.id]) {
			handleNavigationMessage(message.navigate[event.target.id]);
		}
	};
	return (
		text && (
			<div className={classes.messageWrapper}>
				<div
					onClick={onClick}
					className={`${classes.message} assistant`}
					dangerouslySetInnerHTML={{ __html: text }}
				/>
			</div>
		)
	);
};
export default SimpleTextMessage;

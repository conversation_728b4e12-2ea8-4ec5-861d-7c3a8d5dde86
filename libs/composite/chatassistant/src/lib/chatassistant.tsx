/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the Chatassistant component
import React, { FC, useCallback, useEffect, useRef, useState } from 'react';
import { useTheme } from 'react-jss';
import { configurable, useEditContext } from '@base/config-provider';
import { setMetaData, useAPIService } from '@base/utils-hooks';
import { ApexTheme } from '@base/theme';
import { defaultProps, metadata } from './metadata';
import { IChatassistantProps } from './types';
import { styles } from './style';
import websocketService from './websocketservice';
import { v4 as uuid_v4 } from 'uuid';
import { useNavigate } from 'react-router-dom';
import { isEmpty } from 'lodash';
import { agentChatWindowStyles } from './style';
import jiffyAgentLogo from './assets/jiffy_agent.png';
import jiffyAgentOpen from './assets/jiffy_agent_open.png';
import jiffyAgentClose from './assets/jiffy_agent_close.png';
import Icon, { IconCodes } from '@atomic/icon';
import { Message, TableData } from './types';
import Table from '@composite/table';
import { getWealthDomainProps, largeRowData } from './table';
import Columnchart from '@chart/columnchart';
import { columnArg } from './columnData';
import { StrictMode } from 'react';
import { pieData, pieDatas, pieDatax, SAMPLE_DATA } from './pieData';
import Piechart from '@chart/piechart';
import { ErrorBoundary } from '@base/utils-hooks';
import {
	TableContentMessage,
	SimpleTextMessage,
	UserMessage,
} from './components';
import { Chatarea } from './chatArea';
import { ActiveContentTypes, ChatService } from './constants';
import { v4 as uuidv4 } from 'uuid';
import { getConverstaionItemPayload, getConverSations } from './helper';
import ChatWindowHeader from './components/ChatWindowHeader';
import ChatHistoryList from './components/ChatHistoryList';
import ConversationManagerService from './apis/ConversationManagerService';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface ChatassistantProps extends IChatassistantProps {}

const Chatassistant: FC<ChatassistantProps> = (props: ChatassistantProps) => {
	// Framework generated code, below line registers the component
	// NOT TO BE DELETED
	setMetaData('Chatassistant', metadata);

	// Framework generated code, below line provides the contextProvider instance
	const context = useEditContext();
	const {
		apiServiceInstance,
		channel,
		targetService,
		options,
		isFullScreenChat,
		defaultMessages,
		agentJson,
		handleChatRecieveCb,
		targetServiceId,
		userId,
	} = props;
	const theme = useTheme() as ApexTheme;
	const classes = agentChatWindowStyles({ theme });
	const [isOpen, setIsOpen] = useState(false);
	const [isFullscreen, setIsFullscreen] = useState(false);
	const [messages, setMessages] = useState<Message[]>(defaultMessages || []);
	const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);
	const [isClosing, setIsClosing] = useState(false);
	const [conversationId, setConversationId] = useState<string>('');
	const subscriptionRef = useRef<{ unsubscribe: () => void } | null>(null);
	const [activeContent, setActiveContent] = useState(
		ActiveContentTypes.ChatArea
	);
	var subscription: { unsubscribe: () => void } | null = null;

	useEffect(() => {
		ConversationManagerService.init(apiServiceInstance);
		getConverSations()
	}, [])

	// Initial state with welcome message and options
	const initialState = {
		welcome: {
			content: "Hello! I'm your AI assistant. How can I help you today?",
		},
		options: options || [
			'show me the top 5 accounts',
			'Get cash value for account number ********',
			'Create a service request for "*********"',
		],
	};
	const handleNavigationMessage = useCallback((message: Message) => {
		props?.handleNavigationMessage(message);
	}, []);
	/**
	 * Handles incoming messages
	 * @param {Message} message - The received message object
	 */
	function handleReceivedMessage(message: Message, conversationId?: string) {
		if (conversationId && conversationId != message.conversationId) {
			return;
		}
		setMessages((prevMessages) => [...prevMessages, message]);
		if (message.actorType === 'Machine') {
			setIsWaitingForResponse(false);
		}
		handleNavigationMessage(message);
	}

	/**
	 * Sends a message and processes the response
	 * @param {string} messageText - The text message to send
	 */
	const handleSendMessage = async (messageText: string) => {
		if (!messageText.trim()) return;

		if(conversationId === '') {
			await handleCreateConversation();
		}

		// Add user message
		const userMessage: Message = {
			id: Date.now().toString(),
			actorType: 'Human',
			content: messageText,
			responseType: 'text',
			message: {
				$type: 'text',
				text: messageText,
			},
		};
		handleReceivedMessage(userMessage);
		// SAMPLE_DATA.forEach((message) => {
		// 	setTimeout(
		// 		() =>
		// 			handleReceivedMessage({
		// 				id: Date.now().toString(),
		// 				actorType: 'Machine',
		// 				content: messageText,
		// 				responseType: 'text',
		// 				message: {
		// 					$type: 'text',
		// 					text: JSON.stringify(JSON.stringify(message.data)),
		// 				},
		// 			}),
		// 		2000
		// 	);
		// });

		const messgaePayload = getConverstaionItemPayload(
			messageText,
			userId,
			agentJson,
			targetService,
			channel
		);

		setIsWaitingForResponse(true);
		try {
			setTimeout(async () => {
				console.log('Sending message:', messageText);
			const response = await apiServiceInstance.call(
				'conversation',
				'post',
				`/conversations/${conversationId}/items`,
				messgaePayload as unknown as Record<string, string>
			);

			console.log('Message sent successfully:', response);
			}, 2000)
		} catch (error) {
			console.error('Error sending message:', error);
			setIsWaitingForResponse(false);
		}

		// Simulate API delay
		// setTimeout(() => {
		// 	debugger
		// 	const matchingQA = findMatchingQA(messageText);

		// 	if (matchingQA) {
		// 		if (matchingQA.answer.type === 'sequence') {
		// 			const sequenceResponses = getSequenceResponses(
		// 				matchingQA.answer.sequence || []
		// 			);
		// 			sequenceResponses.forEach((response, index) => {
		// 				setTimeout(() => {
		// 					handleReceivedMessage(response);
		// 				}, index * 1000);
		// 			});
		// 		} else {
		// 			const response = generateResponse(matchingQA);
		// 			handleReceivedMessage(response);
		// 		}
		// 	} else {
		// 		const defaultResponse: Message = {
		// 			id: (Date.now() + 1).toString(),
		// 			actorType: 'Machine',
		// 			content:
		// 				"I'm sorry, I don't understand that request. Please try one of the suggested options.",
		// 			responseType: 'text',
		// 			message: {
		// 				$type: 'text',
		// 				text: "I'm sorry, I don't understand that request. Please try one of the suggested options.",
		// 			},
		// 		};
		// 		handleReceivedMessage(defaultResponse);
		// 	}
		// }, waittimer);
	};
	/**
	 * Initializes websocket connection and conversation
	 * Sets up message subscription and cleanup
	 */
	useEffect(() => {
		// let subscription: { unsubscribe: () => void } | null = null;
		// const fetchData = async () => {
		// 	try {
		// 		// const conversationResponse = await apiServiceInstance.call(
		// 		// 	'conversation',
		// 		// 	'post',
		// 		// 	'/conversations',
		// 		// 	{
		// 		// 		topic: new Date().getTime().toString(),
		// 		// 		originalChannel: 'web',
		// 		// 		...(targetService && { targetService: targetService }),
		// 		// 		...(targetServiceId && { targetServiceId: targetServiceId }),
		// 		// 	}
		// 		// );

		// 		// setConversationId(conversationResponse?.data?.id || '');
		// 		const topicResponse = await apiServiceInstance.call(
		// 			'conversation',
		// 			'get',
		// 			'/conversations/channels/inapp/topic'
		// 		);

		// 		const conversationTopic = topicResponse?.data || '';
		// 		if (subscriptionRef.current) {
		// 			subscriptionRef.current.unsubscribe();
		// 		}
		// 		const subscriptionTopic = `/topic/${conversationTopic}:${uuid_v4()}`;

		// 		subscription = websocketService.subscribeToChatChannel(
		// 			subscriptionTopic,
		// 			(data) => {
		// 				console.log('Chat Message recieved !!!!', data);
		// 				handleChatRecieveCb?.(data);
		// 				handleReceivedMessage(data, conversationResponse?.data?.id);
		// 			}
		// 		);
		// 		subscriptionRef.current = subscription;
		// 	} catch (error) {
		// 		console.error('Error details:', error);
		// 	}
		// };

		// fetchData();

		// Cleanup function
		return () => {
			subscription?.unsubscribe();

			// Check if this is a local navigation
			const isLocalNavigation =
				sessionStorage.getItem('localNavigation') === 'true';
			if (isLocalNavigation) {
				// Store messages and conversation state
				const chatState = {
					messages,
					conversationId,
					timestamp: new Date().getTime(),
				};
				sessionStorage.setItem('chatState', JSON.stringify(chatState));
			} else {
				// Clear any existing stored chat state if not local navigation
				sessionStorage.removeItem('chatState');
			}
		};
	}, []);

	const handleCreateConversation = async () => {
		try {
			const conversationResponse = await apiServiceInstance.call(
				'conversation',
				'post',
				'/conversations',
				{
					topic: new Date().getTime().toString(),
					originalChannel: 'web',
					...(targetService && { targetService: targetService }),
					...(targetServiceId && { targetServiceId: targetServiceId }),
				}
			);

			setConversationId(conversationResponse?.data?.id || '');
			const topicResponse = await apiServiceInstance.call(
				'conversation',
				'get',
				'/conversations/channels/inapp/topic'
			);

			const conversationTopic = topicResponse?.data || '';
			if (subscriptionRef.current) {
				subscriptionRef.current.unsubscribe();
			}
			const subscriptionTopic = `/topic/${conversationTopic}:${uuid_v4()}`;

			subscription = websocketService.subscribeToChatChannel(
				subscriptionTopic,
				(data) => {
					console.log('Chat Message recieved !!!!', data);
					handleChatRecieveCb?.(data);
					handleReceivedMessage(data, conversationResponse?.data?.id);
				}
			);
			subscriptionRef.current = subscription;
		} catch (error) {
			console.error('Error details:', error);
		}
	}
	/**
	 * Renders a message based on its type
	 * @param {Message} message - The message to render
	 * @returns {JSX.Element} The rendered message component
	 */

	/**
	 * Toggles the chat window open/closed state
	 */
	const handleToggleChat = () => {
		if (isOpen) {
			setIsClosing(true);
			setTimeout(() => {
				setIsOpen(false);
				setIsClosing(false);
			}, 300);
		} else {
			setIsOpen(true);
		}
	};

	/**
	 * Toggles fullscreen mode
	 */
	const handleToggleFullscreen = () => {
		setIsFullscreen(!isFullscreen);
	};

	const handleSwicthContent = useCallback((content) => {
		setActiveContent(content)
	}, [])

	const handleConversationHistoryClick = useCallback((messages: Message[], conversationId: string) => {
		setMessages(messages)
		setConversationId(conversationId)
		setActiveContent(ActiveContentTypes.ChatArea)
		console.log('messages conversationId', messages, conversationId);
		
	}, [])

	const renderContent = () => {
		if (activeContent === ActiveContentTypes.ChatArea) {
			return (
				<>
					<ChatWindowHeader
						swicthContent={handleSwicthContent}
						isFullscreen={isFullscreen}
						handleToggleFullScreen={handleToggleFullscreen}
					/>
					<Chatarea
						isFullscreen={isFullscreen}
						messages={messages}
						userId={userId}
						initialState={initialState}
						apiServiceInstance={apiServiceInstance}
						targetService={targetService}
						handleSendMessage={handleSendMessage}
						isWaitingForResponse={isWaitingForResponse}
					/>
				</>
			);
		} else if (activeContent === ActiveContentTypes.ChatHistoryList) {
			return <ChatHistoryList swicthContent={handleSwicthContent} handleConversationHistoryClick={handleConversationHistoryClick}/>;
		}
	};

	console.log('messages====', messages);
	

	return (
		<div className={classes.mainContainer}>
			{!isFullScreenChat ? (
				<div className={classes.container}>
					<button
						className={classes.floatingButton}
						onClick={handleToggleChat}
						aria-label={isOpen ? 'Close chat' : 'Open chat'}
					>
						<img
							src={isOpen ? jiffyAgentClose : jiffyAgentOpen}
							alt={isOpen ? 'Close chat' : 'Open chat'}
							height="25px"
						/>
					</button>

					{(isOpen || isClosing) && (
						<>
							<div
								className={`${classes.chatWindow} ${
									isOpen && !isClosing ? 'open' : 'closing'
								} ${isFullscreen ? 'fullscreen' : ''}`}
							>
								{/* <ChatWindowHeader /> */}
								{renderContent()}
							</div>
						</>
					)}
				</div>
			) : (
				<>{renderContent()}</>
			)}
		</div>
	);
};

Chatassistant.defaultProps = defaultProps;

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
export default configurable(Chatassistant);

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

export enum ChatassistantType {
	PRIMARY = 'primary',
	SECONDARY = 'secondary',
	TEXT = 'text',
	SUBTLE = 'subtle',
}

export enum ChatassistantSize {
	SMALL = 'Small',
	HUGE = 'Huge',
	LARGE = 'Large',
	MEDIUM = 'Medium',
}

export interface TableData {
	headers: string[];
	rows: (string | number)[][];
}

export interface Parameter {
	name: string;
	value: string;
}

export interface IChatassistantProps {
	id?: string;
	apiServiceInstance: any;
	userId: string;
	channel?: string;
	options?: string[];
	agentJson?: Record<string, any>;
	defaultMessages?: Message[];
	targetService?: string;
	isFullScreenChat?: boolean;
	handleChatRecieveCb?: (data: any) => void;
	targetServiceId?: string;
	handleNavigationMessage: (message: any) => void;
}

export interface MessageContent {
	loVs?: string[];
	prompt?: string;
	promptType?: 'text' | 'options' | 'boolean' | 'table';
	$type: 'prompt' | 'text' | 'promptr' | 'table';
	text?: string;
	tableData?: any;
	attachments?: null;
	url?: string;
	parameters?: Parameter[];
	navigationType?: 'internal' | 'external' | 'local';
}

export interface Message {
	id: string;
	actorType: 'Human' | 'Machine';
	content: string;
	responseType?: 'boolean' | 'table' | 'text' | 'options' | 'chart' | 'pie';
	conversationId?: string;
	message?: {
		url?: string;
		parameters?: any;
		$type:
			| 'text'
			| 'table'
			| 'prompt'
			| 'pie'
			| 'chart'
			| 'AgentIntialMessage'
			| 'relay'
			| 'custom';
		text?: string;
		tableData?: any;
		payload?: any;
		promptType?: 'radiolist' | 'yesno';
		loVs?: string[];
		pieData?: 'pieData' | 'pieportfolioallocation';
		prompt?: string;
		navigationType?:
			| 'internal'
			| 'external'
			| 'local'
			| 'redirectlocal'
			| undefined;
	};
	timestamp?: string;
}

export interface Page {
	id: string;
	name: string;
	label: string;
	type: string;
	artifactId: string;
	appName: string;
}

export type OptionButtonProps = {
	key: string;
	classes: any;
	handleSendMessage: (message: string) => void;
	message: any;
	handleNavigationMessage: (message: Message) => void;
};
export type SimpleTextMessageProps = {
	message: any;
	classes: any;
	handleNavigationMessage: (message: Message) => void;
};

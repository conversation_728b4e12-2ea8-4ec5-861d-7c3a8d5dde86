import { FC, useEffect, useRef, useState } from 'react';
import { IChatassistantProps, Message } from './types';
import { agentChatWindowStyles } from './style';
import { useTheme } from 'react-jss';
import { ApexTheme } from '@base/theme';
import jiffyAgentLogo from './assets/jiffy_agent.png';
import jiffyAgentOpen from './assets/jiffy_agent_open.png';
import jiffyAgentClose from './assets/jiffy_agent_close.png';
import {
	SimpleTextMessage,
	TableContentMessage,
	UserMessage,
	AgentContentMessage,
	OptionButton,
} from './components';
import { isEmpty } from 'lodash';
import { pieDatas } from './pieData';
import { ErrorBoundary } from '@base/utils-hooks';
import Piechart from '@chart/piechart';
import { columnArg } from './columnData';
import Columnchart from '@chart/columnchart';
import Table from '@composite/table';
import { getWealthDomainProps, largeRowData } from './table';
import Icon, { IconCodes } from '@base/icon';
import { ChatService } from './constants';

export interface ChatareaProps extends IChatassistantProps {
	isFullscreen: boolean;
	messages: Message[];
	initialState: {
		welcome: {
			content: string;
		};
		options: string[];
	};
	handleSendMessage: (messageText: string) => void;
	isWaitingForResponse: boolean;
}

export const Chatarea: FC<ChatareaProps> = ({
	isFullscreen,
	messages,
	initialState,
	apiServiceInstance,
	targetService,
	handleSendMessage,
	isWaitingForResponse,
	handleNavigationMessage,
}) => {
	const theme = useTheme() as ApexTheme;
	const classes = agentChatWindowStyles({ theme });
	const ERROR_MESSAGE = 'Technical error. Please try again.';
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const [inputValue, setInputValue] = useState('');

	/**
	 * Scrolls the chat window to the bottom
	 */

	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
	};

	useEffect(() => {
		scrollToBottom();
	}, [messages]);

	const isValidMessage = (textMessage: any) => {
		return !(
			isEmpty(textMessage?.structured_data) ||
			isEmpty(textMessage.structured_data?.seriesFields) ||
			isEmpty(textMessage.structured_data?.seriesFields?.[0])
		);
	};

	const renderMessage = (message: Message) => {
		if(message.actorType === 'Human' && message.message?.$type === 'relay') {
			let textMessgae = {
				content: message.message.text
			}
			return <UserMessage message={textMessgae} classes={classes} />;
		}
		if (message.actorType === 'Human') {
			return <UserMessage message={message} classes={classes} />;
		}

		if (message.message?.$type === 'custom') {
			const text = message?.message.text ?? '"[]"';
			let textJSON;
			if (
				targetService === ChatService.agentCreation ||
				targetService === ChatService.agentTest
			) {
				textJSON = text;
			} else {
				textJSON = JSON.parse(JSON.parse(text));
			}
			const textArray: any[] = Array.isArray(textJSON) ? textJSON : [textJSON];
			return textArray.map((textMessage) => {
				switch (textMessage.type) {
					case 'singlerow':
						return (
							<SimpleTextMessage
								key={message.id}
								message={textMessage}
								classes={classes}
								handleNavigationMessage={handleNavigationMessage}
							/>
						);
					case 'options':
						return (
							<OptionButton
								key={message.id}
								message={textMessage}
								classes={classes}
								handleSendMessage={handleSendMessage}
								handleNavigationMessage={handleNavigationMessage}
							/>
						);
					case 'table':
						return (
							<TableContentMessage
								message={textMessage}
								classes={classes}
								isFullscreen={isFullscreen}
								handleNavigationMessage={handleNavigationMessage}
							/>
						);
					case 'piechart': {
						if (!isValidMessage(textMessage)) {
							return (
								<SimpleTextMessage
									message={{
										text: ERROR_MESSAGE,
									}}
									classes={classes}
									handleNavigationMessage={handleNavigationMessage}
								/>
							);
						}
						const seriesFields = JSON.parse(
							JSON.stringify(pieDatas.pieData.seriesFields)
						);
						seriesFields[0] = {
							...seriesFields[0],
							...textMessage.structured_data.seriesFields[0],
							seriesProperties: {
								...pieDatas.pieData.seriesFields[0].seriesProperties,
								default: {
									...pieDatas.pieData.seriesFields[0].seriesProperties.default,
									categoryXField: textMessage.structured_data.categoryField,
									clustered: true,
									seriesName: textMessage.structured_data.categoryField,
									valueYField:
										textMessage.structured_data.seriesFields[0].field,
									xAxis: textMessage.structured_data.seriesFields[0].name,
								},
								labelSettings: {
									...pieDatas.pieData.seriesFields[0].seriesProperties
										.labelSettings,
									showLabel: isFullscreen,
								},
							},
						};
						return (
							<ErrorBoundary
								id={'CustomAssistantDemo_ErrorBoundary_Piechart'}
								componentReference={'Piechart'}
							>
								<div className={classes.messageWrapper}>
									<SimpleTextMessage
										message={textMessage}
										classes={classes}
										handleNavigationMessage={handleNavigationMessage}
									/>
									<Piechart
										{...pieDatas.pieData}
										id={message.id}
										categoryField={textMessage.structured_data.categoryField}
										seriesFields={seriesFields}
										data={textMessage.structured_data.data}
									/>
								</div>
							</ErrorBoundary>
						);
					}
					case 'barchart': {
						if (!isValidMessage(textMessage)) {
							return (
								<SimpleTextMessage
									message={{
										text: ERROR_MESSAGE,
									}}
									classes={classes}
									handleNavigationMessage={handleNavigationMessage}
								/>
							);
						}
						const seriesFields = JSON.parse(
							JSON.stringify(columnArg.seriesFields)
						);
						seriesFields[0] = {
							...seriesFields[0],
							...textMessage.structured_data.seriesFields[0],
							seriesProperties: {
								...columnArg.seriesFields[0].seriesProperties,
								default: {
									...columnArg.seriesFields[0].seriesProperties.default,
									seriesName: textMessage.structured_data.seriesFields[0].name,
									categoryXField: textMessage.structured_data.categoryField,
									valueYField:
										textMessage.structured_data.seriesFields[0].field,
									valueXField: textMessage.structured_data.categoryField,
								},
							},
						};
						const boAttributes = {
							cuvb00iuao2sa0u86840: {
								id: 'cuvb00iuao2sa0u86840',
								type: 'Date',
								key: textMessage.structured_data.categoryField,
								name: textMessage.structured_data.categoryField,
								label: '',
								dataType: 'Singlelinetext',
							},
							cuvb00quao2sa0u86860: {
								id: 'cuvb00quao2sa0u86860',
								type: 'number',
								key: textMessage.structured_data.seriesFields[0].field,
								name: textMessage.structured_data.seriesFields[0].field,
								label: '',
								dataType:
									textMessage.structured_data.seriesFields[0].dataType ??
									'Singlelinetext',
							},
						};
						return (
							<ErrorBoundary
								id={'CustomAssistantDemo_ErrorBoundary_Columnchart'}
								componentReference={'Columnchart'}
							>
								<div className={classes.messageWrapper}>
									<SimpleTextMessage
										message={textMessage}
										classes={classes}
										handleNavigationMessage={handleNavigationMessage}
									/>
									<Columnchart
										{...columnArg}
										id={message.id}
										categoryField={textMessage.structured_data.categoryField}
										seriesFields={seriesFields}
										data={textMessage.structured_data.data}
										boAttributes={boAttributes}
									></Columnchart>
								</div>
							</ErrorBoundary>
						);
					}
				}
			});
		}
		if (message.message?.$type === 'prompt' && message.message?.tableData) {
			return (
				<div className={classes.messageWrapper}>
					<div
						className={`${classes.message} assistant`}
						dangerouslySetInnerHTML={{ __html: message.content }}
					/>
					<div className={classes.tableWrapper}>
						<Table
							{...getWealthDomainProps(
								isFullscreen,
								isFullscreen,
								false,
								isFullscreen ? '518px' : '320px'
							)}
							onPaginationHandle={() => {
								return {
									apiData: largeRowData,
									headers: { 'x-jiffy-total-count': largeRowData.length },
								};
							}}
						></Table>
					</div>
				</div>
			);
		} else if (
			message.message?.$type === 'table' &&
			message.message?.tableData
		) {
			return (
				<div className={classes.messageWrapper}>
					<div
						className={`${classes.message} assistant`}
						dangerouslySetInnerHTML={{ __html: message.content }}
					/>
					<div className={classes.tableWrapper}>
						<Table
							{...getWealthDomainProps(
								isFullscreen,
								isFullscreen,
								false,
								isFullscreen ? '518px' : '320px',
								message.message.tableData.columnData
							)}
							onPaginationHandle={() => {
								return {
									apiData: message.message?.tableData.rowData,
									headers: {
										'x-jiffy-total-count':
											message.message?.tableData.rowData.length,
									},
								};
							}}
						></Table>
					</div>
				</div>
			);
		} else if (message.message?.$type === 'chart') {
			return (
				<div className={classes.messageWrapper}>
					<div
						className={`${classes.message} assistant`}
						dangerouslySetInnerHTML={{ __html: message.content }}
					/>
					{/* <Columnchart {...columnArg}></Columnchart> */}
				</div>
			);
		} else if (message.message?.$type === 'pie' && message?.message?.pieData) {
			return (
				<div className={classes.messageWrapper}>
					<div
						className={`${classes.message} assistant`}
						dangerouslySetInnerHTML={{ __html: message.content }}
					/>
					<Piechart {...pieDatas.pieData} />
				</div>
			);
		} else if (message.message?.$type === 'prompt') {
			return (
				<div className={classes.messageWrapper}>
					<div className={`${classes.message} assistant`}>
						{message.message.text || message.content}
					</div>
					{message.message.promptType === 'radiolist' &&
						message.message.loVs && (
							<div className={classes.optionsContainer}>
								{message.message.loVs.map((option: string, index: number) => (
									<button
										key={index}
										className={classes.optionButton}
										onClick={() => triggerSendMessage(option)}
									>
										{option}
									</button>
								))}
							</div>
						)}
					{message.message.promptType === 'yesno' && (
						<div className={classes.booleanContainer}>
							<button
								className={classes.booleanButton}
								onClick={() => triggerSendMessage('Yes')}
							>
								Yes
							</button>
							<button
								className={classes.booleanButton}
								onClick={() => triggerSendMessage('No')}
							>
								No
							</button>
						</div>
					)}
				</div>
			);
		} else if (message.message?.$type === 'AgentIntialMessage') {
			return (
				<AgentContentMessage message={message.message} classes={classes} />
			);
		} else if (message.message?.$type === 'relay') {
			const textMessage =
				message.message.payload.actionPlan || message.message.payload.output;
			return (
				<SimpleTextMessage
					message={{ text: textMessage }}
					classes={classes}
					handleNavigationMessage={handleNavigationMessage}
				/>
			);
		} else {
			return (
				<SimpleTextMessage
					message={message.message}
					classes={classes}
					handleNavigationMessage={handleNavigationMessage}
				/>
			);
		}

		return null;
	};

	const triggerSendMessage = (message: string) => {
		// Clear input and show loading
		setInputValue('');
		handleSendMessage(message);
	};

	return (
		<>
			<div
				className={`${classes.messagesContainer} 'fullscreen'
				}`}
			>
				<div
					className={`${classes.header} ${
						messages.length === 0 ? 'initial' : ''
					}`}
				>
					<div
						className={`${classes.headerMain} ${
							messages.length === 0 ? 'initial' : ''
						}`}
					>
						<img src={jiffyAgentLogo} alt="JIFFY.ai" className={classes.logo} />
						<span className={classes.headerTitle}>JIFFY.ai</span>
					</div>
					{messages.length === 0 && (
						<div
							className={`${classes.initialContent} ${classes.initialStyle} initial`}
						>
							<div className={classes.welcomeMessage}>
								{initialState.welcome.content}
							</div>
							<div className={classes.optionsContainer}>
								<div className={classes.optionsTitle}>Try these examples:</div>
								<div
									className={`${classes.optionsList} ${classes.alignCenter}`}
								>
									{initialState.options.map((option, index) => (
										<button
											key={index}
											className={classes.optionButtonStyle}
											onClick={() => triggerSendMessage(option)}
										>
											{option}
										</button>
									))}
								</div>
							</div>
						</div>
					)}
				</div>

				<div
					className={`${classes.messages} ${isFullscreen ? 'fullscreen' : ''}`}
				>
					{messages.length > 0 &&
						messages.map((message) => (
							<div key={message.id}>{renderMessage(message)}</div>
						))}
					{messages.length > 0 && isWaitingForResponse && (
						<div className={`${classes.messageWrapper}`}>
							<div className={`${classes.message} assistant loader`}>
								<span className={classes.loadingDots}>.</span>
							</div>
						</div>
					)}
					<div ref={messagesEndRef} />
				</div>
			</div>
			<div
				className={`${classes.inputContainer} ${
					isFullscreen ? 'fullscreen' : ''
				}`}
			>
				<div className={classes.inputWrapper}>
					<input
						type="text"
						className={classes.input}
						value={inputValue}
						onChange={(e) => setInputValue(e.target.value)}
						onKeyDown={(e) => {
							if (
								e.key === 'Enter' &&
								!isWaitingForResponse &&
								inputValue.trim()
							) {
								e.preventDefault();
								triggerSendMessage(inputValue);
							}
						}}
						placeholder="Type your message..."
					/>
					<svg
						className={classes.micIcon}
						viewBox="0 0 24 24"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<path
							d="M12 14C13.66 14 15 12.66 15 11V5C15 3.34 13.66 2 12 2C10.34 2 9 3.34 9 5V11C9 12.66 10.34 14 12 14Z"
							fill="currentColor"
						/>
						<path
							d="M17 11C17 13.76 14.76 16 12 16C9.24 16 7 13.76 7 11H5C5 14.53 7.61 17.43 11 17.92V21H13V17.92C16.39 17.43 19 14.53 19 11H17Z"
							fill="currentColor"
						/>
					</svg>
				</div>
				<button
					className={classes.actionButton}
					onClick={() => triggerSendMessage(inputValue)}
					disabled={!inputValue.trim() || isWaitingForResponse}
				>
					<Icon
						icon={IconCodes.icon_Tb_arrow_up}
						className={classes.sendIcon}
					/>
				</button>
			</div>
		</>
	);
};

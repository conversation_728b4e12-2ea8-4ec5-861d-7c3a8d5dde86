/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { ApexTheme, DeviceTypes } from '@base/theme';
import { createUseStyles } from 'react-jss';
import { DEFAULT_CURSOR_STYLE, IDataTableProps } from './types';

export const useDataTableStyles = createUseStyles<string, IDataTableProps>(
	(theme: ApexTheme) => {
		return {
			container: {
				flexDirection: 'column',
				minWidth: '300px',
				//width: '100%',
			},
			datatableHeaders: {
				justifyContent: 'space-between',
				gap: '8px',
				marginBottom: '16px',
				display: 'flex',
			},
			dataGrid: (props) => {
				const { columnData, allowColumnResizing } = props;
				const lastColumn = columnData?.length + 1;
				const lastAreaColIdx = `& .rdg-cell[aria-colindex="${lastColumn}"]`;
				return {
					overflowY: 'overlay',
					borderRadius: '8px',
					border: props.isMinimal
						? 'none'
						: `1px solid ${theme.colors.primary[200]}`,
					'& > .rel5gk2700-beta12': {
						display: props.isMinimal ? 'none' : '',
					},
					'& .rdg-cell': {
						padding: '0px',
						verticalAlign: 'middle',
						outline: 'none !important',
						borderRight: 'none',
						borderLeft: 'none',
						fontFamily: theme.fontFamily,
						fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
						fontWeight: '600',
						letterSpacing: theme.letterSpacings[DeviceTypes.Desktop].bSmall,
						textAlign: 'left',
						color: theme.colors.monochrome.body,
						display: 'flex',
						backgroundColor: props?.rowBackgroundColor?.length
							? props?.rowBackgroundColor
							: 'none',
						// boxShadow: 'none'
						'& .rdg-text-editor': {
							background: theme.colors.monochrome.offWhite,
							border: `1px solid ${theme.colors.monochrome.label}`,
							color: theme.colors.monochrome.label,
							fontWeight: '600',
							fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
							letterSpacing: theme.letterSpacings[DeviceTypes.Desktop].bSmall,
							lineHeight: theme.lineHeights[DeviceTypes.Desktop].bSmall,
						},
						'&:hover': {
							background: props?.rowHoverColor?.length
								? props?.rowHoverColor
								: props.isMinimal
								? theme.colors.monochrome.white
								: theme.colors.monochrome.bg,
						},
					},
					'& .rdg-header': {
						display: 'flex',
						borderTop: '0px',
						fontFamily: theme.fontFamily,
						fontSize: theme.fontSizes[DeviceTypes.Desktop].bMedium,
						fontWeight: '700',
						letterSpacing: theme.letterSpacings[DeviceTypes.Desktop].bMedium,
						textAlign: 'left',
						color: theme.colors.monochrome.body,
					},
					'& .rdg-header-row > .rdg-cell': {
						backgroundColor: props.isMinimal
							? theme.colors.monochrome.offWhite
							: props?.headerBackgroundColor || theme.colors.primary[100],
						padding: '0',
						transition: '0.1s',
						textTransform: props?.headerTitleCase || 'none',
						borderRadius: props?.headerBorderRadius || '0',
						borderRight: `1px solid ${theme.colors.primary[300]}`,
						...(props?.headerBorderColor &&
							props?.hederBorderWidth && {
								borderWidth: props?.hederBorderWidth,
								borderColor: props?.headerBorderColor,
								borderStyle: props?.headerBorderStyle || 'solid',
							}),
						'&:hover': {
							background: props.isMinimal
								? theme.colors.monochrome.white
								: props?.headerHoverColor || theme.colors.monochrome.offWhite,
							'& .columnHeaderLabel': {
								color:
									props?.headerFontHoverColor || theme.colors.monochrome.ash,
							},
						},
						'&:hover #addColumn': {
							visibility: `${allowColumnResizing ? 'visible' : 'hidden'}`,
						},
						'& .columnHeaderLabel': {
							fontWeight: theme.fontWeights.bold,
							color: props?.headerFontColor || theme.colors.monochrome.ash,
						},
					},
					'& .rdg-cell-action': {
						outline: 'none !important',
					},
					'& .rdg-row': {
						backgroundColor: props?.rowBackgroundColor?.length
							? props?.rowBackgroundColor
							: props.isMinimal
							? theme.colors.monochrome.offWhite
							: theme.colors.monochrome.offWhite,
						'&:hover': {
							backgroundColor: props?.rowHoverColor?.length
								? props?.rowHoverColor
								: props.isMinimal
								? theme.colors.monochrome.white
								: theme.colors.monochrome.bg,
							color: theme.colors.monochrome.body,
							cursor: props?.cursorStyle?.length
								? props?.cursorStyle
								: DEFAULT_CURSOR_STYLE,
						},
						'&:hover .column-action': {
							// position: 'absolute',
							// height: '100%',
							// backgroundColor: `${theme.colors.monochrome.offWhite}`,
							// display: 'flex',
							// justifyContent: 'right',
							// paddingRight: 8,
						},
					},
					'& .column-action': {
						position: 'absolute',
						height: '100%',
						display: 'flex',
						justifyContent:
							props?.isEditable || props?.isDeleteable ? 'center' : 'start',
						left: 2,
						// paddingRight: 8,
					},
					'& .rdg-row[aria-selected="true"]': props?.enableBulkSelect
						? {}
						: {
								backgroundColor: `${theme.colors.primary.bg} !important`,
								'& .rdg-cell': {
									borderColor: theme.colors.primary.default,
									borderTop: `0.5px solid ${theme.colors.primary.default}`,
									'& .highlighter > div': {
										color: theme.colors.primary.default,
									},
								},
								'& .rdg-cell[aria-colindex="1"]': {
									borderLeft: `0.5px solid ${theme.colors.primary.default}`,
								},
								[lastAreaColIdx]: {
									borderRight: `0.5px solid ${theme.colors.primary.default}`,
								},
						  },
					'& .rdg-header-sort-cell': {
						width: '100%',
						justifyContent: 'center',
						alignItems: 'baseline',
					},
					height: 'calc(100% - 5px)',
					width: '100%',
					'& .rdg-cell-frozen': {
						// boxShadow: 'inset 0px -1px 0px rgba(0, 0, 0, 0.08), inset 2px 0px 4px rgba(0, 0, 0, 0.08)',
						boxShadow: '2px 0 5px -2px rgba(136, 136, 136, .3)',
					},
					'& .rdg-cell-frozen-last': {
						boxShadow: props.isMinimal
							? ''
							: '2px 0px 4px 0px rgba(0, 0, 0, 0.08)',
					},
					'& .rdg-row:nth-child(odd)': {
						...(props.alternateRowHighlight && {
							backgroundColor: theme.colors.monochrome.bg,
						}),
					},
				};
			},
			gridCell: () => {
				return {
					borderLeft: '1px solid #ddd',
					fontFamily: theme.fontFamily,
					fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
					fontWeight: '600',
					letterSpacing: theme.letterSpacings[DeviceTypes.Desktop].bSmall,
					textAlign: 'left',
					color: theme.colors.monochrome.body,
					display: 'flex',
				};
			},
			gridHeader: () => {
				return {
					backgroundColor: theme.colors.monochrome.white,
					display: 'flex',
					borderTop: '0px',
					fontFamily: theme.fontFamily,
					fontSize: theme.fontSizes[DeviceTypes.Desktop].bMedium,
					fontWeight: '700',
					letterSpacing: '0.5px',
					textAlign: 'left',
					color: theme.colors.monochrome.body,
					borderRight: `2px solid ${theme.colors.primary.default}`,
				};
			},
			labelText: (props) => ({
				padding: props?.labelPadding || '0px',
				fontFamily: theme.fontFamily,
				display: ' block',
				width: '100%',
				whiteSpace: 'nowrap',
				overflow: 'hidden',
				textOverflow: 'ellipsis',
				fontWeight: props?.labelBold
					? theme.fontWeights.bold
					: theme.fontWeights.regular,
			}),
			cellFormatterContainer: (props) => {
				return {
					width: '100%',
					// justifyContent: 'left',
					alignItems: 'center',
					padding: '0px 16px',
				};
			},
			preloader: {
				backgroundColor: 'red',
				width: '176px',
				height: '16px',
				borderRadius: '4px',
				marginTop: '16px',
			},
			cellWrapperStyleClass: {
				width: '100%',
				height: 'inherit',
				display: 'inherit',
				overflow: 'hidden',
				alignItems: 'inherit',
				justifyContent: 'center',
			},
		};
	}
);

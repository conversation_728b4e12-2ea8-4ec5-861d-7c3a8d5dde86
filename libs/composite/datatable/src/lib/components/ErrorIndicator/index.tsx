import { FC } from 'react';
import Box from '@atomic/box';
import Icon from '@atomic/icon';
import Text from '@atomic/text';
import Tooltip, { TooltipPosition, TooltipType } from '@atomic/tooltip';
import { IconCodes } from '@atomic/icon';
import classNames from 'classnames';
import { ApexTheme } from '@base/theme';
import { useTheme } from 'react-jss';
import { useCustomErrorIndicatorStyles } from './style';
import { commonTextStyle } from '../Formatters/helper';

export interface ErrorIndicatorProps {
	message?: string;
	ruleBasedStyle?: any;
	cellStyles?: any;
	columnData?: any;
	dataType?: any;
	dateFormat?: any;
	timeFormat?: any;
	currency?: any;
}

const ErrorIndicator: FC<ErrorIndicatorProps> = (props) => {
	const theme: ApexTheme = useTheme();
	const componentClassNames = useCustomErrorIndicatorStyles(props as any);
	const {
		message,
		ruleBasedStyle,
		cellStyles,
		columnData,
		dataType,
		dateFormat,
		timeFormat,
		currency,
	} = props;
	const containerStyles = {
		zIndex: 99999,
		maxWidth: 'max-content',
		backgroundColor: theme.colors.danger[200],
		borderRadius: '8px',
	};
	return (
		<Tooltip
			content={
				<Box alignItems="inline">
					<Text
						{...commonTextStyle(theme, ruleBasedStyle, cellStyles)}
						dataType={columnData?.dataType || dataType}
						dateFormat={dateFormat}
						timeFormat={timeFormat}
						fontWeight={theme.fontWeights.regular}
						color={theme.colors.danger.dark}
						currency={currency}
					>
						{message || 'There is an error. Please correct it.'}
					</Text>
				</Box>
			}
			isOpen={true}
			overflowContainer={true}
			type={TooltipType.Tooltip}
			triggerOffset={-1}
			placement={TooltipPosition.BottomCenter}
			containerStyles={containerStyles}
			tooltipArrowProps={{
				backgroundColor: theme.colors.danger[200],
			}}
		>
			<Box marginLeft={'auto'}>
				<Icon
					className={classNames(componentClassNames.errorIcon)}
					icon={IconCodes.icon_Tb_alert_circle_filled}
				/>
			</Box>
		</Tooltip>
	);
};

export default ErrorIndicator;

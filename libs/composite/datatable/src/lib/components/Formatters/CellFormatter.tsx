import { FC, useMemo } from 'react';
import Box from '@atomic/box';
import { ICellFormatter, ColumnTypes } from './type';
import Preloader from '@atomic/preloader';
import { FormatterByColumnTypeMapper } from './constant';
import { getFormattedTypeAndStyle, isRightAlignedType } from './helper';
import _get from 'lodash/get';
import classNames from 'classnames';
import { formatter } from '@atomic/text';
import ErrorIndicator from '../ErrorIndicator';
export const CellFormatter: FC<ICellFormatter> = ({
	classes,
	theme,
	tableResource,
	isLoading,
	containerStyleClass,
	renderCell,
	enableActions,
	gridActions,
	rowIndex,
	selectedItems,
	handleCheckboxSelection,
	maxCharLimit,
	disableCellFormating,
	dateFormat,
	timeFormat,
	currency,
	fieldBindings,
	gridActionPosition,
	isEditable,
	onEditBtnClick,
	isDeleteable,
	onDeleteBtnClick,
	enableTooltip,
	aId,
	formError,
}) => {
	let hasError = false;
	const { row, column } = tableResource;
	if (formError?.[rowIndex]) {
		const columnKey = column?.key;
		const colName = columnKey?.split('.')?.[0];
		if (formError[rowIndex]?.[colName]) {
			hasError = true;
		}
	}
	const dataType = column?.dataType;
	let formatedVal = _get(row, column?.key);
	formatedVal = Array.isArray(formatedVal)
		? formatedVal?.join(', ')
		: formatedVal;
	if (dataType !== ColumnTypes.Checkbox && dataType !== ColumnTypes.Address) {
		// address type holds object
		formatedVal = formatedVal?.toString();
	}
	if (dataType === 'link') {
		// Association Link
		const associationVal = _get(row, column?.key?.split('.')[0]);
		if (Array.isArray(associationVal)) {
			// check if associationVal is array
			formatedVal =
				column?.showSelectedCount === true ||
				column?.showSelectedCount === 'true'
					? associationVal?.length
						? `${associationVal?.length} selected`
						: ''
					: associationVal?.map((item) => _get(item, column?.displayField));
		}
	}
	// const { type } = getColumnType(column);
	// const ruleBasedStyle = getValidStyle(tableResource);
	const { type, style, cellStyles } = getFormattedTypeAndStyle(
		column,
		formatedVal
	);
	const isExcludedColumn =
		dataType !== ColumnTypes.Select &&
		dataType !== ColumnTypes.Action &&
		dataType !== ColumnTypes.Checkbox;
	const isCellValueEmpty =
		isExcludedColumn &&
		(formatedVal === null || formatedVal === undefined || formatedVal === '');

	const isRightAligned = useMemo(() => {
		// Ensure the function call only occurs when dependencies change
		return isRightAlignedType(
			column?.dataType,
			disableCellFormating || column?.disableCellFormating
		);
	}, [column?.dataType, disableCellFormating]);

	const Component = FormatterByColumnTypeMapper[type];
	if (disableCellFormating && isExcludedColumn) {
		if (
			formatedVal === null ||
			formatedVal === undefined ||
			formatedVal === ''
		) {
			return null;
		}
		return (
			<div
				style={{
					width: '100%',
					display: 'flex',
					flexDirection: isRightAligned ? 'row-reverse' : 'row',
				}}
			>
				{formatter(formatedVal, dataType, dateFormat, timeFormat, currency)}
			</div>
		);
	}

	const ChildComponent = () => {
		if (type === ColumnTypes.Tag || type === ColumnTypes.Indicator) {
			const ChildComponent =
				FormatterByColumnTypeMapper[column.dataType] ||
				FormatterByColumnTypeMapper[ColumnTypes.Text];
			return (
				<ChildComponent
					columnType={column.dataType}
					theme={theme}
					value={formatedVal}
					ruleBasedStyle={style}
					rowData={tableResource?.row}
					cellStyles={cellStyles}
				/>
			);
		}
	};
	return (
		<Preloader
			loader={isLoading}
			classes={classes.preloader}
			childContainerStyleClass={containerStyleClass}
		>
			{!renderCell ? (
				<Box
					className={classNames(
						classes.cellFormatterContainer,
						'highlighter',
						`column-${column.dataType}`
					)}
					display="flex"
					flexDirection={isRightAligned ? 'row-reverse' : 'row'}
					data-aId={`${aId}${rowIndex}${column?.columnIndex}`}
					border={hasError && `1px solid ${theme.colors.danger['500']}`}
					backgroundColor={hasError && `${theme.colors.danger.bg}`}
				>
					{!isCellValueEmpty && (
						<Component
							theme={theme}
							value={formatedVal}
							ruleBasedStyle={style}
							gridActions={
								enableActions && column.dataType === 'action' ? gridActions : []
							}
							handleCheckboxSelection={handleCheckboxSelection}
							selectedItems={selectedItems}
							rowData={tableResource?.row}
							columnData={tableResource?.column}
							rowIndex={rowIndex}
							maxCharLimit={maxCharLimit}
							dateFormat={dateFormat}
							timeFormat={timeFormat}
							currency={currency}
							fieldBindings={fieldBindings}
							cellStyles={cellStyles}
							gridActionPosition={gridActionPosition}
							isEditable={isEditable}
							onEditBtnClick={onEditBtnClick}
							isDeleteable={isDeleteable}
							onDeleteBtnClick={onDeleteBtnClick}
							enableTooltip={enableTooltip}
							errors={formError?.[rowIndex] || null}
						>
							<ChildComponent />
						</Component>
					)}
					{hasError && (
						<ErrorIndicator
							message={_get(formError?.[rowIndex], column?.key)?.message}
							ruleBasedStyle={style}
							cellStyles={cellStyles}
							columnData={tableResource?.column}
							dataType={dataType}
							dateFormat={dateFormat}
							timeFormat={timeFormat}
							currency={currency}
						/>
					)}
				</Box>
			) : (
				renderCell({
					rowData: tableResource?.row,
					column: tableResource?.column,
					fallbackComponent: (
						<Component
							theme={theme}
							value={formatedVal}
							ruleBasedStyle={style}
							cellStyles={cellStyles}
							gridActions={
								enableActions && column.key === 'action' ? gridActions : []
							}
							maxCharLimit={maxCharLimit}
						/>
					),
				})
			)}
		</Preloader>
	);
};

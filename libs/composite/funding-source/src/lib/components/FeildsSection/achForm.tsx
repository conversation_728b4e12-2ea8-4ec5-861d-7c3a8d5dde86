import {
	createRef,
	FC,
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import Box from '@atomic/box';
import Text from '@atomic/text';
import { IFeildSectionTypes } from './types';
import { styles } from './style';
import classNames from 'classnames';
import { get, isEmpty, isString, isObject } from 'lodash';

import { workflowApiUrlForRoutingNumberBasedBank } from '../../constants';

import { getFeildRenders } from './helper';
import RenderFeildsLoop from './renderFeildsLoop';
const accordionIds = [
	{ id: '0', key: 'firstAccordian' },
	{ id: '1', key: 'additionalBankAccountowners' },
	{ id: '2', key: 'typeOfTransfer' },
	{ id: '3', key: 'transferInstructions' },
	{ id: '4', key: 'partialAccountTransfer' },
];
const AchForm: FC<IFeildSectionTypes> = (props) => {
	const componentStyles = styles(props);
	const {
		onChangeFeild,
		formKeys,
		formValues = {},
		error = {},
		stateList,
		countryList,
		nameAppending,
		sourceMetaData,
		enums,
		apiService,
		triggerFormUpdate,
	} = props;
	const fieldProps = useMemo(
		() => sourceMetaData?.fieldProps || {},
		[sourceMetaData]
	);
	const feildsMetaData = useMemo(
		() => sourceMetaData?.fieldConfigs || {},
		[sourceMetaData]
	);
	const accordianMetaData = useMemo(
		() => sourceMetaData?.accordianConfigs || {},
		[sourceMetaData]
	);
	const [expandedItem, setExpandedItem] = useState(accordionIds[0]?.id);
	const refsById = useRef({});
	const getRefById = useCallback((id) => {
		if (!refsById.current[id]) {
			refsById.current[id] = createRef();
		}
		return refsById.current[id];
	}, []);

	const detailsTobeProvidedClearKeys = useMemo(() => {
		return Object.values(formKeys || {}).reduce((acc: any, formKey: any) => {
			const sourceTypeValue = (sourceMetaData?.topLevel || [])[0]?.key;
			if (
				formKey.key !== sourceTypeValue &&
				formKey.key !==
					(feildsMetaData[formKeys.detailsToBeProvided.id]?.key ||
						formKeys.detailsToBeProvided.key)
			) {
				acc.push(feildsMetaData[formKeys[formKey.id]]?.key || formKey.key);
			}
			return acc;
		}, []);
	}, [formKeys, feildsMetaData]);

	const getFormKey = useCallback(
		(idKey: any) => {
			return feildsMetaData[idKey.id]?.key || idKey.key;
		},
		[feildsMetaData]
	);

	const handleGetBankDetails = useCallback(
		async (value: any) => {
			try {
				const bankDetails = await apiService.call(
					'workflowModel',
					'post',
					workflowApiUrlForRoutingNumberBasedBank,
					{ arguments: { aba: value } }
				);

				if (bankDetails?.data?.bankName) {
					onChangeFeild(
						bankDetails?.data?.bankName,
						getFormKey(formKeys.bankName),
						[],
						true
					);
				}
			} catch (error) {
				console.error('Error fetching bank name:', error);
			}
		},
		[apiService, getFormKey, onChangeFeild, formKeys]
	);

	const handleChange = useCallback(
		(
			value: any,
			key: any,
			clearKeys?: any[],
			loopIndex?: number,
			loopKey?: string,
			triggerFormUpdate?: boolean,
			triggerFormValueUpdate?: boolean
		) => {
			onChangeFeild(
				value,
				key,
				clearKeys,
				triggerFormUpdate,
				triggerFormValueUpdate
			);
			if (
				key === getFormKey(formKeys.routingNumber) &&
				value?.length === 9 &&
				sourceMetaData?.enableRoutingNumberBasedBank
			) {
				handleGetBankDetails(value);
			}
		},
		[onChangeFeild, getFormKey, handleGetBankDetails, formKeys]
	);
	useEffect(() => {
		const usedToFundProductValue = get(
			formValues,
			getFormKey(formKeys.usedToFundProduct)
		);
		const personalOrCorporateAccountValue = get(
			formValues,
			getFormKey(formKeys.personalOrCorporateAccount)
		);
		if (
			shouldShowField([formKeys.usedToFundProduct.id]) &&
			usedToFundProductValue !== false &&
			!usedToFundProductValue
		) {
			handleChange(
				true,
				feildsMetaData[formKeys.usedToFundProduct.id]?.key ||
					formKeys.usedToFundProduct.key,
				[],
				-1,
				null,
				true
			);
		}
		if (
			shouldShowField([formKeys.personalOrCorporateAccount.id]) &&
			get(formValues, getFormKey(formKeys.personalOrCorporateAccount)) !==
				false &&
			!personalOrCorporateAccountValue
		) {
			handleChange(
				true,
				getFormKey(formKeys.personalOrCorporateAccount),
				[],
				-1,
				null,
				true
			);
		}
	}, []);

	const getErrorSlice = useCallback(
		(key, oneToMenyKey, oneToManyLoopIndex) => {
			const errorMessage =
				oneToMenyKey && oneToManyLoopIndex >= 0
					? get(error, `${oneToMenyKey}.${oneToManyLoopIndex}.${key}`)
					: get(error, key);
			return !isEmpty(errorMessage)
				? isString(errorMessage)
					? { message: errorMessage }
					: isObject(errorMessage) &&
					  (errorMessage as any)?.message &&
					  errorMessage
				: null;
		},
		[error]
	);
	const renderField = useCallback(
		(
			Component: React.ElementType,
			props: Record<string, any>,
			noWrapperRequired: boolean,
			additionalProps: any
		) => {
			const { minWidth, maxWidth } = additionalProps || {};
			return noWrapperRequired ? (
				<Component {...props} />
			) : (
				<Box
					className={classNames(componentStyles.fieldInputWrapper)}
					minWidth={minWidth}
					maxWidth={maxWidth}
				>
					<Component {...props} />
				</Box>
			);
		},
		[componentStyles.fieldInputWrapper]
	);

	const renderFeildComponents: any = useMemo(() => {
		return getFeildRenders(
			renderField,
			formValues,
			handleChange,
			getErrorSlice,
			countryList,
			stateList,
			nameAppending,
			getRefById,
			triggerFormUpdate,
			fieldProps
		);
	}, [
		renderField,
		fieldProps,
		formValues,
		handleChange,
		getErrorSlice,
		countryList,
		stateList,
		nameAppending,
		getRefById,
		triggerFormUpdate,
	]);
	const shouldShowField = useCallback(
		(keys: string[]) => {
			return keys.some((key) => feildsMetaData[key]?.on !== false);
		},
		[feildsMetaData]
	);

	const getFormKeysArray = useCallback(
		(ids: Array<any>) => {
			return ids.map((itm: any) => feildsMetaData[itm.id]?.key || itm.key);
		},
		[feildsMetaData]
	);
	const toogleAccordion = (id) => {
		setExpandedItem(expandedItem === id ? '' : id);
	};
	const getLabelText = useCallback(
		(idKey: any, label: string) => {
			return feildsMetaData[idKey.id]?.text || label;
		},
		[feildsMetaData]
	);
	const achLoopDataMap = [
		{
			accordianRequired: false,
			accordinaData: {},
			feildsData: [
				{
					item: formKeys.personalOrCorporateAccount,
					renderType: 'renderToggle',
					text: 'Personal Account',
					feildProps: [],
				},
				{
					item: formKeys.usedToFundProduct,
					renderType: 'renderDropdown',
					text: 'Used to fund product',
					feildProps: [
						enums[formKeys.usedToFundProduct.enum],
						getFormKeysArray([formKeys.usedForAccountFundingAmount]),
						false,
						false,
						{},
						get(formValues, getFormKey(formKeys.usedToFundProduct)) !== true
							? { wrapperClasss: componentStyles.halfFlex }
							: {},
					],
				},
				{
					item: formKeys.usedForAccountFundingAmount,
					renderType: 'renderCurrency',
					text: 'Amount',
					feildProps: [],
					conditional:
						shouldShowField([formKeys.usedToFundProduct.id]) &&
						get(formValues, getFormKey(formKeys.usedToFundProduct)) === true,
				},
				{
					item: formKeys.amount,
					renderType: 'renderCurrency',
					text: 'Amount',
					feildProps: [
						{
							showAsMandatory: false,
						},
					],
				},
				{
					item: formKeys.useForBankAccountTransfers,
					renderType: 'renderDropdown',
					text: 'Use for bank account transfers',
					feildProps: [enums[formKeys.useForBankAccountTransfers.enum], []],
					conditional:
						get(formValues, getFormKey(formKeys.usedToFundProduct)) === false,
				},

				{
					item: formKeys.routingNumber,
					renderType: 'renderFormattedInput',
					text: 'Bank routing number',
					feildProps: ['*********'],
				},
				{
					item: formKeys.bankName,
					renderType: 'renderInput',
					text: 'Bank name',
					feildProps: [{}, {}],
				},
				{
					item: formKeys.bankAccountNumber,
					renderType: 'renderFormattedInput',
					text: 'Bank account number',
					feildProps: ['*************************'],
				},
				{
					item: formKeys.bankAccountTitle,
					renderType: 'renderInput',
					text: 'Account title',
					feildProps: [],
				},
			],
		},
		{
			accordianRequired: true,
			accordinaData: {
				key: accordionIds[1]?.key,
				isExpanded: expandedItem === accordionIds[1].id,
				heading: 'Additional Bank Account Owners',
				onIconClick: () => toogleAccordion(accordionIds[1]?.id),
			},
			feildsData: [
				{
					item: formKeys.areYouAnOwnerOnTheBankAccount,
					renderType: 'renderRadioGroup',
					text: 'Are you an owner on the Bank Account?',
					feildProps: [
						enums[formKeys.areYouAnOwnerOnTheBankAccount.enum],
						getFormKeysArray([
							formKeys.otherOwnerName,
							formKeys.otherOwnerEmail,
						]),
					],
				},
				{
					item: formKeys.bankAccountHasOtherOwners,
					renderType: 'renderRadioGroup',
					text: 'Are any of the bank account owners NOT owners of this account?',
					feildProps: [
						enums[formKeys.bankAccountHasOtherOwners.enum],
						getFormKeysArray([
							formKeys.otherOwnerName,
							formKeys.otherOwnerEmail,
						]),
					],
				},
				{
					item: formKeys.otherOwnerName,
					renderType: 'renderInput',
					text: 'Other owner name',
					feildProps: [],
					conditional:
						get(formValues, formKeys.bankAccountHasOtherOwners.key) === true ||
						get(formValues, formKeys.areYouAnOwnerOnTheBankAccount.key) ===
							false,
				},
				{
					item: formKeys.otherOwnerEmail,
					renderType: 'renderEmail',
					text: 'Other owner email',
					feildProps: [],
					conditional:
						get(formValues, formKeys.bankAccountHasOtherOwners.key) === true ||
						get(formValues, formKeys.areYouAnOwnerOnTheBankAccount.key) ===
							false,
				},
				{
					item: formKeys.areTheNameAndTaxIdSame,
					renderType: 'renderRadioGroup',
					text: `Are the names and Tax ID's on the finanical institution identical to the new account?`,
					feildProps: [enums[formKeys.areTheNameAndTaxIdSame.enum], []],
				},
			],
		},
		{
			accordianRequired: true,
			accordinaData: {
				key: accordionIds[2]?.key,
				isExpanded: expandedItem === accordionIds[2].id,
				heading: 'Type of Transfer',
				onIconClick: () => toogleAccordion(accordionIds[2]?.id),
			},
			feildsData: [
				{
					item: formKeys.doYouWantToScheduleARecurringTransfer,
					renderType: 'renderRadioGroup',
					text: 'Do you want to schedule a recurring transfer?',
					feildProps: [
						enums[formKeys.doYouWantToScheduleARecurringTransfer.enum],
						getFormKeysArray([
							formKeys.transferDirection,
							formKeys.recurringTransferAmount,
							formKeys.recurringTransferFrequency,
						]),
					],
				},
				{
					item: formKeys.transferDirection,
					renderType: 'renderDropdown',
					text: 'Transfer direction',
					feildProps: [enums[formKeys.transferDirection.enum], []],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.doYouWantToScheduleARecurringTransfer)
						) === true,
				},
				{
					item: formKeys.recurringTransferAmount,
					renderType: 'renderCurrency',
					text: 'Amount',
					feildProps: [],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.doYouWantToScheduleARecurringTransfer)
						) === true,
				},
				{
					item: formKeys.recurringTransferFrequency,
					renderType: 'renderDropdown',
					text: 'Frequency',
					feildProps: [enums[formKeys.recurringTransferFrequency.enum], []],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.doYouWantToScheduleARecurringTransfer)
						) === true,
				},
				{
					item: formKeys.recurringTransferStartDate,
					renderType: 'renderDate',
					text: 'First start date',
					feildProps: [],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.doYouWantToScheduleARecurringTransfer)
						) === true,
				},
				{
					item: formKeys.recurringTransferSecondDate,
					renderType: 'renderDate',
					text: 'Second date',
					feildProps: [],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.doYouWantToScheduleARecurringTransfer)
						) === true,
				},
				{
					item: formKeys.recurringTransferEndDate,
					renderType: 'renderDate',
					text: 'End date for recurring',
					feildProps: [],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.doYouWantToScheduleARecurringTransfer)
						) === true,
				},
			],
		},
	];
	return (
		<>
			<Box
				className={classNames(
					componentStyles.spaceBetweenSection,
					componentStyles.sectionMarginTop,
					componentStyles.sectionMarginBottom16
				)}
			>
				{shouldShowField([formKeys.detailsToBeProvided.id]) && (
					<Box
						className={`${classNames(
							componentStyles.InformationToBeSuppliedLater
						)} label`}
					>
						<Text>
							{getLabelText(
								formKeys.detailsToBeProvided,
								'Information to be supplied later'
							)}
						</Text>
						{renderFeildComponents.renderCheckbox(
							getFormKey(formKeys.detailsToBeProvided),
							detailsTobeProvidedClearKeys
						)}
					</Box>
				)}
				{shouldShowField([formKeys.bankAccountType.id]) &&
					!get(formValues, getFormKey(formKeys.detailsToBeProvided)) && (
						<Box>
							{renderFeildComponents.renderRadioGroup(
								getFormKey(formKeys.bankAccountType),
								getLabelText(formKeys.bankAccountType, 'Account type'),
								enums[formKeys.bankAccountType.enum],
								[],
								{ flexDirection: 'row', gap: '10px', alignItems: 'end' },
								{ separatorStyles: { paddingBottom: '0px' } }
							)}
						</Box>
					)}
			</Box>
			{!get(formValues, getFormKey(formKeys.detailsToBeProvided)) && (
				<RenderFeildsLoop
					loopData={achLoopDataMap as any}
					renderFeildComponents={renderFeildComponents}
					feildsMetaData={feildsMetaData}
					accordianMetaData={accordianMetaData}
					getErrorSlice={getErrorSlice}
					onAddRow={() => {}}
					onDeleteRow={() => {}}
				/>
			)}
		</>
	);
};
export default AchForm;

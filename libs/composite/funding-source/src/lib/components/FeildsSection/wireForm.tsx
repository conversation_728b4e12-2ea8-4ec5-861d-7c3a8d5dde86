import { createRef, FC, useCallback, useEffect, useMemo, useRef } from 'react';
import Box from '@atomic/box';
import { IFeildSectionTypes } from './types';
import { styles } from './style';
import classNames from 'classnames';
import { get, isEmpty, set, isString, isObject } from 'lodash';
import { getFeildRenders } from './helper';
import { v4 as uuid } from 'uuid';
import RenderFeildsLoop from './renderFeildsLoop';

const Wire: FC<IFeildSectionTypes> = (props) => {
	const componentStyles = styles(props);
	const {
		onChangeFeild,
		formKeys,
		formValues = {},
		error = {},
		countryList = [],
		stateList = [],
		nameAppending,
		sourceMetaData,
		enums,
		triggerFormUpdate,
	} = props;
	const fieldProps = useMemo(
		() => sourceMetaData?.fieldProps || {},
		[sourceMetaData]
	);
	const feildsMetaData = useMemo(
		() => sourceMetaData?.fieldConfigs || {},
		[sourceMetaData]
	);
	const refsById = useRef({});
	const getRefById = (id) => {
		if (!refsById.current[id]) {
			refsById.current[id] = createRef();
		}
		return refsById.current[id];
	};
	const handleChange = useCallback(
		(
			value: any,
			key: any,
			clearKeys?: any[],
			loopIndex?: number,
			loopKey?: string,
			triggerFormUpdate?: boolean,
			triggerFormValueUpdate?: boolean
		) => {
			let curData = value;
			let curKey = key;
			if (loopIndex >= 0) {
				curData = get(formValues, loopKey, [{ key: uuid() }]);
				curData[loopIndex] = set(curData[loopIndex], key, value);
				curKey = loopKey;
			}
			onChangeFeild(
				curData,
				curKey,
				clearKeys,
				triggerFormUpdate,
				triggerFormValueUpdate
			);
		},
		[onChangeFeild, formValues, formKeys, feildsMetaData]
	);

	const getErrorSlice = useCallback(
		(key, oneToMenyKey, oneToManyLoopIndex) => {
			const errorMessage =
				oneToMenyKey && oneToManyLoopIndex >= 0
					? get(error, `${oneToMenyKey}.${oneToManyLoopIndex}.${key}`)
					: get(error, key);
			return !isEmpty(errorMessage)
				? isString(errorMessage)
					? { message: errorMessage }
					: isObject(errorMessage) &&
					  (errorMessage as any)?.message &&
					  errorMessage
				: null;
		},
		[error]
	);

	const renderField = useCallback(
		(
			Component: React.ElementType,
			props: Record<string, any>,
			noWrapperRequired: boolean,
			additionalProps: any = {}
		) => {
			const { wrapperClasss, minWidth, maxWidth } = additionalProps || {};
			return noWrapperRequired ? (
				<Component {...props} />
			) : (
				<Box
					className={
						wrapperClasss
							? classNames(componentStyles.fieldInputWrapper, wrapperClasss)
							: classNames(componentStyles.fieldInputWrapper)
					}
					maxWidth={maxWidth}
					minWidth={minWidth}
				>
					<Component {...props} />
				</Box>
			);
		},
		[componentStyles.fieldInputWrapper]
	);

	const shouldShowField = useCallback(
		(keys: string[]) => {
			return keys.some((key) => feildsMetaData[key]?.on !== false);
		},
		[feildsMetaData]
	);
	const getFormKeysArray = useCallback(
		(ids: Array<any>) => {
			return ids.map((itm: any) => feildsMetaData[itm.id]?.key || itm.key);
		},
		[feildsMetaData]
	);
	const getFormKey = useCallback(
		(idKey: any) => {
			return feildsMetaData[idKey.id]?.key || idKey.key;
		},
		[feildsMetaData]
	);
	useEffect(() => {
		const usedForAccountFundingValue = get(
			formValues,
			getFormKey(formKeys.usedForAccountFunding)
		);
		if (
			shouldShowField([formKeys.usedForAccountFunding.id]) &&
			usedForAccountFundingValue !== false &&
			!usedForAccountFundingValue
		) {
			handleChange(
				true,
				getFormKey(formKeys.usedForAccountFunding),
				[],
				-1,
				'',
				true
			);
		}
	}, []);
	const renderFeildComponents: any = useMemo(() => {
		return getFeildRenders(
			renderField,
			formValues,
			handleChange,
			getErrorSlice,
			countryList,
			stateList,
			nameAppending,
			getRefById,
			triggerFormUpdate,
			fieldProps
		);
	}, [
		renderField,
		fieldProps,
		formValues,
		handleChange,
		getErrorSlice,
		countryList,
		stateList,
		nameAppending,
		triggerFormUpdate,
	]);
	const loopData = [
		{
			accordianRequired: false,
			accordinaData: {},
			feildsData: [
				{
					text: 'Used to fund product',
					renderType: 'renderDropdown',
					item: formKeys.usedForAccountFunding,
					feildProps: [
						enums[formKeys.usedForAccountFunding.enum],
						getFormKeysArray([formKeys.usedForAccountFundingAmount]),
					],
				},
				{
					text: 'Amount',
					renderType: 'renderCurrency',
					item: formKeys.usedForAccountFundingAmount,
					feildProps: [
						{
							name: 'wireAmount2',
						},
					],
					conditional:
						get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
						true,
				},
				{
					text: 'Amount',
					renderType: 'renderCurrency',
					item: formKeys.amount,
					feildProps: [
						{
							name: 'wireAmount',
							showAsMandatory: false,
						},
					],
				},
			],
		},
	];
	return (
		<RenderFeildsLoop
			loopData={loopData as any}
			renderFeildComponents={renderFeildComponents}
			feildsMetaData={feildsMetaData}
			getErrorSlice={getErrorSlice}
			onAddRow={() => {}}
		/>
	);
};
export default Wire;

import { createRef, FC, useCallback, useMemo, useRef, useState } from 'react';
import Box from '@atomic/box';
import { IFeildSectionTypes } from './types';
import { styles } from './style';
import classNames from 'classnames';
import { get, isEmpty, isObject, isString, set } from 'lodash';
import { getFeildRenders, isNotEmptyAndNull } from './helper';
import Text from '@atomic/text';
import Icon from '@atomic/icon';
import { v4 as uuid } from 'uuid';
import {
	transferTypeValueOfBrokerageTrustCompanyPartialAccountTransfer,
	transferTypeValueOfMutualFundCompanyTransferLiquidation,
	transferAllOptions,
	transferOptionsValueOfTransferInKindOptions,
	transferAllOptionsValueOfShareAmount,
	transferOptionsValueOfLiquidateOptions,
	transferTypeOpionsIRAValueofBankOrCreditUnionTransfer,
	bankTransferTypeOptionsValueOfTransferPartialCash,
	bankTransferTypeOptionsValueOfLiquidateCDAtMaturityAndTransferCash,
	transferTypeValueOfAnnuityPartialLiquidation,
	transferTypeValueOfTransferAgentTransfer,
	otherAccountType,
	brokerageOrTrustCompanyOptionsPartialAccountTransfer,
	externalTransferTopLevelCertificateOfDepositTransfer,
	externalTransferTopLevelBrokerageTrustCompanyPartialAccountTransfer,
	externalTransferCeritificateOfDepositeLiquidateAndTransferOptionFullAtMaturityLiquidateCDAndTransferAllCash,
	externalTransferCeritificateOfDepositeLiquidateAndTransferOptionPartialAtMaturityLiquidateCDAndTransferCash,
	externalTransferTopLevelAnnuityCompanyTransfer,
	externalTransferAnnuityTransferOptionsFullMeturity,
	externalTransferAnnuityTransferOptionsPartialMeturity,
	externalTransferTopLevelMutualFundCompanyTransfer,
} from '../../constants';
import { IconCodes } from '@base/icon';

import AccordianWrapper from './accordianWrapper';
import { ApexTheme } from '@base/theme';
import { useTheme } from 'react-jss';
import Tooltip from '@atomic/tooltip';
import { TooltipType } from '@atomic/tooltip';
import { TooltipPosition } from '@atomic/tooltip';

const accordionIds = [
	{ id: '0', key: 'receivingFirmInformation' },
	{ id: '1', key: 'deliveringAccountInformation' },
	{ id: '2', key: 'transferInstructions' },
	{ id: '3', key: 'accountRegistrationDifferences' },
	{ id: '4', key: 'oneAndTheSamePersonCertification' },
];
const ExtAcat: FC<IFeildSectionTypes> = (props) => {
	const componentStyles = styles(props);
	const {
		onChangeFeild,
		formKeys,
		formValues = {},
		error = {},
		isIRA,
		ssn,
		countryList = [],
		stateList = [],
		nameAppending,
		sourceMetaData,
		enums,
		triggerFormUpdate,
	} = props;
	const fieldProps = useMemo(
		() => sourceMetaData?.fieldProps || {},
		[sourceMetaData]
	);
	const feildsMetaData = useMemo(
		() => sourceMetaData?.fieldConfigs || {},
		[sourceMetaData]
	);
	const accordianMetaData = useMemo(
		() => sourceMetaData?.accordianConfigs || {},
		[sourceMetaData]
	);

	const refsById = useRef({});

	const getRefById = (id) => {
		if (!refsById.current[id]) {
			refsById.current[id] = createRef();
		}
		return refsById.current[id];
	};
	const theme: ApexTheme = useTheme();
	const [expandedItem, setExpandedItem] = useState(accordionIds[1]?.id);

	const detailsTobeProvidedClearKeys = useMemo(() => {
		return Object.values(formKeys || {}).reduce((acc: any, formKey: any) => {
			const sourceTypeValue = (sourceMetaData?.topLevel || [])[0]?.key;
			if (
				formKey.key !== sourceTypeValue &&
				formKey.key !==
					(feildsMetaData[formKeys.detailsToBeProvided.id]?.key ||
						formKeys.detailsToBeProvided.key)
			) {
				acc.push(feildsMetaData[formKeys[formKey.id]]?.key || formKey.key);
			}
			return acc;
		}, []);
	}, [formKeys, feildsMetaData]);
	const curLoopRowValue = get(
		formValues,
		feildsMetaData[formKeys.loopingRowKey.id]?.key ||
			formKeys.loopingRowKey.key,
		[{ key: uuid() }]
	);
	const curLoopRowData =
		Array.isArray(curLoopRowValue) && curLoopRowValue.length === 0
			? [{ key: uuid() }]
			: curLoopRowValue;

	const curLoop2RowValue = get(
		formValues,
		feildsMetaData[formKeys.loppingRow2Key.id]?.key ||
			formKeys.loppingRow2Key.key,
		[{ key: uuid() }]
	);
	const curLoop2RowData =
		Array.isArray(curLoop2RowValue) && curLoop2RowValue.length === 0
			? [{ key: uuid() }]
			: curLoop2RowValue;

	const handleChange = useCallback(
		(
			value: any,
			key: any,
			clearKeys?: any[],
			loopIndex?: number,
			loopKey?: string,
			triggerFormUpdate?: boolean,
			triggerFormValueUpdate?: boolean
		) => {
			let curData = value;
			let curKey = key;
			if (loopIndex >= 0) {
				curData = get(formValues, loopKey, [{ key: uuid() }]);
				curData[loopIndex] = set(
					curData[loopIndex] || { key: uuid() },
					key,
					value
				);
				curKey = loopKey;
			}
			onChangeFeild(
				curData,
				curKey,
				clearKeys,
				triggerFormUpdate,
				triggerFormValueUpdate
			);
		},
		[onChangeFeild, formValues]
	);
	const toogleAccordion = (id) => {
		setExpandedItem(expandedItem === id ? '' : id);
	};
	const getErrorSlice = useCallback(
		(key, oneToMenyKey, oneToManyLoopIndex) => {
			const errorMessage =
				oneToMenyKey && oneToManyLoopIndex >= 0
					? get(error, `${oneToMenyKey}.${oneToManyLoopIndex}.${key}`)
					: get(error, key);

			return !isEmpty(errorMessage)
				? isString(errorMessage)
					? { message: errorMessage }
					: isObject(errorMessage) &&
					  (errorMessage as any)?.message &&
					  errorMessage
				: null;
		},
		[error]
	);
	const onAddRow = useCallback(
		(loopData, loopKey) => {
			const curValues = [...loopData];
			curValues.push({ key: uuid() });
			handleChange(curValues, loopKey);
		},
		[handleChange]
	);
	const onDeleteRow = useCallback(
		(index, loopData, loopKey) => {
			const curValues = [...loopData];
			curValues.splice(index, 1);
			handleChange(curValues, loopKey, [], -1, '', true, true);
		},
		[handleChange]
	);
	const shouldShowField = useCallback(
		(keys: string[]) => {
			return keys.some((key) => feildsMetaData[key]?.on !== false);
		},
		[feildsMetaData]
	);
	const shouldShowAccordian = useCallback(
		(index: number) => {
			return accordianMetaData[accordionIds[index]?.key]?.on !== false;
		},
		[accordianMetaData]
	);
	const selectedTransferType = useMemo(() => {
		const sourceTypeValue = (sourceMetaData?.topLevel || [])[0]?.key;
		return get(formValues, sourceTypeValue || '');
	}, [formValues, sourceMetaData]);
	const getFormKeysArray = useCallback(
		(ids: Array<any>) => {
			return ids.map((itm: any) => feildsMetaData[itm.id]?.key || itm.key);
		},
		[feildsMetaData]
	);
	const getFormKey = useCallback(
		(idKey: any) => {
			return feildsMetaData[idKey.id]?.key || idKey.key;
		},
		[feildsMetaData]
	);
	const getLabelText = useCallback(
		(idKey: any, label: string) => {
			return feildsMetaData[idKey.id]?.text || label;
		},
		[feildsMetaData]
	);
	const renderField = useCallback(
		(
			Component: React.ElementType,
			props: Record<string, any>,
			noWrapperRequired: boolean,
			additionalProps: any = {}
		) => {
			const { minWidth, maxWidth, doubleWrapperRequired } =
				additionalProps || {};

			return noWrapperRequired ? (
				<Component {...props} />
			) : doubleWrapperRequired ? (
				<Box display="block" width="100%">
					<Component {...props} />
				</Box>
			) : (
				<Box
					className={classNames(componentStyles.fieldInputWrapper)}
					minWidth={minWidth}
					maxWidth={maxWidth}
				>
					<Component {...props} />
				</Box>
			);
		},
		[componentStyles.fieldInputWrapper]
	);
	const renderFeildComponents: any = useMemo(() => {
		return getFeildRenders(
			renderField,
			formValues,
			handleChange,
			getErrorSlice,
			countryList,
			stateList,
			nameAppending,
			getRefById,
			triggerFormUpdate,
			fieldProps
		);
	}, [
		renderField,
		fieldProps,
		formValues,
		handleChange,
		getErrorSlice,
		countryList,
		stateList,
		nameAppending,
		triggerFormUpdate,
	]);
	const checkMaximumCountReached = useCallback(
		(loopItem) => {
			return feildsMetaData[loopItem.id]?.count || 20;
		},
		[formValues]
	);
	const getEnumValue = (itm) => {
		return enums[feildsMetaData[itm.id]?.enum || itm.enum];
	};
	const checkValueOfLoopHasValue = useCallback((data = [], loopIndex) => {
		const currentLoopData = data[loopIndex] || {};

		return Object.keys(currentLoopData).some(
			(keyName) =>
				keyName !== 'key' &&
				get(currentLoopData, keyName) !== '' &&
				get(currentLoopData, keyName) !== null
		);
	}, []);
	return (
		<Box
			className={classNames(
				componentStyles.feildsWrapper,
				componentStyles.sectionMarginBottom
			)}
		>
			{shouldShowField([formKeys.detailsToBeProvided.id]) && (
				<Box
					className={`${classNames(
						componentStyles.InformationToBeSuppliedLater,
						componentStyles.sectionMarginTop
					)} label`}
				>
					<Text>
						{getLabelText(
							formKeys.detailsToBeProvided,
							'Information to be supplied later'
						)}
					</Text>
					{renderFeildComponents.renderCheckbox(
						getFormKey(formKeys.detailsToBeProvided),
						detailsTobeProvidedClearKeys
					)}
				</Box>
			)}
			{!get(formValues, getFormKey(formKeys.detailsToBeProvided)) && (
				<>
					{shouldShowAccordian(0) && (
						<AccordianWrapper
							isExpanded={expandedItem === accordionIds[0]?.id}
							heading={'Receiving Firm Information'}
							onIconClick={() => toogleAccordion(accordionIds[0]?.id)}
						>
							<Box
								className={classNames(
									componentStyles.staticValueWrapper,
									componentStyles.sectionMarginTop
								)}
							>
								{sourceMetaData?.firmInformation.map((staticValue) => {
									return (
										<Box className={classNames(componentStyles.staticBox)}>
											<Icon
												icon={staticValue.icon}
												className={classNames(componentStyles.staticBoxIcon)}
												iconStyle={{ fontSize: theme.fontSizes.desktop.bHuge }}
											/>
											<Text
												className={classNames(
													componentStyles.staticBoxIconContent
												)}
											>
												{staticValue.name}:
											</Text>
											<Text
												className={classNames(
													componentStyles.staticBoxIconContentValue
												)}
											>
												{staticValue.value}
											</Text>
										</Box>
									);
								})}
							</Box>
						</AccordianWrapper>
					)}
					{shouldShowAccordian(1) && (
						<AccordianWrapper
							isExpanded={expandedItem === accordionIds[1]?.id}
							heading={'Delivering Account Information'}
							onIconClick={() => toogleAccordion(accordionIds[1]?.id)}
							shouldShowField={shouldShowField}
							getFormKey={getFormKey}
							getErrorSlice={getErrorSlice}
							formIds={[
								formKeys.clearingNumber,
								formKeys.accountNumber,
								formKeys.accountTitle,
								formKeys.ssn,
								formKeys.accountType,
								formKeys.otherAccountType,
								formKeys.firmName,
								formKeys.firmPhoneNumber,
								formKeys.firmAddress,
								formKeys.deliveringAccountTitleSameAsReceiving,
							]}
						>
							<Box
								className={classNames(
									componentStyles.feildsAccordianfiledsWrapperAllTogether
								)}
							>
								<Box
									className={classNames(componentStyles.feildsInputBoxWrapper)}
								>
									{shouldShowField([formKeys.clearingNumber.id]) &&
										renderFeildComponents.renderInput(
											getFormKey(formKeys.clearingNumber),
											getLabelText(formKeys.clearingNumber, 'Clearing number')
										)}
									{shouldShowField([formKeys.firmName.id]) &&
										renderFeildComponents.renderInput(
											getFormKey(formKeys.firmName),
											getLabelText(formKeys.firmName, 'Firm name')
										)}

									{shouldShowField([formKeys.firmPhoneNumber.id]) &&
										renderFeildComponents.renderPhoneNumber(
											getFormKey(formKeys.firmPhoneNumber),
											getLabelText(
												formKeys.firmPhoneNumber,
												'Firm phone number'
											)
										)}
									{shouldShowField([
										formKeys.deliveringAccountTitleSameAsReceiving,
									]) &&
										renderFeildComponents.renderDropdown(
											getFormKey(
												formKeys.deliveringAccountTitleSameAsReceiving
											),
											getLabelText(
												formKeys.deliveringAccountTitleSameAsReceiving,
												'Is the delivering account name and/or title the same as the receiving account?'
											),
											enums[
												formKeys.deliveringAccountTitleSameAsReceiving.enum
											],
											getFormKeysArray([
												formKeys.lastNameChanged,
												formKeys.firstMiddleNameChanged,
											]),
											false,
											false,
											{},
											{ minWidth: 619, maxWidth: 619 }
										)}
									{shouldShowField([formKeys.lastNameChanged.id]) &&
										get(
											formValues,
											getFormKey(formKeys.deliveringAccountTitleSameAsReceiving)
										) === false &&
										renderFeildComponents.renderRadioGroup(
											getFormKey(formKeys.lastNameChanged),
											getLabelText(
												formKeys.lastNameChanged,
												'Last name changed'
											),
											enums[formKeys.lastNameChanged.enum],
											[],
											false,
											false,
											{}
										)}
									{shouldShowField([formKeys.firstMiddleNameChanged.id]) &&
										get(
											formValues,
											getFormKey(formKeys.deliveringAccountTitleSameAsReceiving)
										) === false &&
										renderFeildComponents.renderRadioGroup(
											getFormKey(formKeys.firstMiddleNameChanged),
											getLabelText(
												formKeys.firstMiddleNameChanged,
												'First/middle name changed'
											),
											enums[formKeys.lastNameChanged.enum],
											[],
											false,
											false,
											{}
										)}

									{shouldShowField([formKeys.firmAddress.id]) &&
										renderFeildComponents.renderAddress(
											getFormKey(formKeys.firmAddress),
											getLabelText(formKeys.firmAddress, 'Firm address')
										)}

									{shouldShowField([formKeys.accountNumber.id]) &&
										renderFeildComponents.renderFormattedInput(
											getFormKey(formKeys.accountNumber),
											getLabelText(formKeys.accountNumber, 'Account number'),
											'*************************'
										)}
									{shouldShowField([formKeys.accountTitle.id]) &&
										renderFeildComponents.renderInput(
											getFormKey(formKeys.accountTitle),
											getLabelText(formKeys.accountTitle, 'Account title')
										)}
									{shouldShowField([formKeys.ssn.id]) &&
										renderFeildComponents.renderSsn(
											getFormKey(formKeys.ssn),
											getLabelText(formKeys.ssn, 'SSN'),
											{
												placeholder: ssn,
												disabled: true,
											}
										)}

									{shouldShowField([formKeys.accountType.id]) &&
										renderFeildComponents.renderDropdown(
											getFormKey(formKeys.accountType),
											getLabelText(formKeys.accountType, 'Account type'),
											getEnumValue(formKeys.accountType),
											[],
											false,
											true
										)}
									{get(formValues, getFormKey(formKeys.accountType)) ===
										otherAccountType &&
										shouldShowField([formKeys.otherAccountType.id]) &&
										renderFeildComponents.renderInput(
											getFormKey(formKeys.otherAccountType),
											getLabelText(formKeys.otherAccountType, 'Other')
										)}
								</Box>
							</Box>
						</AccordianWrapper>
					)}
					{shouldShowAccordian(2) && (
						<AccordianWrapper
							isExpanded={expandedItem === accordionIds[2]?.id}
							heading={'Transfer Instructions'}
							onIconClick={() => toogleAccordion(accordionIds[2]?.id)}
							getFormKey={getFormKey}
							shouldShowField={shouldShowField}
							getErrorSlice={getErrorSlice}
							formIds={[
								formKeys.transferType,
								formKeys.brokerageOrTrustCompany,
								formKeys.partialTransferAmount,
								formKeys.bankAccountTransferType,
								{
									feild: formKeys.description,
									currentLength: curLoopRowData.length,
									oneToMenyKey: getFormKey(formKeys.loopingRowKey),
								},
								{
									feild: formKeys.cusip,
									currentLength: curLoopRowData.length,
									oneToMenyKey: getFormKey(formKeys.loopingRowKey),
								},
								{
									feild: formKeys.quantity,
									currentLength: curLoopRowData.length,
									oneToMenyKey: getFormKey(formKeys.loopingRowKey),
								},

								{
									feild: formKeys.symbol,
									currentLength: curLoopRowData.length,
									oneToMenyKey: getFormKey(formKeys.loopingRowKey),
								},
								{
									feild: formKeys.fundAccountNumber,
									currentLength: curLoopRowData.length,
									oneToMenyKey: getFormKey(formKeys.loopingRowKey),
								},
								{
									feild: formKeys.mutualFundTransferOption,
									currentLength: curLoopRowData.length,
									oneToMenyKey: getFormKey(formKeys.loopingRowKey),
								},
								{
									feild: formKeys.transferAll,
									currentLength: curLoopRowData.length,
									oneToMenyKey: getFormKey(formKeys.loopingRowKey),
								},

								{
									feild: formKeys.dividendOption,
									currentLength: curLoopRowData.length,
									oneToMenyKey: getFormKey(formKeys.loopingRowKey),
								},
								{
									feild: formKeys.capitalGainsOption,
									currentLength: curLoopRowData.length,
									oneToMenyKey: getFormKey(formKeys.loopingRowKey),
								},
							]}
						>
							<>
								<Box
									className={classNames(componentStyles.feildsInputBoxWrapper)}
								>
									{shouldShowField([formKeys.transferType.id]) &&
										renderFeildComponents.renderDropdown(
											getFormKey(formKeys.transferType),
											getLabelText(formKeys.transferType, 'Transfer type'),
											isIRA
												? enums[formKeys.transferType.enum]
												: enums[formKeys.transferType.enum2],
											getFormKeysArray([
												formKeys.loopingRowKey,
												formKeys.partialTransferAmount,
												formKeys.cdMaturityDate,
											]),
											false,
											false,
											{},
											get(formValues, getFormKey(formKeys.transferType)) !==
												transferTypeValueOfAnnuityPartialLiquidation &&
												get(formValues, getFormKey(formKeys.transferType)) !==
													transferTypeOpionsIRAValueofBankOrCreditUnionTransfer
												? { wrapperClasss: componentStyles.halfFlex }
												: {}
										)}
									{shouldShowField([formKeys.brokerageOrTrustCompany.id]) &&
										selectedTransferType ===
											externalTransferTopLevelBrokerageTrustCompanyPartialAccountTransfer &&
										renderFeildComponents.renderDropdown(
											getFormKey(formKeys.brokerageOrTrustCompany),
											getLabelText(
												formKeys.brokerageOrTrustCompany,
												'Brokerage/Trust company'
											),
											enums[formKeys.brokerageOrTrustCompany.enum],
											getFormKeysArray([
												formKeys.loopingRowKey,
												formKeys.cashAmount,
											]),
											false,
											false,
											{},
											{}
										)}

									{shouldShowField([formKeys.cashAmount.id]) &&
										get(
											formValues,
											getFormKey(formKeys.brokerageOrTrustCompany)
										) ===
											brokerageOrTrustCompanyOptionsPartialAccountTransfer &&
										renderFeildComponents.renderCurrency(
											getFormKey(formKeys.cashAmount),
											getLabelText(formKeys.cashAmount, 'Cash amount')
										)}
									{selectedTransferType ===
										externalTransferTopLevelAnnuityCompanyTransfer && (
										<>
											{shouldShowField([formKeys.annuityTransfer.id]) &&
												renderFeildComponents.renderDropdown(
													getFormKey(formKeys.annuityTransfer),
													getLabelText(
														formKeys.annuityTransfer,
														'Annuity transfer type'
													),
													enums[formKeys.annuityTransfer.enum],
													getFormKeysArray([
														formKeys.amount,
														formKeys.cdMaturityDate,
													]),
													false,
													false,
													{}
												)}
											{get(formValues, getFormKey(formKeys.annuityTransfer)) &&
												shouldShowField([formKeys.amount.id]) &&
												renderFeildComponents.renderCurrency(
													getFormKey(formKeys.amount),
													getLabelText(formKeys.amount, 'Amount')
												)}
											{shouldShowField([formKeys.cdMaturityDate.id]) &&
												(get(
													formValues,
													getFormKey(formKeys.annuityTransfer)
												) ===
													externalTransferAnnuityTransferOptionsFullMeturity ||
													get(
														formValues,
														getFormKey(formKeys.annuityTransfer)
													) ===
														externalTransferAnnuityTransferOptionsPartialMeturity) &&
												renderFeildComponents.renderDate(
													getFormKey(formKeys.cdMaturityDate),
													getLabelText(formKeys.cdMaturityDate, 'Maturity date')
												)}
										</>
									)}
									{selectedTransferType ===
										externalTransferTopLevelMutualFundCompanyTransfer &&
										shouldShowField([formKeys.mutualFundTransferType.id]) &&
										renderFeildComponents.renderDropdown(
											getFormKey(formKeys.mutualFundTransferType),
											getLabelText(
												formKeys.mutualFundTransferType,
												'Transfer type'
											),
											enums[formKeys.mutualFundTransferType.enum]
										)}
									{selectedTransferType ===
										externalTransferTopLevelCertificateOfDepositTransfer && (
										<>
											{shouldShowField([
												formKeys.liquidationAndTransferType.id,
											]) &&
												renderFeildComponents.renderDropdown(
													getFormKey(formKeys.liquidationAndTransferType),
													getLabelText(
														formKeys.liquidationAndTransferType,
														'Liquidation and transfer type'
													),
													enums[formKeys.liquidationAndTransferType.enum],
													getFormKeysArray([
														formKeys.amount,
														formKeys.cdMaturityDate,
													]),
													false,
													false,
													{},
													{}
												)}
											{get(
												formValues,
												getFormKey(formKeys.liquidationAndTransferType)
											) &&
												shouldShowField([formKeys.amount.id]) &&
												renderFeildComponents.renderCurrency(
													getFormKey(formKeys.amount),
													getLabelText(formKeys.amount, 'Amount')
												)}
											{shouldShowField([formKeys.cdMaturityDate.id]) &&
												(get(
													formValues,
													getFormKey(formKeys.liquidationAndTransferType)
												) ===
													externalTransferCeritificateOfDepositeLiquidateAndTransferOptionFullAtMaturityLiquidateCDAndTransferAllCash ||
													get(
														formValues,
														getFormKey(formKeys.liquidationAndTransferType)
													) ===
														externalTransferCeritificateOfDepositeLiquidateAndTransferOptionPartialAtMaturityLiquidateCDAndTransferCash) &&
												renderFeildComponents.renderDate(
													getFormKey(formKeys.cdMaturityDate),
													getLabelText(formKeys.cdMaturityDate, 'Maturity date')
												)}
										</>
									)}

									{get(formValues, getFormKey(formKeys.transferType)) ===
										transferTypeValueOfAnnuityPartialLiquidation &&
										shouldShowField([formKeys.partialTransferAmount.id]) &&
										renderFeildComponents.renderCurrency(
											getFormKey(formKeys.partialTransferAmount),
											getLabelText(
												formKeys.partialTransferAmount,
												'Partial transfer amount'
											)
										)}
									{get(formValues, getFormKey(formKeys.transferType)) ===
										transferTypeOpionsIRAValueofBankOrCreditUnionTransfer && (
										<>
											{shouldShowField([formKeys.bankAccountTransferType.id]) &&
												renderFeildComponents.renderDropdown(
													getFormKey(formKeys.bankAccountTransferType),
													getLabelText(
														formKeys.bankAccountTransferType,
														'Bank transfer type'
													),
													isIRA
														? enums[formKeys.bankAccountTransferType.enum2]
														: enums[formKeys.bankAccountTransferType.enum],
													[]
												)}

											{get(
												formValues,
												getFormKey(formKeys.bankAccountTransferType)
											) === bankTransferTypeOptionsValueOfTransferPartialCash &&
												shouldShowField([formKeys.partialTransferAmount.id]) &&
												renderFeildComponents.renderCurrency(
													getFormKey(formKeys.partialTransferAmount),
													getLabelText(formKeys.partialTransferAmount, 'Amount')
												)}
										</>
									)}
									{get(formValues, getFormKey(formKeys.transferType)) ===
										transferTypeOpionsIRAValueofBankOrCreditUnionTransfer &&
										get(
											formValues,
											getFormKey(formKeys.bankAccountTransferType)
										) ===
											bankTransferTypeOptionsValueOfLiquidateCDAtMaturityAndTransferCash &&
										shouldShowField([formKeys.cdMaturityDate.id]) &&
										renderFeildComponents.renderDate(
											getFormKey(formKeys.cdMaturityDate),
											getLabelText(formKeys.cdMaturityDate, 'Maturity date'),
											{},
											{ wrapperClasss: componentStyles.halfFlex }
										)}
								</Box>

								{(get(formValues, getFormKey(formKeys.transferType)) ===
									transferTypeValueOfBrokerageTrustCompanyPartialAccountTransfer ||
									get(
										formValues,
										getFormKey(formKeys.brokerageOrTrustCompany)
									) === brokerageOrTrustCompanyOptionsPartialAccountTransfer) &&
									shouldShowField([
										formKeys.loopingRowKey.id,
										formKeys.description.id,
										formKeys.cusip.id,
										formKeys.quantity.id,
									]) &&
									curLoopRowData.map((rowItem, rowIndex) => {
										const checkValueExist = checkValueOfLoopHasValue(
											curLoopRowData,
											rowIndex
										);
										return (
											<Box flexDirection="row">
												<Box
													className={classNames(
														componentStyles.feildsInputBoxWrapper
													)}
												>
													{shouldShowField([formKeys.description.id]) &&
														renderFeildComponents.renderInput(
															getFormKey(formKeys.description),
															getLabelText(
																formKeys.description,
																'Asset description'
															),
															{},

															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
													{shouldShowField([formKeys.cusip.id]) &&
														renderFeildComponents.renderInput(
															getFormKey(formKeys.cusip),
															getLabelText(formKeys.cusip, 'CUSIP'),
															{
																allowedCharacters: 'text_and_number',
																textTransform: 'uppercase',
																maxCharLength: 9,
															},
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
													{shouldShowField([formKeys.quantity.id]) &&
														renderFeildComponents.renderInput(
															getFormKey(formKeys.quantity),
															getLabelText(formKeys.quantity, 'Quantity'),
															{
																allowedCharacters: 'number_only',
															},
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
												</Box>
												<Box
													className={classNames(
														componentStyles.addDeleteSection
													)}
												>
													{checkMaximumCountReached(formKeys.loopingRowKey) >
														curLoopRowData.length && (
														<Box>
															{checkValueExist ? (
																<Icon
																	icon={IconCodes.icon_Bd_Plus_Circle}
																	onClick={() =>
																		onAddRow(
																			curLoopRowData,
																			getFormKey(formKeys.loopingRowKey)
																		)
																	}
																	className={classNames(
																		componentStyles.addIcon
																	)}
																/>
															) : (
																<Tooltip
																	content={
																		'Please fill out this row before adding another'
																	}
																	type={TooltipType.Tooltip}
																	placement={TooltipPosition.BottomCenter}
																	triggerOffset={10}
																>
																	<Box>
																		<Icon
																			icon={IconCodes.icon_Bd_Plus_Circle}
																			className={classNames(
																				componentStyles.addIconDisabled
																			)}
																		/>
																	</Box>
																</Tooltip>
															)}
														</Box>
													)}
													{curLoopRowData.length > 1 && (
														<Icon
															icon={IconCodes.icon_Bd_Delete}
															onClick={() =>
																onDeleteRow(
																	rowIndex,
																	curLoopRowData,
																	getFormKey(formKeys.loopingRowKey)
																)
															}
															className={classNames(componentStyles.deleteIcon)}
														/>
													)}
												</Box>
											</Box>
										);
									})}

								{(get(formValues, getFormKey(formKeys.transferType)) ===
									transferTypeValueOfMutualFundCompanyTransferLiquidation ||
									selectedTransferType ===
										externalTransferTopLevelMutualFundCompanyTransfer) &&
									shouldShowField([
										formKeys.loopingRowKey.id,
										formKeys.description.id,
										formKeys.symbol.id,
										formKeys.fundAccountNumber.id,
										formKeys.mutualFundTransferOption.id,
										formKeys.transferAll.id,
										formKeys.quantity.id,
										formKeys.dividendOption.id,
										formKeys.capitalGainsOption.id,
									]) &&
									curLoopRowData.map((rowItem, rowIndex) => {
										const checkValueExist = checkValueOfLoopHasValue(
											curLoopRowData,
											rowIndex
										);
										return (
											<Box flexDirection="row">
												<Box
													className={classNames(
														componentStyles.feildsInputBoxWrapper
													)}
												>
													{shouldShowField([formKeys.description.id]) &&
														renderFeildComponents.renderInput(
															getFormKey(formKeys.description),
															getLabelText(
																formKeys.description,
																'Fund Name/Share Class'
															),
															{},

															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
													{shouldShowField([formKeys.symbol.id]) &&
														renderFeildComponents.renderInput(
															getFormKey(formKeys.symbol),
															getLabelText(formKeys.symbol, 'Symbol'),
															{
																allowedCharacters: 'text_and_number',
																textTransform: 'uppercase',
																maxCharLength: 9,
															},
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
													{shouldShowField([formKeys.fundAccountNumber.id]) &&
														renderFeildComponents.renderFormattedInput(
															getFormKey(formKeys.fundAccountNumber),
															getLabelText(
																formKeys.fundAccountNumber,
																'Fund account number'
															),
															'*************************',
															{},
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
													{shouldShowField([
														formKeys.mutualFundTransferOption.id,
													]) &&
														renderFeildComponents.renderDropdown(
															getFormKey(formKeys.mutualFundTransferOption),
															getLabelText(
																formKeys.mutualFundTransferOption,
																'Transfer option'
															),
															enums[formKeys.mutualFundTransferOption.enum],

															[
																{
																	loopKey: getFormKey(formKeys.loopingRowKey),
																	loopIndex: rowIndex,
																	key: formKeys.transferAll,
																},
																{
																	loopKey: getFormKey(formKeys.loopingRowKey),
																	loopIndex: rowIndex,
																	key: formKeys.quantity,
																},
																{
																	loopKey: getFormKey(formKeys.loopingRowKey),
																	loopIndex: rowIndex,
																	key: formKeys.dividendOption,
																},
																{
																	loopKey: getFormKey(formKeys.loopingRowKey),
																	loopIndex: rowIndex,
																	key: formKeys.capitalGainsOption,
																},
															],
															false,
															false,
															{},
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
													{(get(
														formValues,
														`${getFormKey(
															formKeys.loopingRowKey
														)}[${rowIndex}].${getFormKey(
															formKeys.mutualFundTransferOption
														)}`
													) === transferOptionsValueOfTransferInKindOptions ||
														get(
															formValues,
															`${getFormKey(
																formKeys.loopingRowKey
															)}[${rowIndex}].${getFormKey(
																formKeys.mutualFundTransferOption
															)}`
														) === transferOptionsValueOfLiquidateOptions) && (
														<>
															{shouldShowField([formKeys.transferAll.id]) &&
																renderFeildComponents.renderDropdown(
																	getFormKey(formKeys.transferAll),
																	getLabelText(
																		formKeys.transferAll,
																		'Transfer all'
																	),
																	enums[formKeys.transferAll.enum],
																	[
																		{
																			loopKey: getFormKey(
																				formKeys.loopingRowKey
																			),
																			loopIndex: rowIndex,
																			key: getFormKey(formKeys.quantity),
																		},
																	],
																	false,
																	false,
																	{},
																	{
																		oneToManyLoopIndex: rowIndex,
																		oneToMenyKey: getFormKey(
																			formKeys.loopingRowKey
																		),
																	}
																)}
															{get(
																formValues,
																`${getFormKey(
																	formKeys.loopingRowKey
																)}[${rowIndex}].${getFormKey(
																	formKeys.transferAll
																)}`
															) === transferAllOptionsValueOfShareAmount &&
																shouldShowField([formKeys.quantity.id]) &&
																renderFeildComponents.renderCurrency(
																	getFormKey(formKeys.sequiritiesAmount),
																	getLabelText(
																		formKeys.sequiritiesAmount,
																		'Share amount'
																	),

																	{
																		maxCharLength: 5,
																		decimalScale: 5,
																		allowNegative: false,
																	},
																	{
																		oneToManyLoopIndex: rowIndex,
																		oneToMenyKey: getFormKey(
																			formKeys.loopingRowKey
																		),
																	}
																)}
															{get(
																formValues,
																`${getFormKey(
																	formKeys.loopingRowKey
																)}[${rowIndex}].${getFormKey(
																	formKeys.mutualFundTransferOption
																)}`
															) ===
																transferOptionsValueOfTransferInKindOptions &&
																shouldShowField([formKeys.dividendOption.id]) &&
																renderFeildComponents.renderDropdown(
																	getFormKey(formKeys.dividendOption),
																	getLabelText(
																		formKeys.dividendOption,
																		'Dividend options'
																	),
																	enums[formKeys.dividendOption.enum],
																	[],
																	false,
																	false,
																	{},

																	{
																		oneToManyLoopIndex: rowIndex,
																		oneToMenyKey: getFormKey(
																			formKeys.loopingRowKey
																		),
																	}
																)}
															{get(
																formValues,
																`${getFormKey(
																	formKeys.loopingRowKey
																)}[${rowIndex}].${getFormKey(
																	formKeys.mutualFundTransferOption
																)}`
															) ===
																transferOptionsValueOfTransferInKindOptions &&
																shouldShowField([
																	formKeys.capitalGainsOption.id,
																]) &&
																renderFeildComponents.renderDropdown(
																	getFormKey(formKeys.capitalGainsOption),
																	getLabelText(
																		formKeys.capitalGainsOption,
																		'Capital gain options'
																	),
																	enums[formKeys.capitalGainsOption.enum],
																	[],
																	false,
																	false,
																	{},
																	{
																		oneToManyLoopIndex: rowIndex,
																		oneToMenyKey: getFormKey(
																			formKeys.loopingRowKey
																		),
																	}
																)}
														</>
													)}
													{get(
														formValues,
														getFormKey(formKeys.mutualFundTransferOption)
													) === transferOptionsValueOfLiquidateOptions && (
														<>
															{shouldShowField([formKeys.transferAll.id]) &&
																renderFeildComponents.renderDropdown(
																	getFormKey(formKeys.transferAll),
																	getLabelText(
																		formKeys.transferAll,
																		'Transfer all'
																	),
																	transferAllOptions,
																	[
																		{
																			loopKey: getFormKey(
																				formKeys.loopingRowKey
																			),
																			loopIndex: rowIndex,
																			key: getFormKey(formKeys.quantity),
																		},
																	],
																	false,
																	false,
																	{},
																	{
																		oneToManyLoopIndex: rowIndex,
																		oneToMenyKey: getFormKey(
																			formKeys.loopingRowKey
																		),
																	}
																)}
															{get(
																formValues,
																`${getFormKey(
																	formKeys.loopingRowKey
																)}[${rowIndex}].${getFormKey(
																	formKeys.transferAll
																)}`
															) === transferAllOptionsValueOfShareAmount &&
																shouldShowField([formKeys.quantity.id]) &&
																renderFeildComponents.renderCurrency(
																	getFormKey(formKeys.sequiritiesAmount),
																	getLabelText(
																		formKeys.sequiritiesAmount,
																		'Share amount'
																	),
																	{
																		decimalScale: 5,
																		allowNegative: false,
																	},
																	{
																		oneToManyLoopIndex: rowIndex,
																		oneToMenyKey: getFormKey(
																			formKeys.loopingRowKey
																		),
																	}
																)}
														</>
													)}
												</Box>
												<Box
													className={classNames(
														componentStyles.addDeleteSection
													)}
												>
													{checkMaximumCountReached(formKeys.loopingRowKey) >
														curLoopRowData.length && (
														<Box>
															{checkValueExist ? (
																<Icon
																	icon={IconCodes.icon_Bd_Plus_Circle}
																	onClick={() =>
																		onAddRow(
																			curLoopRowData,
																			getFormKey(formKeys.loopingRowKey)
																		)
																	}
																	className={classNames(
																		componentStyles.addIcon
																	)}
																/>
															) : (
																<Tooltip
																	content={
																		'Please fill out this row before adding another'
																	}
																	type={TooltipType.Tooltip}
																	placement={TooltipPosition.BottomCenter}
																	triggerOffset={10}
																>
																	<Box>
																		<Icon
																			icon={IconCodes.icon_Bd_Plus_Circle}
																			className={classNames(
																				componentStyles.addIconDisabled
																			)}
																		/>
																	</Box>
																</Tooltip>
															)}
														</Box>
													)}
													{curLoopRowData.length > 1 && (
														<Icon
															icon={IconCodes.icon_Bd_Delete}
															onClick={() =>
																onDeleteRow(
																	rowIndex,
																	curLoopRowData,
																	getFormKey(formKeys.loopingRowKey)
																)
															}
															className={classNames(componentStyles.deleteIcon)}
														/>
													)}
												</Box>
											</Box>
										);
									})}

								{get(formValues, getFormKey(formKeys.transferType)) ===
									transferTypeValueOfTransferAgentTransfer &&
									shouldShowField([
										formKeys.description.id,
										formKeys.cusip.id,
										formKeys.transferAll.id,
										formKeys.quantity.id,
									]) &&
									curLoopRowData.map((rowItem, rowIndex) => {
										const checkValueExist = checkValueOfLoopHasValue(
											curLoopRowData,
											rowIndex
										);
										return (
											<Box flexDirection="row">
												<Box
													className={classNames(
														componentStyles.feildsInputBoxWrapper
													)}
												>
													{shouldShowField([formKeys.description.id]) &&
														renderFeildComponents.renderInput(
															getFormKey(formKeys.description),
															getLabelText(
																formKeys.description,
																'Asset Description'
															),
															{},

															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
													{shouldShowField([formKeys.cusip.id]) &&
														renderFeildComponents.renderInput(
															getFormKey(formKeys.cusip),
															getLabelText(formKeys.cusip, 'CUSIP'),
															{
																allowedCharacters: 'text_and_number',
																textTransform: 'uppercase',
																maxCharLength: 9,
															},
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
													{shouldShowField([formKeys.transferAll.id]) &&
														renderFeildComponents.renderDropdown(
															getFormKey(formKeys.transferAll),
															getLabelText(
																formKeys.transferAll,
																'Transfer all'
															),
															enums[formKeys.transferAll.enum],
															[
																{
																	loopKey: getFormKey(formKeys.loopingRowKey),
																	loopIndex: rowIndex,
																	key: getFormKey(formKeys.quantity),
																},
															],
															false,
															false,
															{},
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
													{shouldShowField([formKeys.quantity.id]) &&
														get(
															formValues,
															`${getFormKey(
																formKeys.loopingRowKey
															)}[${rowIndex}].${getFormKey(
																formKeys.transferAll
															)}`
														) === transferAllOptionsValueOfShareAmount &&
														renderFeildComponents.renderCurrency(
															getFormKey(formKeys.sequiritiesAmount),
															getLabelText(
																formKeys.sequiritiesAmount,
																'Share amount'
															),
															{ allowDecimalPlaces: false },
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loopingRowKey
																),
															}
														)}
												</Box>
												<Box
													className={classNames(
														componentStyles.addDeleteSection
													)}
												>
													{checkMaximumCountReached(formKeys.loopingRowKey) >
														curLoopRowData.length && (
														<Box>
															{checkValueExist ? (
																<Box>
																	{checkValueExist ? (
																		<Icon
																			icon={IconCodes.icon_Bd_Plus_Circle}
																			onClick={() =>
																				onAddRow(
																					curLoopRowData,
																					getFormKey(formKeys.loopingRowKey)
																				)
																			}
																			className={classNames(
																				componentStyles.addIcon
																			)}
																		/>
																	) : (
																		<Tooltip
																			content={
																				'Please fill out this row before adding another'
																			}
																			type={TooltipType.Tooltip}
																			placement={TooltipPosition.BottomCenter}
																			triggerOffset={10}
																		>
																			<Box>
																				<Icon
																					icon={IconCodes.icon_Bd_Plus_Circle}
																					className={classNames(
																						componentStyles.addIconDisabled
																					)}
																				/>
																			</Box>
																		</Tooltip>
																	)}
																</Box>
															) : (
																<Tooltip
																	content={
																		'Please fill out this row before adding another'
																	}
																	type={TooltipType.Tooltip}
																	placement={TooltipPosition.BottomCenter}
																	triggerOffset={10}
																>
																	<Box>
																		<Icon
																			icon={IconCodes.icon_Bd_Plus_Circle}
																			className={classNames(
																				componentStyles.addIconDisabled
																			)}
																		/>
																	</Box>
																</Tooltip>
															)}
														</Box>
													)}
													{curLoopRowData.length > 1 && (
														<Icon
															icon={IconCodes.icon_Bd_Delete}
															onClick={() =>
																onDeleteRow(
																	rowIndex,
																	curLoopRowData,
																	getFormKey(formKeys.loopingRowKey)
																)
															}
															className={classNames(componentStyles.deleteIcon)}
														/>
													)}
												</Box>
											</Box>
										);
									})}
							</>
						</AccordianWrapper>
					)}
					{shouldShowAccordian(3) &&
						get(
							formValues,
							getFormKey(formKeys.deliveringAccountTitleSameAsReceiving)
						) === false && (
							<AccordianWrapper
								isExpanded={expandedItem === accordionIds[3]?.id}
								heading={'Account Registration Differences'}
								onIconClick={() => toogleAccordion(accordionIds[3]?.id)}
								getFormKey={getFormKey}
								formIds={[
									formKeys.accountTitle,
									{
										feild: formKeys.accountOwnerOfDeliveringAccount,
										currentLength: curLoop2RowData.length,
										oneToMenyKey: getFormKey(formKeys.loopingRowKey),
									},
									{
										feild: formKeys.emailAddress,
										currentLength: curLoop2RowData.length,
										oneToMenyKey: getFormKey(formKeys.loopingRowKey),
									},
								]}
								getErrorSlice={getErrorSlice}
								shouldShowField={shouldShowField}
							>
								<>
									<Box
										className={classNames(
											componentStyles.feildsInputBoxWrapper
										)}
									>
										{shouldShowField([formKeys.accountTitle.id]) &&
											renderFeildComponents.renderInput(
												getFormKey(formKeys.accountTitle),
												getLabelText(
													formKeys.accountTitle,
													'Account Title of Delivering Account'
												),
												{},
												{ wrapperClasss: componentStyles.halfFlex }
											)}
									</Box>
									{shouldShowField([
										formKeys.accountOwnerOfDeliveringAccount.id,
										formKeys.emailAddress.id,
										formKeys.loppingRow2Key.id,
									]) &&
										curLoop2RowData.map((rowItem, rowIndex) => {
											const checkValueExist = checkValueOfLoopHasValue(
												curLoop2RowData,
												rowIndex
											);
											return (
												<Box flexDirection="row">
													<Box
														className={classNames(
															componentStyles.feildsInputBoxWrapper
														)}
													>
														{shouldShowField([
															formKeys.accountOwnerOfDeliveringAccount.id,
														]) &&
															renderFeildComponents.renderInput(
																getFormKey(
																	formKeys.accountOwnerOfDeliveringAccount
																),
																getLabelText(
																	formKeys.accountOwnerOfDeliveringAccount,
																	'Account owner of delivering account'
																),
																{},

																{
																	oneToManyLoopIndex: rowIndex,
																	oneToMenyKey: getFormKey(
																		formKeys.loppingRow2Key
																	),
																}
															)}

														{shouldShowField([formKeys.emailAddress.id]) &&
															renderFeildComponents.renderEmail(
																getFormKey(formKeys.emailAddress),
																getLabelText(
																	formKeys.emailAddress,
																	'Email address'
																),

																{},

																{
																	oneToManyLoopIndex: rowIndex,
																	oneToMenyKey: getFormKey(
																		formKeys.loppingRow2Key
																	),
																}
															)}
													</Box>
													<Box
														className={classNames(
															componentStyles.addDeleteSection
														)}
													>
														{checkMaximumCountReached(formKeys.loppingRow2Key) >
															curLoop2RowData.length && (
															<Box>
																{checkValueExist ? (
																	<Icon
																		icon={IconCodes.icon_Bd_Plus_Circle}
																		onClick={() =>
																			onAddRow(
																				curLoop2RowData,
																				getFormKey(formKeys.loppingRow2Key)
																			)
																		}
																		className={classNames(
																			componentStyles.addIcon
																		)}
																	/>
																) : (
																	<Tooltip
																		content={
																			'Please fill out this row before adding another'
																		}
																		type={TooltipType.Tooltip}
																		placement={TooltipPosition.BottomCenter}
																		triggerOffset={10}
																	>
																		<Box>
																			<Icon
																				icon={IconCodes.icon_Bd_Plus_Circle}
																				className={classNames(
																					componentStyles.addIconDisabled
																				)}
																			/>
																		</Box>
																	</Tooltip>
																)}
															</Box>
														)}
														{curLoop2RowData.length > 1 && (
															<Icon
																icon={IconCodes.icon_Bd_Delete}
																onClick={() =>
																	onDeleteRow(
																		rowIndex,
																		curLoop2RowData,
																		getFormKey(formKeys.loppingRow2Key)
																	)
																}
																className={classNames(
																	componentStyles.deleteIcon
																)}
															/>
														)}
													</Box>
												</Box>
											);
										})}
								</>
							</AccordianWrapper>
						)}
					{shouldShowAccordian(4) && (
						<AccordianWrapper
							isExpanded={expandedItem === accordionIds[4]?.id}
							heading={'One and the same person certification'}
							onIconClick={() => toogleAccordion(accordionIds[4]?.id)}
							getFormKey={getFormKey}
							getErrorSlice={getErrorSlice}
							formIds={[
								formKeys.printNameOnAccount,
								formKeys.printAlternateNameOnDeliveringAccount,
							]}
							shouldShowField={shouldShowField}
						>
							{shouldShowField([
								formKeys.printNameOnAccount.id,
								formKeys.printAlternateNameOnDeliveringAccount.id,
							]) && (
								<Box
									className={classNames(componentStyles.feildsInputBoxWrapper)}
								>
									{shouldShowField([formKeys.printNameOnAccount.id]) &&
										renderFeildComponents.renderInput(
											getFormKey(formKeys.printNameOnAccount),
											getLabelText(
												formKeys.printNameOnAccount,
												'Print name on account'
											),
											{ showAsMandatory: false }
										)}
									{shouldShowField([
										formKeys.printAlternateNameOnDeliveringAccount.id,
									]) &&
										renderFeildComponents.renderInput(
											getFormKey(
												formKeys.printAlternateNameOnDeliveringAccount
											),
											getLabelText(
												formKeys.printAlternateNameOnDeliveringAccount,
												'Print alternate name on delivering account'
											),
											{ showAsMandatory: false }
										)}
								</Box>
							)}
						</AccordianWrapper>
					)}
				</>
			)}
		</Box>
	);
};
export default ExtAcat;

import { createRef, FC, useCallback, useEffect, useMemo, useRef } from 'react';
import Box from '@atomic/box';
import { IFeildSectionTypes } from './types';
import { styles } from './style';
import classNames from 'classnames';
import { get, isEmpty, set, isString, isObject } from 'lodash';
import { getFeildRenders } from './helper';
import { v4 as uuid } from 'uuid';

import {
	isTDepositMethodePhysical,
	payableToValueOfNationalFinancialService,
} from '../../constants';
import RenderFeildsLoop from './renderFeildsLoop';

const Check: FC<IFeildSectionTypes> = (props) => {
	const componentStyles = styles(props);
	const {
		onChangeFeild,
		formKeys,
		formValues = {},
		error = {},
		countryList = [],
		stateList = [],
		nameAppending,
		sourceMetaData,
		enums,
		verionOfMetadata,
		productType,
		triggerFormUpdate,
	} = props;
	const fieldProps = useMemo(
		() => sourceMetaData?.fieldProps || {},
		[sourceMetaData]
	);
	const refsById = useRef({});
	const feildsMetaData = useMemo(
		() => sourceMetaData?.fieldConfigs || {},
		[sourceMetaData?.fieldConfigs]
	);
	const accordianMetaData = useMemo(
		() => sourceMetaData?.accordianConfigs || {},
		[sourceMetaData?.accordianConfigs]
	);
	const getRefById = (id) => {
		if (!refsById.current[id]) {
			refsById.current[id] = createRef();
		}
		return refsById.current[id];
	};
	const handleChange = useCallback(
		(
			value: any,
			key: any,
			clearKeys?: any[],
			loopIndex?: number,
			loopKey?: string,
			triggerFormUpdate?: boolean,
			triggerFormValueUpdate?: boolean
		) => {
			let curData = value;
			let curKey = key;
			let clearKey = clearKeys;
			if (loopIndex >= 0) {
				curData = get(formValues, loopKey, [{ key: uuid() }]);
				curData[loopIndex] = set(
					curData[loopIndex] || { key: uuid() },
					key,
					value
				);
				curKey = loopKey;
			}
			const productTypeValueIsAnnuityOrInsurance =
				productType?.toLocaleLowerCase() === 'annuity' ||
				productType?.toLocaleLowerCase() === 'insurance';
			if (
				key === getFormKey(formKeys.depositMethod) &&
				value === isTDepositMethodePhysical &&
				!productTypeValueIsAnnuityOrInsurance &&
				verionOfMetadata === '2.0'
			) {
				onChangeFeild(
					payableToValueOfNationalFinancialService,
					getFormKey(formKeys.payableTo),
					[],
					true
				);
				clearKey = clearKeys.filter(
					(i) => i !== getFormKey(formKeys.payableTo)
				);
			}
			onChangeFeild(
				curData,
				curKey,
				clearKey,
				triggerFormUpdate,
				triggerFormValueUpdate
			);
		},
		[onChangeFeild, formValues, formKeys, feildsMetaData, productType]
	);

	const getErrorSlice = useCallback(
		(key, oneToMenyKey, oneToManyLoopIndex) => {
			const errorMessage =
				oneToMenyKey && oneToManyLoopIndex >= 0
					? get(error, `${oneToMenyKey}.${oneToManyLoopIndex}.${key}`)
					: get(error, key);
			return !isEmpty(errorMessage)
				? isString(errorMessage)
					? { message: errorMessage }
					: isObject(errorMessage) &&
					  (errorMessage as any)?.message &&
					  errorMessage
				: null;
		},
		[error]
	);

	const renderField = useCallback(
		(
			Component: React.ElementType,
			props: Record<string, any>,
			noWrapperRequired: boolean,
			additionalProps: any = {}
		) => {
			const { minWidth, maxWidth } = additionalProps || {};
			return noWrapperRequired ? (
				<Component {...props} />
			) : (
				<Box
					className={classNames(componentStyles.fieldInputWrapper)}
					minWidth={minWidth}
					maxWidth={maxWidth}
				>
					<Component {...props} />
				</Box>
			);
		},
		[componentStyles.fieldInputWrapper]
	);
	const renderFeildComponents: any = useMemo(() => {
		return getFeildRenders(
			renderField,
			formValues,
			handleChange,
			getErrorSlice,
			countryList,
			stateList,
			nameAppending,
			getRefById,
			triggerFormUpdate,
			fieldProps
		);
	}, [
		renderField,
		fieldProps,
		formValues,
		handleChange,
		getErrorSlice,
		countryList,
		stateList,
		nameAppending,
		triggerFormUpdate,
	]);
	const shouldShowField = useCallback(
		(keys: string[]) => {
			return keys.some((key) => feildsMetaData[key]?.on !== false);
		},
		[feildsMetaData]
	);
	const getFormKeysArray = useCallback(
		(ids: Array<any>) => {
			return ids.map((itm: any) => feildsMetaData[itm.id]?.key || itm.key);
		},
		[feildsMetaData]
	);
	const getFormKey = useCallback(
		(idKey: any) => {
			return feildsMetaData[idKey.id]?.key || idKey.key;
		},
		[feildsMetaData]
	);
	useEffect(() => {
		const usedForAccountFundingValue = get(
			formValues,
			getFormKey(formKeys.usedForAccountFunding)
		);
		if (
			shouldShowField([formKeys.usedForAccountFunding.id]) &&
			usedForAccountFundingValue !== false &&
			!usedForAccountFundingValue
		) {
			handleChange(
				true,
				getFormKey(formKeys.usedForAccountFunding),
				[],
				-1,
				'',
				true
			);
		}
	}, []);
	// ---- RenderFieldsLoop implementation for Check Form ----
	const checkFormLoopData = [
		{
			accordianRequired: false,
			accordinaData: {},
			feildsData: [
				{
					item: formKeys.usedForAccountFunding,
					renderType: 'renderDropdown',
					text: 'Used to fund product',
					feildProps: [
						enums[formKeys.usedForAccountFunding.enum],
						getFormKeysArray([formKeys.usedForAccountFundingAmount]),
						false,
						false,
						{},
					],
				},
				{
					item: formKeys.usedForAccountFundingAmount,
					renderType: 'renderCurrency',
					text: 'Amount',
					feildProps: [
						{ name: 'checkAmount2' },
						{ wrapperClasss: componentStyles.halfFlex },
					],
					conditional:
						get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
						true,
				},
				{
					item: formKeys.amount,
					renderType: 'renderCurrency',
					text: 'Amount',
					feildProps: [
						{ name: 'checkAmount', showAsMandatory: false },
						{ wrapperClasss: componentStyles.halfFlex },
					],
				},
				{
					item: formKeys.isTheCheckAccompanyingApplication,
					renderType: 'renderRadioGroup',
					text: 'Is the check accompanying this application?',
					feildProps: [
						enums[formKeys.isTheCheckAccompanyingApplication.enum],
						[],
						{},
						{
							width: 'auto',
							showAsMandatory: verionOfMetadata === '2.0' ? true : false,
						},
					],
				},
				{
					item: formKeys.depositMethod,
					renderType: 'renderDropdown',
					text: 'Deposit method',
					feildProps: [
						enums[formKeys.depositMethod.enum],
						getFormKeysArray([formKeys.payableTo]),
						false,
						false,
						{},
						{ wrapperClasss: componentStyles.halfFlex },
					],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.isTheCheckAccompanyingApplication)
						) === true,
				},
				{
					item: formKeys.payableTo,
					renderType: 'renderDropdown',
					text: 'Payable to',
					feildProps: [
						enums[formKeys.payableTo.enum],
						[],
						false,
						false,
						{},
						{ wrapperClasss: componentStyles.halfFlex },
					],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.isTheCheckAccompanyingApplication)
						) === true &&
						get(formValues, getFormKey(formKeys.depositMethod)) ===
							isTDepositMethodePhysical,
				},
			],
		},
	];

	return (
		<RenderFeildsLoop
			loopData={checkFormLoopData as any}
			renderFeildComponents={renderFeildComponents}
			feildsMetaData={feildsMetaData}
			accordianMetaData={accordianMetaData}
			getErrorSlice={getErrorSlice}
			onAddRow={() => {}}
			onDeleteRow={() => {}}
		/>
	);
};
export default Check;

import {
	createRef,
	FC,
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import Box from '@atomic/box';
import { IFeildSectionTypes } from './types';
import { styles } from './style';
import classNames from 'classnames';
import { get, isEmpty, set, isString, isObject } from 'lodash';
import { getFeildRenders } from './helper';
import { v4 as uuid } from 'uuid';
import {
	amountTypeOptionsValueOfDollar,
	amountTypeOptionsValueOfShares,
	internalTransferTopLevelInternalBanIRACDSavg,
	internalTransferTopLevelInternalExistingCSIJournal,
	liquidationInstructionsValueOfLiquidateAndTransferOfMyAssets,
	transferInstructionsMeturityDate,
	transferInstructionsTransferTypePartialAccountValue,
} from '../../constants';

import RenderFeildsLoop from './renderFeildsLoop';
const accordionIds = [
	{ id: '0', key: 'firstAccordian' },
	{ id: '1', key: 'deliveringAccountInformation' },
	{ id: '2', key: 'recievingAccountInformation' },
	{ id: '3', key: 'transferInstructions' },
	{ id: '4', key: 'partialAccountTransfer' },
];
const InternalTransfer: FC<IFeildSectionTypes> = (props) => {
	const componentStyles = styles(props);
	const {
		onChangeFeild,
		formKeys,
		formValues = {},
		error = {},
		countryList = [],
		stateList = [],
		nameAppending,
		isIRA,
		sourceMetaData,
		enums,
		triggerFormUpdate,
		ageOnDec31,
	} = props;
	const fieldProps = useMemo(
		() => sourceMetaData?.fieldProps || {},
		[sourceMetaData]
	);
	const feildsMetaData = useMemo(
		() => sourceMetaData?.fieldConfigs || {},
		[sourceMetaData]
	);
	const accordianMetaData = useMemo(
		() => sourceMetaData?.accordianConfigs || {},
		[sourceMetaData]
	);
	const refsById = useRef({});
	const getRefById = (id) => {
		if (!refsById.current[id]) {
			refsById.current[id] = createRef();
		}
		return refsById.current[id];
	};

	const getFormKey = useCallback(
		(idKey: any) => {
			return feildsMetaData[idKey.id]?.key || idKey.key;
		},
		[feildsMetaData]
	);
	const curLoopRowValue = get(formValues, getFormKey(formKeys.loopingRowKey), [
		{ key: uuid() },
	]);
	const curLoopRowData =
		Array.isArray(curLoopRowValue) && curLoopRowValue.length === 0
			? [{ key: uuid() }]
			: curLoopRowValue;

	const [expandedItem, setExpandedItem] = useState(accordionIds[1]?.id);

	const handleChange = useCallback(
		(
			value: any,
			key: any,
			clearKeys?: any[],
			loopIndex?: number,
			loopKey?: string | number,
			triggerFormUpdate?: boolean,
			triggerFormValueUpdate?: boolean
		) => {
			let curData = value;
			let curKey = key;

			if (loopIndex >= 0 && loopKey !== 0) {
				curData = get(formValues, loopKey, [{ key: uuid() }]);
				curData[loopIndex] = set(
					curData[loopIndex] || { key: uuid() },
					key,
					value
				);
				curKey = loopKey;
			} else if (loopIndex >= 0 && loopKey === 0) {
				curData = get(formValues, curKey, ['']);

				curData[loopIndex] = value;
			}
			if (
				curKey === getFormKey(formKeys.transferInstructionsTransferType) &&
				curData === transferInstructionsTransferTypePartialAccountValue
			) {
				onChangeFeild(
					false as any,
					getFormKey(formKeys.transferIsIraContribution),
					[],
					true
				);
			}
			onChangeFeild(
				curData,
				curKey,
				clearKeys,
				triggerFormUpdate,
				triggerFormValueUpdate
			);
		},
		[onChangeFeild, formValues, formKeys, getFormKey]
	);

	const toogleAccordion = (id) => {
		setExpandedItem(expandedItem === id ? '' : id);
	};
	const getErrorSlice = useCallback(
		(key, oneToMenyKey, oneToManyLoopIndex) => {
			const errorMessage =
				oneToMenyKey && oneToManyLoopIndex >= 0
					? get(error, `${oneToMenyKey}.${oneToManyLoopIndex}.${key}`)
					: get(error, key);
			return !isEmpty(errorMessage)
				? isString(errorMessage)
					? { message: errorMessage }
					: isObject(errorMessage) &&
					  (errorMessage as any)?.message &&
					  errorMessage
				: null;
		},
		[error]
	);
	const onAddRow = useCallback(
		(loopData, loopKey, arrayLoop) => {
			const curValues = [...loopData];
			if (arrayLoop) {
				curValues.push('');
			} else {
				curValues.push({ key: uuid() });
			}

			handleChange(curValues, loopKey);
		},
		[handleChange]
	);
	const onDeleteRow = useCallback(
		(index, loopData, loopKey) => {
			const curValues = [...loopData];
			curValues.splice(index, 1);
			handleChange(curValues, loopKey, [], -1, '', true, true);
		},
		[handleChange]
	);
	const renderField = useCallback(
		(
			Component: React.ElementType,
			props: Record<string, any>,
			noWrapperRequired: boolean,
			additionalProps: any = {}
		) => {
			const { minWidth, maxWidth, doubleWrapperRequired } =
				additionalProps || {};

			return noWrapperRequired ? (
				<Component {...props} />
			) : doubleWrapperRequired ? (
				<Box display="block" width="100%">
					<Component {...props} />
				</Box>
			) : (
				<Box
					className={classNames(componentStyles.fieldInputWrapper)}
					minWidth={minWidth}
					maxWidth={maxWidth}
				>
					<Component {...props} />
				</Box>
			);
		},
		[componentStyles.fieldInputWrapper]
	);
	const renderFeildComponents: any = useMemo(() => {
		return getFeildRenders(
			renderField,
			formValues,
			handleChange,
			getErrorSlice,
			countryList,
			stateList,
			nameAppending,
			getRefById,
			triggerFormUpdate,
			fieldProps
		);
	}, [
		renderField,
		fieldProps,
		formValues,
		handleChange,
		getErrorSlice,
		countryList,
		stateList,
		nameAppending,
		triggerFormUpdate,
	]);

	const getFormKeysArray = useCallback(
		(ids: Array<any>) => {
			return ids.map((itm: any) => feildsMetaData[itm.id]?.key || itm.key);
		},
		[feildsMetaData]
	);
	useEffect(() => {
		const usedForAccountFundingValue = get(
			formValues,
			getFormKey(formKeys.usedForAccountFunding)
		);
		if (
			feildsMetaData[getFormKey(formKeys.usedForAccountFunding)]?.on !==
				false &&
			usedForAccountFundingValue !== false &&
			!usedForAccountFundingValue
		) {
			handleChange(
				true,
				getFormKey(formKeys.usedForAccountFunding),
				[],
				-1,
				'',
				true
			);
		}
	}, []);
	const selectedTransferType = useMemo(() => {
		const sourceTypeValue = (sourceMetaData?.topLevel || [])[0]?.key;
		return get(
			formValues,
			sourceTypeValue || formKeys.internalTransferTopLevel.key
		);
	}, [formValues, formKeys, sourceMetaData]);
	const loopDataMap = {
		[internalTransferTopLevelInternalBanIRACDSavg]: [
			{
				accordianRequired: false,
				accordinaData: {},
				feildsData: [
					{
						item: formKeys.usedForAccountFunding,
						renderType: 'renderDropdown',
						text: 'Used to fund product',
						feildProps: [
							enums[formKeys.usedForAccountFunding.enum],
							[getFormKey(formKeys.amount)],
						],
					},
					{
						item: formKeys.amount,
						renderType: 'renderCurrency',
						text: 'Amount',
						conditional:
							get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
							true,
					},
					{
						item: formKeys.existingAccountType,
						renderType: 'renderDropdown',
						text: 'Existing account type',

						feildProps: [enums[formKeys.existingAccountType.enum]],
					},
					{
						arrayLoop: true,
						isLoop: true,
						loopItem: formKeys.transferInstructionsAccountNumber,
						data: get(
							formValues,
							getFormKey(formKeys.transferInstructionsAccountNumber)
						) || [''],
						feilds: [
							{
								item: formKeys.transferInstructionsAccountNumber,
								renderType: 'renderFormattedInput',
								text: 'Transfering account number',
								feildProps: (loopIndex: number) => [
									'*************************',
									{},
								],
							},
						],
					},
					{
						item: formKeys.transferInstructions,
						renderType: 'renderDropdown',
						text: 'Transfer instructions',
						feildProps: [
							enums[formKeys.transferInstructions.enum],
							[],
							false,
							false,
							{},
						],
					},
					{
						item: formKeys.maturityDate,
						renderType: 'renderDate',
						text: 'Maturity date',
						conditional:
							get(formValues, getFormKey(formKeys.transferInstructions)) ===
							transferInstructionsMeturityDate,
					},
					{
						item: formKeys.liquidationInstructions,
						renderType: 'renderDropdown',
						text: 'Liquidation instructions',
						feildProps: [
							enums[formKeys.liquidationInstructions.enum],
							[],
							false,
							false,
							{},
						],
					},
					{
						item: formKeys.partialLiquidationAmount2,
						renderType: 'renderCurrency',
						text: 'Partial ($)',
						conditional:
							get(formValues, getFormKey(formKeys.liquidationInstructions)) ===
							liquidationInstructionsValueOfLiquidateAndTransferOfMyAssets,
					},
					{
						item: formKeys.RMDOptions,
						renderType: 'renderDropdown',
						text: 'RMD options',
						feildProps: [enums[formKeys.RMDOptions.enum], [], false, false, {}],
						conditional: ageOnDec31 >= 72,
					},
				],
			},
		],
		[internalTransferTopLevelInternalExistingCSIJournal]: [
			{
				accordianRequired: false,
				accordinaData: {},
				feildsData: [
					{
						item: formKeys.usedForAccountFunding,
						renderType: 'renderDropdown',
						text: 'Used to fund product',
						feildProps: [
							enums[formKeys.usedForAccountFunding.enum],
							[getFormKey(formKeys.amount)],
						],
					},
					{
						item: formKeys.amount,
						renderType: 'renderCurrency',
						text: 'Amount',
						conditional:
							get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
							true,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[1]?.key,
					isExpanded: expandedItem === accordionIds[1]?.id,
					heading: 'Delivering Account Information',
					onIconClick: () => toogleAccordion(accordionIds[1]?.id),
					getErrorSlice: getErrorSlice,
				},
				feildsData: [
					{
						text: 'Account number',
						renderType: 'renderFormattedInput',
						feildProps: ['*************************', {}],
						item: formKeys.deliveringAccountAccountNumber,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[2]?.key,
					isExpanded: expandedItem === accordionIds[2]?.id,
					heading: 'Transfer Instructions',
					onIconClick: () => toogleAccordion(accordionIds[2]?.id),
					getErrorSlice: getErrorSlice,
				},
				feildsData: [
					{
						text: 'Transfer type',
						renderType: 'renderDropdown',
						feildProps: [
							enums[formKeys.transferInstructionsTransferType.enum],
							getFormKeysArray([
								formKeys.transferIsIraContribution,
								formKeys.transferInstructionsContributionYear,
								formKeys.transferInstructions60DayRollover,
								formKeys.transferInstructionsCashAmountV2,
								formKeys.loopingRowKey,
							]),
						],
						item: formKeys.transferInstructionsTransferType,
					},
					{
						text: 'Is this for an IRA contribution?',
						renderType: 'renderDropdown',
						feildProps: [
							enums[formKeys.transferIsIraContribution.enum],
							getFormKeysArray([
								formKeys.transferInstructionsContributionYear,
								formKeys.transferInstructions60DayRollover,
								formKeys.transferInstructionsCashAmountV2,
							]),
						],
						item: formKeys.transferIsIraContribution,
						conditional:
							isIRA &&
							get(
								formValues,
								getFormKey(formKeys.transferInstructionsTransferType)
							) === transferInstructionsTransferTypePartialAccountValue,
					},
					{
						text: 'IRA contribution year',
						renderType: 'renderDropdown',
						feildProps: [
							enums[formKeys.transferInstructionsContributionYear.enum],
						],
						item: formKeys.transferInstructionsContributionYear,
						conditional:
							get(
								formValues,
								getFormKey(formKeys.transferIsIraContribution)
							) === true &&
							get(
								formValues,
								getFormKey(formKeys.transferInstructionsTransferType)
							) === transferInstructionsTransferTypePartialAccountValue,
					},
					{
						text: '60 Day rollover',
						renderType: 'renderDropdown',
						feildProps: [
							enums[formKeys.transferInstructions60DayRollover.enum],
							[
								...getFormKeysArray([
									formKeys.transferInstructionsCashAmountV2,
								]),
								{
									loopKey: getFormKey(formKeys.loopingRowKey),
									key: getFormKey(formKeys.partialLiquidationAmountType),
								},
								{
									loopKey: getFormKey(formKeys.loopingRowKey),
									key: getFormKey(formKeys.partialLiquidationAmountType),
								},
								{
									loopKey: getFormKey(formKeys.loopingRowKey),
									key: getFormKey(
										formKeys.partialAccountTransferAmountOfShares
									),
								},
								{
									loopKey: getFormKey(formKeys.loopingRowKey),
									key: getFormKey(formKeys.partialLiquidationAmount),
								},
							],
						],
						item: formKeys.transferInstructions60DayRollover,
						conditional:
							get(
								formValues,
								getFormKey(formKeys.transferIsIraContribution)
							) === false &&
							isIRA &&
							get(
								formValues,
								getFormKey(formKeys.transferInstructionsTransferType)
							) === transferInstructionsTransferTypePartialAccountValue,
					},
					{
						text: 'Cash amount',
						renderType: 'renderCurrency',
						item: formKeys.transferInstructionsCashAmountV2,
						conditional:
							get(
								formValues,
								getFormKey(formKeys.transferInstructionsTransferType)
							) === transferInstructionsTransferTypePartialAccountValue &&
							(get(
								formValues,
								getFormKey(formKeys.transferIsIraContribution)
							) === true ||
								get(
									formValues,
									getFormKey(formKeys.transferInstructions60DayRollover)
								) === true),
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[3]?.key,
					isExpanded: expandedItem === accordionIds[3]?.id,
					heading: 'Partial Account Transfer',
					onIconClick: () => toogleAccordion(accordionIds[3]?.id),
					getErrorSlice: getErrorSlice,
				},
				conditional:
					get(
						formValues,
						getFormKey(formKeys.transferInstructionsTransferType)
					) === transferInstructionsTransferTypePartialAccountValue,
				feildsData: [
					{
						isLoop: true,
						loopItem: formKeys.loopingRowKey,
						data: curLoopRowData,

						feilds: [
							{
								text: 'Asset description',
								renderType: 'renderInput',
								item: formKeys.partialAccountTransferDescription,
								feildProps: (loopIndex: number) => [{}],
							},
							{
								text: 'Symbol',
								renderType: 'renderInput',
								item: formKeys.partialAccountTransferSymbol,
								feildProps: (loopIndex: number) => [
									{
										allowedCharacters: 'text_and_number',
										textTransform: 'uppercase',
										maxCharLength: 9,
									},
								],
							},
							{
								text: 'Amount type',
								renderType: 'renderDropdown',
								item: formKeys.partialLiquidationAmountType,
								conditional: (loopIndex: number) =>
									get(
										formValues,
										getFormKey(formKeys.transferIsIraContribution)
									) !== true &&
									!get(
										formValues,
										getFormKey(formKeys.transferInstructions60DayRollover)
									),
								feildProps: (loopIndex: number) => [
									enums[formKeys.partialLiquidationAmountType.enum],
									[
										{
											loopKey: getFormKey(formKeys.loopingRowKey),
											loopIndex: loopIndex,
											key: getFormKey(
												formKeys.partialAccountTransferAmountOfShares
											),
										},
										{
											loopKey: getFormKey(formKeys.loopingRowKey),
											loopIndex: loopIndex,
											key: getFormKey(formKeys.partialLiquidationAmount),
										},
									],
									false,
									false,
									{},
								],
							},
							{
								text: 'Number of shares',
								renderType: 'renderInput',
								item: formKeys.partialAccountTransferAmountOfShares,
								feildProps: (loopIndex: number) => [
									{
										allowedCharacters: 'number_only',
									},
								],
								conditional: (loopIndex: number) =>
									get(
										formValues,
										`${getFormKey(
											formKeys.loopingRowKey
										)}[${loopIndex}].${getFormKey(
											formKeys.partialLiquidationAmountType
										)}`
									) === amountTypeOptionsValueOfShares,
							},

							{
								text: 'Amount ($)',
								renderType: 'renderCurrency',
								item: formKeys.partialLiquidationAmount,
								feildProps: (loopIndex: number) => [{}],
								conditional: (loopIndex: number) =>
									get(
										formValues,
										`${getFormKey(
											formKeys.loopingRowKey
										)}[${loopIndex}].${getFormKey(
											formKeys.partialLiquidationAmountType
										)}`
									) === amountTypeOptionsValueOfDollar,
							},
						],
					},
					{
						text: 'Notes',
						renderType: 'renderInput',
						item: formKeys.note,
						feildProps: [
							{
								showAsMandatory: false,
							},
						],
					},
				],
			},
		],
	};
	return (
		<RenderFeildsLoop
			loopData={loopDataMap[selectedTransferType]}
			renderFeildComponents={renderFeildComponents}
			feildsMetaData={feildsMetaData}
			accordianMetaData={accordianMetaData}
			getErrorSlice={getErrorSlice}
			onAddRow={onAddRow}
			onDeleteRow={onDeleteRow}
		/>
	);
};
export default InternalTransfer;

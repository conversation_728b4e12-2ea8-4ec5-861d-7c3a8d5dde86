import {
	createRef,
	FC,
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import Box from '@atomic/box';
import { IFeildSectionTypes } from './types';
import { styles } from './style';
import classNames from 'classnames';
import { get, isEmpty, set, isString, isObject } from 'lodash';
import { getFeildRenders } from './helper';
import { v4 as uuid } from 'uuid';
import Text from '@atomic/text';
import {
	internalTransferTypeOptionsValueOfAllCash,
	internalTransferTypeOptionsValueOfSecurities,
	internalTransferTypeOptionsValueOfSpecificAmountOfCash,
	transferInstructionsTransferTypeFullAccountValue,
	transferInstructionsTransferTypePartialAccountValue,
} from '../../constants';

import RenderFeildsLoop from './renderFeildsLoop';

const accordionIds = [
	{ id: '0', key: 'firstAccordian' },
	{ id: '1', key: 'deliveringAccountInformation' },
	{ id: '2', key: 'recievingAccountInformation' },
	{ id: '3', key: 'transferInstructions' },
	{ id: '4', key: 'partialAccountTransfer' },
];

const InternalTransfer: FC<IFeildSectionTypes> = (props) => {
	const componentStyles = styles(props);
	const {
		onChangeFeild,
		formKeys,
		formValues = {},
		error = {},
		countryList = [],
		stateList = [],
		nameAppending,
		isIRA,
		sourceMetaData,
		enums,
		triggerFormUpdate,
	} = props;
	const fieldProps = useMemo(
		() => sourceMetaData?.fieldProps || {},
		[sourceMetaData]
	);
	const feildsMetaData = useMemo(
		() => sourceMetaData?.fieldConfigs || {},
		[sourceMetaData]
	);
	const accordianMetaData = useMemo(
		() => sourceMetaData?.accordianConfigs || {},
		[sourceMetaData]
	);
	const refsById = useRef({});
	const getRefById = (id) => {
		if (!refsById.current[id]) {
			refsById.current[id] = createRef();
		}
		return refsById.current[id];
	};
	const detailsTobeProvidedClearKeys = useMemo(() => {
		return Object.values(formKeys || {}).reduce((acc: any, formKey: any) => {
			const sourceTypeValue = (sourceMetaData?.topLevel || [])[0]?.key;
			if (
				formKey.key !== sourceTypeValue &&
				formKey.key !==
					(feildsMetaData[formKeys.detailsToBeProvided.id]?.key ||
						formKeys.detailsToBeProvided.key)
			) {
				acc.push(feildsMetaData[formKeys[formKey.id]]?.key || formKey.key);
			}
			return acc;
		}, []);
	}, [formKeys, feildsMetaData]);
	const getFormKey = useCallback(
		(idKey: any) => {
			return feildsMetaData[idKey.id]?.key || idKey.key;
		},
		[feildsMetaData]
	);
	const curLoopRowData = get(formValues, getFormKey(formKeys.loopingRowKey), [
		{ key: uuid() },
	]);

	const [expandedItem, setExpandedItem] = useState(accordionIds[1]?.id);
	const whatAreyouTransferingValue =
		get(
			formValues,
			feildsMetaData[formKeys.transferInstructionsWhatAreYouTransfering.id]
				?.key || formKeys.transferInstructionsWhatAreYouTransfering.key
		) || [];
	const isIraContributionValue = get(
		formValues,
		feildsMetaData[formKeys.transferIsIraContribution.id]?.key ||
			formKeys.transferIsIraContribution.key
	);

	const handleChange = useCallback(
		(
			value: any,
			key: any,
			clearKeys?: any[],
			loopIndex?: number,
			loopKey?: string,
			triggerFormUpdate?: boolean,
			triggerFormValueUpdate?: boolean
		) => {
			let curData = value;
			let curKey = key;
			if (loopIndex >= 0) {
				curData = get(formValues, loopKey, [{ key: uuid() }]);
				curData[loopIndex] = set(
					curData[loopIndex] || { key: uuid() },
					key,
					value
				);
				curKey = loopKey;
			}
			if (
				curKey === getFormKey(formKeys.transferInstructionsTransferType) &&
				curData === transferInstructionsTransferTypePartialAccountValue
			) {
				onChangeFeild(
					false as any,
					getFormKey(formKeys.transferIsIraContribution),
					[],
					true
				);
			}
			onChangeFeild(
				curData,
				curKey,
				clearKeys,
				triggerFormUpdate,
				triggerFormValueUpdate
			);
		},
		[onChangeFeild, formValues, formKeys]
	);
	useEffect(() => {
		if (
			whatAreyouTransferingValue.includes(
				internalTransferTypeOptionsValueOfSecurities
			) &&
			isIraContributionValue === true
		) {
			handleChange(
				whatAreyouTransferingValue.filter(
					(val) => val !== internalTransferTypeOptionsValueOfSecurities
				),
				feildsMetaData[formKeys.transferInstructionsWhatAreYouTransfering.id]
					?.key || formKeys.transferInstructionsWhatAreYouTransfering.key
			);
		}
	}, [whatAreyouTransferingValue, isIraContributionValue]);
	const toogleAccordion = (id) => {
		setExpandedItem(expandedItem === id ? '' : id);
	};
	const getErrorSlice = useCallback(
		(key, oneToMenyKey, oneToManyLoopIndex) => {
			const errorMessage =
				oneToMenyKey && oneToManyLoopIndex >= 0
					? get(error, `${oneToMenyKey}.${oneToManyLoopIndex}.${key}`)
					: get(error, key);
			return !isEmpty(errorMessage)
				? isString(errorMessage)
					? { message: errorMessage }
					: isObject(errorMessage) &&
					  (errorMessage as any)?.message &&
					  errorMessage
				: null;
		},
		[error]
	);
	const onAddRow = useCallback(
		(loopData, loopKey) => {
			const curValues = [...loopData];
			curValues.push({ key: uuid() });
			handleChange(curValues, loopKey);
		},
		[handleChange]
	);
	const onDeleteRow = useCallback(
		(index, loopData, loopKey) => {
			const curValues = [...loopData];
			curValues.splice(index, 1);
			handleChange(curValues, loopKey, [], -1, '', true, true);
		},
		[handleChange]
	);
	const renderField = useCallback(
		(
			Component: React.ElementType,
			props: Record<string, any>,
			noWrapperRequired: boolean,
			additionalProps: any = {}
		) => {
			const { minWidth, maxWidth, doubleWrapperRequired } =
				additionalProps || {};

			return noWrapperRequired ? (
				<Component {...props} />
			) : doubleWrapperRequired ? (
				<Box display="block" width="100%">
					<Component {...props} />
				</Box>
			) : (
				<Box
					className={classNames(componentStyles.fieldInputWrapper)}
					minWidth={minWidth}
					maxWidth={maxWidth}
				>
					<Component {...props} />
				</Box>
			);
		},
		[componentStyles.fieldInputWrapper]
	);
	const renderFeildComponents: any = useMemo(() => {
		return getFeildRenders(
			renderField,
			formValues,
			handleChange,
			getErrorSlice,
			countryList,
			stateList,
			nameAppending,
			getRefById,
			triggerFormUpdate,
			fieldProps
		);
	}, [
		renderField,
		fieldProps,
		formValues,
		handleChange,
		getErrorSlice,
		countryList,
		stateList,
		nameAppending,
		triggerFormUpdate,
	]);
	const shouldShowField = useCallback(
		(keys: string[]) => {
			return keys.some((key) => feildsMetaData[key]?.on !== false);
		},
		[feildsMetaData]
	);

	const getFormKeysArray = useCallback(
		(ids: Array<any>) => {
			return ids.map((itm: any) => feildsMetaData[itm.id]?.key || itm.key);
		},
		[feildsMetaData]
	);

	const getLabelText = useCallback(
		(idKey: any, label: string) => {
			return feildsMetaData[idKey.id]?.text || label;
		},
		[feildsMetaData]
	);
	const getEnumValue = (itm) => {
		return enums[feildsMetaData[itm.id]?.enum || itm.enum];
	};
	const getEnumValue2 = (itm) => {
		return enums[(feildsMetaData[itm.id] as any)?.enum2 || itm.enum2];
	};
	const internalTransferV2LoopData = [
		{
			accordianRequired: true,
			accordinaData: {
				key: accordionIds[1]?.key,
				isExpanded: expandedItem === accordionIds[1]?.id,
				heading: 'Delivering Account Information',
				onIconClick: () => toogleAccordion(accordionIds[1]?.id),
			},
			feildsData: [
				{
					item: formKeys.deliveringAccountAccountNumber,
					renderType: 'renderFormattedInput',
					text: 'Account number',
					feildProps: ['*************************'],
				},
				{
					item: formKeys.deliveringAccountAccountTitle,
					renderType: 'renderInput',
					text: 'Account title',
				},
			],
		},
		{
			accordianRequired: true,
			accordinaData: {
				key: accordionIds[2]?.key,
				isExpanded: expandedItem === accordionIds[2]?.id,
				heading: 'Receiving Firm Information',
				onIconClick: () => toogleAccordion(accordionIds[2]?.id),
			},
			feildsData: [
				{
					item: formKeys.requireCostBasisStepUp,
					renderType: 'renderDropdown',
					text: 'Transfer require cost basis step up?',
					feildProps: [
						getEnumValue(formKeys.requireCostBasisStepUp),
						getFormKeysArray([formKeys.dateOfDeath]),
					],
				},
				{
					item: formKeys.dateOfDeath,
					renderType: 'renderDate',
					text: 'Date of death',
					conditional:
						get(formValues, getFormKey(formKeys.requireCostBasisStepUp)) ===
						true,
				},
			],
		},
		{
			accordianRequired: true,
			accordinaData: {
				key: accordionIds[3]?.key,
				isExpanded: expandedItem === accordionIds[3]?.id,
				heading: 'Transfer Instructions',
				onIconClick: () => toogleAccordion(accordionIds[3]?.id),
			},
			feildsData: [
				{
					item: formKeys.transferInstructionsTransferType,
					renderType: 'renderDropdown',
					text: 'Transfer type',
					feildProps: [
						getEnumValue(formKeys.transferInstructionsTransferType),
						getFormKeysArray([
							formKeys.transferInstructionsCloseAccount,
							formKeys.transferInstructionsContributionYear,
							formKeys.transferInstructionsWhatAreYouTransfering,
							formKeys.transferInstructionsCashTransferMethod,
							formKeys.transferInstructionsCashAmount,
							formKeys.loopingRowKey,
						]),
					],
				},
				{
					item: formKeys.transferInstructionsCloseAccount,
					renderType: 'renderDropdown',
					text: 'Close account',
					feildProps: [getEnumValue(formKeys.transferInstructionsCloseAccount)],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.transferInstructionsTransferType)
						) === transferInstructionsTransferTypeFullAccountValue,
				},
				{
					item: formKeys.transferInstructionsWhatAreYouTransfering,
					renderType: 'renderCheckboxGroup',
					text: 'What are you transferring?',
					feildProps: [
						get(formValues, getFormKey(formKeys.transferIsIraContribution)) ===
						false
							? getEnumValue2(
									formKeys.transferInstructionsWhatAreYouTransfering
							  )
							: getEnumValue(
									formKeys.transferInstructionsWhatAreYouTransfering
							  ),
						getFormKeysArray[formKeys.transferInstructionsCashAmount],
						false,
						false,
					],
					conditional:
						get(
							formValues,
							getFormKey(formKeys.transferInstructionsTransferType)
						) === transferInstructionsTransferTypePartialAccountValue,
				},
				{
					item: formKeys.transferInstructionsCashTransferMethod,
					renderType: 'renderDropdown',
					text: 'Cash transfer method',
					feildProps: [
						getEnumValue(formKeys.transferInstructionsCashTransferMethod),
						getFormKeysArray([formKeys.transferInstructionsCashAmount]),
					],
					conditional: (
						get(
							formValues,
							getFormKey(formKeys.transferInstructionsWhatAreYouTransfering)
						) || []
					).includes(internalTransferTypeOptionsValueOfAllCash),
				},
				{
					item: formKeys.transferIsIraContribution,
					renderType: 'renderDropdown',
					text: 'Is this for an IRA contribution?',
					feildProps: [
						getEnumValue(formKeys.transferIsIraContribution),
						getFormKeysArray([formKeys.transferInstructionsContributionYear]),
					],
					conditional:
						(
							get(
								formValues,
								getFormKey(formKeys.transferInstructionsWhatAreYouTransfering)
							) || []
						).includes(internalTransferTypeOptionsValueOfAllCash) && isIRA,
				},
				{
					item: formKeys.transferInstructionsContributionYear,
					renderType: 'renderDropdown',
					text: 'IRA contribution year',
					feildProps: [
						getEnumValue(formKeys.transferInstructionsContributionYear),
					],
					conditional:
						get(formValues, getFormKey(formKeys.transferIsIraContribution)) ===
						true,
				},
				{
					item: formKeys.transferInstructionsCashAmount,
					renderType: 'renderCurrency',
					text: 'Cash amount',
					conditional:
						get(
							formValues,
							getFormKey(formKeys.transferInstructionsCashTransferMethod)
						) === internalTransferTypeOptionsValueOfSpecificAmountOfCash,
				},
			],
		},
		{
			accordianRequired: true,
			accordinaData: {
				key: accordionIds[4]?.key,
				isExpanded: expandedItem === accordionIds[4]?.id,
				heading: 'Partial Account Transfer',
				onIconClick: () => toogleAccordion(accordionIds[4]?.id),
			},
			conditional:
				get(
					formValues,
					getFormKey(formKeys.transferInstructionsTransferType)
				) === transferInstructionsTransferTypePartialAccountValue,
			feildsData: [
				{
					isLoop: true,
					loopItem: formKeys.loopingRowKey,
					data: curLoopRowData,
					feilds: [
						{
							text: 'Asset description',
							renderType: 'renderInput',
							item: formKeys.partialAccountTransferDescription,
							feildProps: (loopIndex) => [{}],
						},
						{
							text: 'Amount type',
							renderType: 'renderDropdown',
							item: formKeys.partialLiquidationAmountType,
							feildProps: (loopIndex) => [
								getEnumValue(formKeys.partialLiquidationAmountType),
								[],
								false,
								false,
								{},
							],
							conditional: (loopIndex) =>
								get(
									formValues,
									getFormKey(formKeys.transferIsIraContribution)
								) === true,
						},
						{
							text: 'Symbol',
							renderType: 'renderInput',
							item: formKeys.partialAccountTransferSymbol,
							feildProps: (loopIndex) => [
								{
									allowedCharacters: 'text_and_number',
									textTransform: 'uppercase',
									maxCharLength: 9,
								},
							],
						},
						{
							text: 'Quantity',
							renderType: 'renderInput',
							item: formKeys.partialAccountTransferQuantity,
							feildProps: (loopIndex) => [
								{
									allowedCharacters: 'number_only',
								},
							],
						},
					],
				},
				{
					text: 'Notes',
					renderType: 'renderInput',
					item: formKeys.note,
				},
			],
		},
	];
	return (
		<Box
			className={
				get(formValues, getFormKey(formKeys.detailsToBeProvided))
					? classNames(
							componentStyles.feildsWrapper,
							componentStyles.sectionMarginBottom
					  )
					: classNames(componentStyles.feildsWrapper)
			}
		>
			{shouldShowField([formKeys.detailsToBeProvided.id]) && (
				<Box
					className={`${classNames(
						componentStyles.InformationToBeSuppliedLater,
						componentStyles.sectionMarginTop
					)} label`}
				>
					<Text>
						{getLabelText(
							formKeys.detailsToBeProvided,
							'Information to be supplied later'
						)}
					</Text>
					{renderFeildComponents.renderCheckbox(
						getFormKey(formKeys.detailsToBeProvided),
						detailsTobeProvidedClearKeys
					)}
				</Box>
			)}
			{!get(formValues, getFormKey(formKeys.detailsToBeProvided)) && (
				<RenderFeildsLoop
					loopData={internalTransferV2LoopData as any}
					renderFeildComponents={renderFeildComponents}
					feildsMetaData={feildsMetaData}
					accordianMetaData={accordianMetaData}
					getErrorSlice={getErrorSlice}
					onAddRow={onAddRow}
					onDeleteRow={onDeleteRow}
				/>
			)}
		</Box>
	);
};
export default InternalTransfer;

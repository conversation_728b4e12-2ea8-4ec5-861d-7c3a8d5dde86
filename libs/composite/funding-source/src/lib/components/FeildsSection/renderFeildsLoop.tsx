import { useCallback } from 'react';
import Box from '@atomic/box';
import Text from '@atomic/text';
import { styles } from './style';
import classNames from 'classnames';
import { RenderFeildsLoopProps } from './types';
import AccordianWrapper from './accordianWrapper';
import { get, isNumber } from 'lodash';
import { IconCodes } from '@atomic/icon';
import Icon from '@atomic/icon';
import { ApexTheme } from '@base/theme';
import { useTheme } from 'react-jss';
import { accountTypeStaticNameMaping } from '../../constants';
import Tooltip, { TooltipPosition, TooltipType } from '@atomic/tooltip';

const RenderFeildsLoop = (props: RenderFeildsLoopProps) => {
	const componentStyles = styles(props as any);
	const {
		loopData = [],
		renderFeildComponents,
		feildsMetaData,
		accordianMetaData = {},
		onAddRow,
		onDeleteRow,
		getErrorSlice,
		registrationType,
		registrationTypes = [],
	} = props;
	const registeredOption = Array.isArray(registrationTypes)
		? registrationTypes.find((itm) => itm.code === registrationType)
		: {};

	const shouldShowAccordian = useCallback(
		(key) => {
			return accordianMetaData[key]?.on !== false;
		},
		[accordianMetaData]
	);

	const shouldShowField = useCallback(
		(keys: any[]) => {
			return keys.some((key) => feildsMetaData[key]?.on !== false);
		},
		[feildsMetaData]
	);
	const theme: ApexTheme = useTheme();
	const getFormKey = useCallback(
		(idKey: any) => {
			return feildsMetaData[idKey.id]?.key || idKey.key;
		},
		[feildsMetaData]
	);

	const getLabelText = useCallback(
		(idKey: any, label: string) => {
			return feildsMetaData[idKey.id]?.text || label;
		},
		[feildsMetaData]
	);
	const checkMaximumCountReached = useCallback(
		(loopItem) => {
			return feildsMetaData[loopItem.id]?.count || 20;
		},
		[feildsMetaData]
	);
	const checkValueOfLoopHasValue = useCallback((data = [], loopIndex) => {
		const currentLoopData = data[loopIndex] || {};

		return Object.keys(currentLoopData).some(
			(keyName) =>
				keyName !== 'key' &&
				get(currentLoopData, keyName) !== '' &&
				get(currentLoopData, keyName) !== null
		);
	}, []);
	const feildsDataRender = useCallback(
		(feildsData: any, loopIndex?: number, loopItem?: any, arrayLoop?: any) => {
			const loopDataRender = (loopData: any) => {
				const {
					data,
					feilds,
					loopItem,
					conditional = true,
					arrayLoop,
				} = loopData;
				const loopFeildIds = feilds.map((itm) => itm?.item?.id);
				return (
					conditional &&
					shouldShowField(loopFeildIds) && (
						<Box
							className={classNames(
								componentStyles.feildsAccordianfiledsWrapperAllTogether
							)}
							width="100%"
						>
							{data.map((feild: any, index) => {
								const checkValueExist = checkValueOfLoopHasValue(data, index);

								return (
									<Box flexDirection="row" key={index}>
										{feildsDataRender(feilds, index, loopItem, arrayLoop)}
										<Box
											className={classNames(componentStyles.addDeleteSection)}
										>
											{checkMaximumCountReached(loopItem) > data.length && (
												<Box>
													{checkValueExist ? (
														<Icon
															icon={IconCodes.icon_Bd_Plus_Circle}
															className={classNames(componentStyles.addIcon)}
															onClick={() =>
																onAddRow(data, getFormKey(loopItem), arrayLoop)
															}
														/>
													) : (
														<Tooltip
															content={
																'Please fill out this row before adding another'
															}
															type={TooltipType.Tooltip}
															placement={TooltipPosition.BottomCenter}
															triggerOffset={10}
														>
															<Box>
																<Icon
																	icon={IconCodes.icon_Bd_Plus_Circle}
																	className={classNames(
																		componentStyles.addIconDisabled
																	)}
																/>
															</Box>
														</Tooltip>
													)}
												</Box>
											)}
											{data.length > 1 && (
												<Icon
													icon={IconCodes.icon_Bd_Delete}
													onClick={() =>
														onDeleteRow(index, data, getFormKey(loopItem))
													}
													className={classNames(componentStyles.deleteIcon)}
												/>
											)}
										</Box>
									</Box>
								);
							})}
						</Box>
					)
				);
			};

			return (
				<Box className={classNames(componentStyles.feildsInputBoxWrapper)}>
					{feildsData.map((feild: any) => {
						const {
							renderType,
							item,
							text,
							isLoop,
							isStatic,
							data = [],
							feildProps = isNumber(loopIndex) ? (i) => [] : [],
							additionalProps,
							conditional = isNumber(loopIndex) ? (i) => true : true,
						} = feild;
						return isLoop ? (
							loopDataRender(feild)
						) : isStatic ? (
							<Box
								className={classNames(
									componentStyles.staticValueWrapper,
									componentStyles.sectionMarginTop
								)}
							>
								{data.map((staticValue) => {
									return (
										<Box className={classNames(componentStyles.staticBox)}>
											<Icon
												icon={staticValue.icon}
												className={classNames(componentStyles.staticBoxIcon)}
												iconStyle={{
													fontSize: theme.fontSizes.desktop.bHuge,
												}}
											/>
											<Text
												className={classNames(
													componentStyles.staticBoxIconContent
												)}
											>
												{staticValue.name}:
											</Text>
											<Text
												className={classNames(
													componentStyles.staticBoxIconContentValue
												)}
											>
												{staticValue.type === 'accountType'
													? accountTypeStaticNameMaping[
															registeredOption?.name?.toLowerCase()
													  ] ||
													  registeredOption?.name ||
													  registrationType
													: staticValue.value}
											</Text>
										</Box>
									);
								})}
							</Box>
						) : (
							shouldShowField([item?.id]) &&
							(isNumber(loopIndex) ? conditional(loopIndex) : conditional) &&
							renderFeildComponents[renderType](
								getFormKey(item),
								getLabelText(item, text),
								...(isNumber(loopIndex) ? feildProps(loopIndex) : feildProps),
								isNumber(loopIndex) && {
									oneToManyLoopIndex: loopIndex,
									oneToMenyKey: getFormKey(loopItem),
									arrayLoop,
									...additionalProps,
								}
							)
						);
					})}
				</Box>
			);
		},
		[
			getFormKey,
			getLabelText,
			onAddRow,
			onDeleteRow,
			renderFeildComponents,
			shouldShowField,
			checkMaximumCountReached,
			componentStyles,
			theme,
		]
	);
	return (
		<Box
			className={classNames(
				componentStyles.feildsWrapper,
				componentStyles.sectionMarginBottom
			)}
		>
			{loopData.map((feild: any) => {
				const {
					accordianRequired,
					accordinaData,
					feildsData,
					conditional = true,
				} = feild;
				const formIds = feildsData.reduce((acc: any, itms: any) => {
					const loopData = itms?.isLoop
						? itms.feilds.map((itm: any) => {
								return {
									feild: itm.item,
									currentLength: itms.data.length,
									oneToMenyKey: getFormKey(itms.loopItem),
								};
						  })
						: itms?.item
						? [itms?.item]
						: [];

					return Array.isArray(loopData) && loopData[0]
						? [...acc, ...loopData]
						: acc;
				}, []);
				const isShowAccordian = shouldShowAccordian(accordinaData?.key);
				if (!isShowAccordian) {
					return null;
				}
				return accordianRequired
					? conditional && (
							<AccordianWrapper
								{...(accordinaData || {})}
								getFormKey={getFormKey}
								shouldShowField={shouldShowField}
								formIds={formIds}
								getErrorSlice={getErrorSlice}
							>
								<Box
									className={classNames(
										componentStyles.feildsAccordianfiledsWrapperAllTogether
									)}
								>
									{feildsDataRender(feildsData)}
								</Box>
							</AccordianWrapper>
					  )
					: feildsDataRender(feildsData);
			})}
		</Box>
	);
};
export default RenderFeildsLoop;

import { createRef, FC, useCallback, useMemo, useRef, useState } from 'react';
import Box from '@atomic/box';
import Icon from '@atomic/icon';
import { IFeildSectionTypes } from './types';
import { styles } from './style';
import classNames from 'classnames';
import { get, isEmpty, set, isString, isObject } from 'lodash';
import { v4 as uuid } from 'uuid';
import { getFeildRenders, isNotEmptyAndNull } from './helper';
import Text from '@atomic/text';
import { transferTypeNonACATValueOfBrockeragePartial } from '../../constants';
import { IconCodes } from '@base/icon';
import AccordianWrapper from './accordianWrapper';
import { TooltipType } from '@atomic/tooltip';
import { TooltipPosition } from '@atomic/tooltip';
import Tooltip from '@atomic/tooltip';

const accordionIds = [
	{ id: '0', key: 'receivingFirmInformation' },
	{ id: '1', key: 'deliveringAccountInformation' },
	{ id: '2', key: 'transferInstructions' },
	{ id: '3', key: 'accountRegistrationDifferences' },
	{ id: '4', key: 'oneAndTheSamePersonCertification' },
];
const ExtNonAcat: FC<IFeildSectionTypes> = (props) => {
	const componentStyles = styles(props);
	const {
		onChangeFeild,
		formKeys,
		formValues = {},
		error = {},
		countryList = [],
		stateList = [],
		ssn,
		nameAppending,
		sourceMetaData,
		enums,
		triggerFormUpdate,
	} = props;
	const fieldProps = useMemo(
		() => sourceMetaData?.fieldProps || {},
		[sourceMetaData]
	);
	const feildsMetaData = useMemo(
		() => sourceMetaData?.fieldConfigs || {},
		[sourceMetaData]
	);
	const refsById = useRef({});
	const getRefById = (id) => {
		if (!refsById.current[id]) {
			refsById.current[id] = createRef();
		}
		return refsById.current[id];
	};
	const [expandedItem, setExpandedItem] = useState(accordionIds[1]?.id);

	const detailsTobeProvidedClearKeys = useMemo(() => {
		return Object.values(formKeys || {}).reduce((acc: any, formKey: any) => {
			const sourceTypeValue = (sourceMetaData?.topLevel || [])[0]?.key;
			if (
				formKey.key !== sourceTypeValue &&
				formKey.key !==
					(feildsMetaData[formKeys.detailsToBeProvided.id]?.key ||
						formKeys.detailsToBeProvided.key)
			) {
				acc.push(feildsMetaData[formKeys[formKey.id]]?.key || formKey.key);
			}
			return acc;
		}, []);
	}, [formKeys, feildsMetaData]);
	const curLoopRowValue = get(
		formValues,
		feildsMetaData[formKeys.loopingRowKey.id]?.key ||
			formKeys.loopingRowKey.key,
		[{ key: uuid() }]
	);
	const curLoopRowData =
		Array.isArray(curLoopRowValue) && curLoopRowValue.length === 0
			? [{ key: uuid() }]
			: curLoopRowValue;
	const curLoop2RowValue = get(
		formValues,
		feildsMetaData[formKeys.loppingRow2Key.id]?.key ||
			formKeys.loppingRow2Key.key,
		[{ key: uuid() }]
	);
	const curLoop2RowData =
		Array.isArray(curLoop2RowValue) && curLoop2RowValue.length === 0
			? [{ key: uuid() }]
			: curLoop2RowValue;

	const handleChange = useCallback(
		(
			value: any,
			key: any,
			clearKeys?: any[],
			loopIndex?: number,
			loopKey?: string,
			triggerFormUpdate?: boolean,
			triggerFormValueUpdate?: boolean
		) => {
			let curData = value;
			let curKey = key;
			if (loopIndex >= 0) {
				curData = get(formValues, loopKey, [{ key: uuid() }]);
				curData[loopIndex] = set(
					curData[loopIndex] || { key: uuid() },
					key,
					value
				);
				curKey = loopKey;
			}
			onChangeFeild(
				curData,
				curKey,
				clearKeys,
				triggerFormUpdate,
				triggerFormValueUpdate
			);
		},
		[onChangeFeild, formValues]
	);
	const toogleAccordion = (id) => {
		setExpandedItem(expandedItem === id ? '' : id);
	};
	const getErrorSlice = useCallback(
		(key, oneToMenyKey, oneToManyLoopIndex) => {
			const errorMessage =
				oneToMenyKey && oneToManyLoopIndex >= 0
					? get(error, `${oneToMenyKey}.${oneToManyLoopIndex}.${key}`)
					: get(error, key);
			return !isEmpty(errorMessage)
				? isString(errorMessage)
					? { message: errorMessage }
					: isObject(errorMessage) &&
					  (errorMessage as any)?.message &&
					  errorMessage
				: null;
		},
		[error]
	);
	const onAddRow = useCallback(
		(loopData, loopKey) => {
			const curValues = [...loopData];
			curValues.push({ key: uuid() });

			handleChange(curValues, loopKey);
		},
		[handleChange]
	);
	const onDeleteRow = useCallback(
		(index, loopData, loopKey) => {
			const curValues = [...loopData];
			curValues.splice(index, 1);
			handleChange(curValues, loopKey, [], -1, '', true, true);
		},
		[handleChange]
	);
	const renderField = useCallback(
		(
			Component: React.ElementType,
			props: Record<string, any>,
			noWrapperRequired: boolean,
			additionalProps: any = {}
		) => {
			const { minWidth, maxWidth, doubleWrapperRequired } =
				additionalProps || {};

			return noWrapperRequired ? (
				<Component {...props} />
			) : doubleWrapperRequired ? (
				<Box display="block" width="100%">
					<Component {...props} />
				</Box>
			) : (
				<Box
					className={classNames(componentStyles.fieldInputWrapper)}
					minWidth={minWidth}
					maxWidth={maxWidth}
				>
					<Component {...props} />
				</Box>
			);
		},
		[componentStyles.fieldInputWrapper]
	);
	const renderFeildComponents: any = useMemo(() => {
		return getFeildRenders(
			renderField,
			formValues,
			handleChange,
			getErrorSlice,
			countryList,
			stateList,
			nameAppending,
			getRefById,
			triggerFormUpdate,
			fieldProps
		);
	}, [
		renderField,
		fieldProps,
		formValues,
		handleChange,
		getErrorSlice,
		countryList,
		stateList,
		nameAppending,
		triggerFormUpdate,
	]);
	const shouldShowField = useCallback(
		(keys: string[]) => {
			return keys.some((key) => feildsMetaData[key]?.on !== false);
		},
		[feildsMetaData]
	);

	const getFormKeysArray = useCallback(
		(ids: Array<any>) => {
			return ids.map((itm: any) => feildsMetaData[itm.id]?.key || itm.key);
		},
		[feildsMetaData]
	);
	const getFormKey = useCallback(
		(idKey: any) => {
			return feildsMetaData[idKey.id]?.key || idKey.key;
		},
		[feildsMetaData]
	);
	const getLabelText = useCallback(
		(idKey: any, label: string) => {
			return feildsMetaData[idKey.id]?.text || label;
		},
		[feildsMetaData]
	);
	const checkMaximumCountReached = useCallback(
		(loopItem) => {
			return feildsMetaData[loopItem.id]?.count || 20;
		},
		[formValues]
	);
	const getEnumValue = (itm) => {
		return enums[feildsMetaData[itm.id]?.enum || itm.enum];
	};
	const checkValueOfLoopHasValue = useCallback((data = [], loopIndex) => {
		const currentLoopData = data[loopIndex] || {};

		return Object.keys(currentLoopData).some(
			(keyName) =>
				keyName !== 'key' &&
				get(currentLoopData, keyName) !== '' &&
				get(currentLoopData, keyName) !== null
		);
	}, []);
	return (
		<Box
			className={classNames(
				componentStyles.feildsWrapper,
				componentStyles.sectionMarginBottom
			)}
		>
			{shouldShowField([formKeys.detailsToBeProvided.id]) && (
				<Box
					className={`${classNames(
						componentStyles.InformationToBeSuppliedLater,
						componentStyles.sectionMarginTop
					)} label`}
				>
					<Text>
						{getLabelText(
							formKeys.detailsToBeProvided,
							'Information to be supplied later'
						)}
					</Text>
					{renderFeildComponents.renderCheckbox(
						getFormKey(formKeys.detailsToBeProvided),
						detailsTobeProvidedClearKeys
					)}
				</Box>
			)}
			{!get(formValues, getFormKey(formKeys.detailsToBeProvided)) && (
				<>
					<AccordianWrapper
						isExpanded={expandedItem === accordionIds[0]?.id}
						heading={'Receiving Firm Information'}
						onIconClick={() => toogleAccordion(accordionIds[0]?.id)}
					>
						<Box
							className={classNames(
								componentStyles.staticValueWrapper,
								componentStyles.sectionMarginTop
							)}
						>
							{sourceMetaData?.firmInformation.map((staticValue) => {
								return (
									<Box className={classNames(componentStyles.staticBox)}>
										<Icon
											icon={staticValue.icon}
											className={classNames(componentStyles.staticBoxIcon)}
										/>
										<Text
											className={classNames(
												componentStyles.staticBoxIconContent
											)}
										>
											{staticValue.name}:
										</Text>
										<Text
											className={classNames(
												componentStyles.staticBoxIconContentValue
											)}
										>
											{staticValue.value}
										</Text>
									</Box>
								);
							})}
						</Box>
					</AccordianWrapper>
					<AccordianWrapper
						isExpanded={expandedItem === accordionIds[1]?.id}
						heading={'Delivering Account Information'}
						onIconClick={() => toogleAccordion(accordionIds[1]?.id)}
						getFormKey={getFormKey}
						shouldShowField={shouldShowField}
						getErrorSlice={getErrorSlice}
						formIds={[
							formKeys.clearingNumber,
							formKeys.accountNumber,
							formKeys.accountTitle,
							formKeys.ssn,
							formKeys.accountType,
							formKeys.firmName,
							formKeys.firmPhoneNumber,
							formKeys.firmAddress,
							formKeys.deliveringAccountTitleSameAsReceiving,
						]}
					>
						<Box
							className={classNames(
								componentStyles.feildsAccordianfiledsWrapperAllTogether
							)}
						>
							<Box
								className={classNames(componentStyles.feildsInputBoxWrapper)}
							>
								{shouldShowField([formKeys.clearingNumber.id]) &&
									renderFeildComponents.renderInput(
										getFormKey(formKeys.clearingNumber),
										getLabelText(formKeys.clearingNumber, 'Clearing number')
									)}
								{shouldShowField([formKeys.firmName.id]) &&
									renderFeildComponents.renderInput(
										getFormKey(formKeys.firmName),
										getLabelText(formKeys.firmName, 'Firm name')
									)}
								{shouldShowField([formKeys.firmPhoneNumber.id]) &&
									renderFeildComponents.renderPhoneNumber(
										getFormKey(formKeys.firmPhoneNumber),
										getLabelText(formKeys.firmPhoneNumber, 'Firm phone number')
									)}
								{renderFeildComponents.renderDropdown(
									getFormKey(formKeys.deliveringAccountTitleSameAsReceiving),
									getLabelText(
										formKeys.deliveringAccountTitleSameAsReceiving,
										'Is the delivering account name and/or title the same as the receiving account?'
									),
									enums[formKeys.deliveringAccountTitleSameAsReceiving.enum],
									getFormKeysArray([
										formKeys.accountTitle,
										formKeys.loppingRow2Key,
									]),
									false,
									false,
									{},
									{ minWidth: 619, maxWidth: 619 }
								)}
								{renderFeildComponents.renderAddress(
									getFormKey(formKeys.firmAddress),
									getLabelText(formKeys.firmAddress, 'Firm address')
								)}
								{shouldShowField([formKeys.accountNumber.id]) &&
									renderFeildComponents.renderFormattedInput(
										getFormKey(formKeys.accountNumber),
										getLabelText(formKeys.accountNumber, 'Account number'),
										'*************************'
									)}
								{shouldShowField([formKeys.accountTitle.id]) &&
									renderFeildComponents.renderInput(
										getFormKey(formKeys.accountTitle),
										getLabelText(formKeys.accountTitle, 'Account title')
									)}
								{shouldShowField([formKeys.ssn.id]) &&
									renderFeildComponents.renderSsn(
										getFormKey(formKeys.ssn),
										getLabelText(formKeys.ssn, 'SSN'),
										{
											disabled: true,
											placeholder: ssn,
										}
									)}
								{shouldShowField([formKeys.accountType.id]) &&
									renderFeildComponents.renderDropdown(
										getFormKey(formKeys.accountType),
										getLabelText(formKeys.accountType, 'Account type'),
										getEnumValue(formKeys.accountType),
										[],
										false,
										true
									)}
							</Box>
						</Box>
					</AccordianWrapper>
					<AccordianWrapper
						isExpanded={expandedItem === accordionIds[2]?.id}
						heading={'Transfer Instructions'}
						onIconClick={() => toogleAccordion(accordionIds[2]?.id)}
						getFormKey={getFormKey}
						shouldShowField={shouldShowField}
						getErrorSlice={getErrorSlice}
						formIds={[
							formKeys.transferType,
							{
								feild: formKeys.transferInstructionsDescription,
								currentLength: curLoopRowData.length,
								oneToMenyKey: getFormKey(formKeys.loopingRowKey),
							},
							{
								feild: formKeys.transferInstructionsSymbol,
								currentLength: curLoopRowData.length,
								oneToMenyKey: getFormKey(formKeys.loopingRowKey),
							},
							{
								feild: formKeys.transferInstructionsQuantity,
								currentLength: curLoopRowData.length,
								oneToMenyKey: getFormKey(formKeys.loopingRowKey),
							},
						]}
					>
						<>
							{shouldShowField([formKeys.transferType.id]) && (
								<Box
									className={classNames(componentStyles.feildsInputBoxWrapper)}
								>
									{renderFeildComponents.renderDropdown(
										getFormKey(formKeys.transferType),
										getLabelText(formKeys.transferType, 'Transfer type'),
										enums[formKeys.transferType.enum],
										getFormKeysArray([formKeys.loopingRowKey]),
										false,
										false,
										{ wrapperClasss: componentStyles.halfFlex }
									)}
								</Box>
							)}
							{shouldShowField([
								formKeys.loopingRowKey.id,
								formKeys.transferInstructionsDescription.id,
								formKeys.transferInstructionsSymbol.id,
								formKeys.transferInstructionsQuantity.id,
							]) &&
								get(formValues, getFormKey(formKeys.transferType)) ===
									transferTypeNonACATValueOfBrockeragePartial &&
								curLoopRowData.map((rowItem, rowIndex) => {
									const checkValueExist = checkValueOfLoopHasValue(
										curLoopRowData,
										rowIndex
									);
									return (
										<Box flexDirection="row">
											<Box
												className={classNames(
													componentStyles.feildsInputBoxWrapper
												)}
											>
												{shouldShowField([
													formKeys.transferInstructionsDescription.id,
												]) &&
													renderFeildComponents.renderInput(
														getFormKey(
															formKeys.transferInstructionsDescription
														),
														getLabelText(
															formKeys.transferInstructionsDescription,
															'Asset description'
														),
														{},
														{
															oneToManyLoopIndex: rowIndex,
															oneToMenyKey: getFormKey(formKeys.loopingRowKey),
														}
													)}
												{shouldShowField([
													formKeys.transferInstructionsSymbol.id,
												]) &&
													renderFeildComponents.renderInput(
														getFormKey(formKeys.transferInstructionsSymbol),
														getLabelText(
															formKeys.transferInstructionsSymbol,
															'CUSIP'
														),
														{
															allowedCharacters: 'text_and_number',
															textTransform: 'uppercase',
															maxCharLength: 9,
														},
														{
															oneToManyLoopIndex: rowIndex,
															oneToMenyKey: getFormKey(formKeys.loopingRowKey),
														}
													)}
												{shouldShowField([
													formKeys.transferInstructionsQuantity.id,
												]) &&
													renderFeildComponents.renderInput(
														getFormKey(formKeys.transferInstructionsQuantity),
														getLabelText(
															formKeys.transferInstructionsQuantity,
															'Quantity'
														),
														{
															allowedCharacters: 'number_only',
														},
														{
															oneToManyLoopIndex: rowIndex,
															oneToMenyKey: getFormKey(formKeys.loopingRowKey),
														}
													)}
											</Box>
											<Box
												className={classNames(componentStyles.addDeleteSection)}
											>
												{checkMaximumCountReached(formKeys.loopingRowKey) >
													curLoopRowData.length && (
													<Box>
														{checkValueExist ? (
															<Icon
																icon={IconCodes.icon_Bd_Plus_Circle}
																onClick={() =>
																	onAddRow(
																		curLoopRowData,
																		getFormKey(formKeys.loopingRowKey)
																	)
																}
																className={classNames(componentStyles.addIcon)}
															/>
														) : (
															<Tooltip
																content={
																	'Please fill out this row before adding another'
																}
																type={TooltipType.Tooltip}
																placement={TooltipPosition.BottomCenter}
																triggerOffset={10}
															>
																<Box>
																	<Icon
																		icon={IconCodes.icon_Bd_Plus_Circle}
																		className={classNames(
																			componentStyles.addIconDisabled
																		)}
																	/>
																</Box>
															</Tooltip>
														)}
													</Box>
												)}
												{curLoopRowData.length > 1 && (
													<Icon
														icon={IconCodes.icon_Bd_Delete}
														onClick={() =>
															onDeleteRow(
																rowIndex,
																curLoopRowData,
																getFormKey(formKeys.loopingRowKey)
															)
														}
														className={classNames(componentStyles.deleteIcon)}
													/>
												)}
											</Box>
										</Box>
									);
								})}
						</>
					</AccordianWrapper>
					{get(
						formValues,
						getFormKey(formKeys.deliveringAccountTitleSameAsReceiving)
					) === false && (
						<AccordianWrapper
							isExpanded={expandedItem === accordionIds[3]?.id}
							heading={'Account Registration Differences'}
							onIconClick={() => toogleAccordion(accordionIds[3]?.id)}
							getFormKey={getFormKey}
							shouldShowField={shouldShowField}
							getErrorSlice={getErrorSlice}
							formIds={[
								formKeys.accountTitle,
								{
									feild: formKeys.accountOwnerOfDeliveringAccount,
									currentLength: curLoop2RowData.length,
									oneToMenyKey: getFormKey(formKeys.loppingRow2Key),
								},
								{
									feild: formKeys.emailAddress,
									currentLength: curLoop2RowData.length,
									oneToMenyKey: getFormKey(formKeys.loppingRow2Key),
								},
							]}
						>
							<>
								{shouldShowField([formKeys.accountTitle.id]) && (
									<Box
										className={classNames(
											componentStyles.feildsInputBoxWrapper
										)}
									>
										{renderFeildComponents.renderInput(
											getFormKey(formKeys.accountTitle),
											getLabelText(
												formKeys.accountTitle,
												'Account Title of Delivering Account'
											),
											{},
											{ wrapperClasss: componentStyles.halfFlex }
										)}
									</Box>
								)}
								{shouldShowField([
									formKeys.accountOwnerOfDeliveringAccount.id,
									formKeys.emailAddress.id,
								]) &&
									curLoop2RowData.map((rowItem, rowIndex) => {
										const checkValueExist = checkValueOfLoopHasValue(
											curLoop2RowData,
											rowIndex
										);
										return (
											<Box flexDirection="row">
												<Box
													className={classNames(
														componentStyles.feildsInputBoxWrapper
													)}
												>
													{shouldShowField([
														formKeys.accountOwnerOfDeliveringAccount.id,
													]) &&
														renderFeildComponents.renderInput(
															getFormKey(
																formKeys.accountOwnerOfDeliveringAccount
															),
															getLabelText(
																formKeys.accountOwnerOfDeliveringAccount,
																'Account owner of delivering account'
															),
															{},
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loppingRow2Key
																),
															}
														)}

													{shouldShowField([formKeys.emailAddress.id]) &&
														renderFeildComponents.renderEmail(
															getFormKey(formKeys.emailAddress),
															getLabelText(
																formKeys.emailAddress,
																'Email address'
															),
															{},
															{
																oneToManyLoopIndex: rowIndex,
																oneToMenyKey: getFormKey(
																	formKeys.loppingRow2Key
																),
															}
														)}
												</Box>
												<Box
													className={classNames(
														componentStyles.addDeleteSection
													)}
												>
													{checkMaximumCountReached(formKeys.loppingRow2Key) >
														curLoop2RowData.length && (
														<Box>
															{checkValueExist ? (
																<Icon
																	icon={IconCodes.icon_Bd_Plus_Circle}
																	onClick={() =>
																		onAddRow(
																			curLoop2RowData,
																			getFormKey(formKeys.loppingRow2Key)
																		)
																	}
																	className={classNames(
																		componentStyles.addIcon
																	)}
																/>
															) : (
																<Tooltip
																	content={
																		'Please fill out this row before adding another'
																	}
																	type={TooltipType.Tooltip}
																	placement={TooltipPosition.BottomCenter}
																	triggerOffset={10}
																>
																	<Box>
																		<Icon
																			icon={IconCodes.icon_Bd_Plus_Circle}
																			className={classNames(
																				componentStyles.addIconDisabled
																			)}
																		/>
																	</Box>
																</Tooltip>
															)}
														</Box>
													)}
													{curLoop2RowData.length > 1 && (
														<Icon
															icon={IconCodes.icon_Bd_Delete}
															onClick={() =>
																onDeleteRow(
																	rowIndex,
																	curLoop2RowData,
																	getFormKey(formKeys.loppingRow2Key)
																)
															}
															className={classNames(componentStyles.deleteIcon)}
														/>
													)}
												</Box>
											</Box>
										);
									})}
							</>
						</AccordianWrapper>
					)}
					<AccordianWrapper
						isExpanded={expandedItem === accordionIds[4]?.id}
						heading={'One and the same person certification'}
						onIconClick={() => toogleAccordion(accordionIds[4]?.id)}
						getFormKey={getFormKey}
						shouldShowField={shouldShowField}
						getErrorSlice={getErrorSlice}
						formIds={[
							formKeys.printNameOnAccount,
							formKeys.printAlternateNameOnDeliveringAccount,
						]}
					>
						{shouldShowField([
							formKeys.printNameOnAccount.id,
							formKeys.printAlternateNameOnDeliveringAccount.id,
						]) && (
							<Box
								className={classNames(componentStyles.feildsInputBoxWrapper)}
							>
								{shouldShowField([formKeys.printNameOnAccount.id]) &&
									renderFeildComponents.renderInput(
										getFormKey(formKeys.printNameOnAccount),
										getLabelText(
											formKeys.printNameOnAccount,
											'Print name on account'
										),
										{ showAsMandatory: false }
									)}
								{shouldShowField([
									formKeys.printAlternateNameOnDeliveringAccount.id,
								]) &&
									renderFeildComponents.renderInput(
										getFormKey(formKeys.printAlternateNameOnDeliveringAccount),
										getLabelText(
											formKeys.printAlternateNameOnDeliveringAccount,
											'Print alternate name on delivering account'
										),
										{ showAsMandatory: false }
									)}
							</Box>
						)}
					</AccordianWrapper>
				</>
			)}
		</Box>
	);
};
export default ExtNonAcat;

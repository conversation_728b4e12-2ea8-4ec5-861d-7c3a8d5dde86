import { isNull, isEmpty, get, isNil } from 'lodash';
import Dropdown from '@atomic/dropdown';
import Currency from '@atomic/currency';
import Address from '@atomic/address';
import Radiogroup, { Orientation } from '@atomic/radiogroup';
import Input, { DateFormat, InputTextAlignment } from '@atomic/input';
import Email from '@atomic/email';
import Checkbox, { CheckBoxSizes, CheckboxTypes } from '@atomic/checkbox';
import CheckboxGroup from '@atomic/checkboxgroup';
import { IconCodes } from '@base/icon';
import InputFormatComponent from './inputFormat';
import Percentage from '@atomic/percentage';
import Phonenumber from '@atomic/phonenumber';
import Numeric from '@atomic/numeric';
import SSN from '@atomic/ssn';
import Datepicker from '@composite/datepicker';
import Toggle, { type } from '@atomic/toggle';
import { DeviceTypes, jiffy as theme } from '@base/theme';

export const isNotEmptyAndNull = (value) => {
	return !isNull(value) && !isEmpty(value);
};
export const isEmptyOrNull = (value) => {
	return isNull(value) || isEmpty(value);
};

export const getFeildRenders = (
	renderField,
	formValues,
	handleChange,
	getErrorSlice,
	countryList,
	stateList,
	nameAppending,
	getRefById,
	triggerFormUpdate,
	fieldProps
) => {
	const getFeildProps = (name) => {
		return fieldProps[name] || {};
	};
	const getValue = (
		name,
		additionalProps,
		valueAsId?: boolean,
		defualtValue?: any
	) => {
		const loopIndexData =
			additionalProps?.oneToMenyKey && !additionalProps?.arrayLoop
				? get(formValues, additionalProps.oneToMenyKey, [])[
						additionalProps?.oneToManyLoopIndex
				  ]
				: (formValues as any);
		const value = get(loopIndexData, name, defualtValue);
		return valueAsId
			? value?.id
			: additionalProps?.arrayLoop
			? (value || [])[additionalProps?.oneToManyLoopIndex] || ''
			: value;
	};

	const getError = (name, additionalProps) => {
		const erroObj = getErrorSlice(
			name,
			additionalProps?.arrayLoop ? null : additionalProps?.oneToMenyKey || null,
			additionalProps?.oneToManyLoopIndex >= 0 && !additionalProps?.arrayLoop
				? additionalProps?.oneToManyLoopIndex
				: -1
		);
		if (erroObj && erroObj.message) {
			erroObj.ref = erroObj.ref || {
				name: getRefById(
					nameAppending + name + (additionalProps?.oneToManyLoopIndex || '')
				)?.current,
			};
		}
		return erroObj;
	};
	const handleFieldChange = (
		value: any,
		name: string,
		additionalProps: any,
		clearKeys: any,
		triggerFormUpdate?: boolean,
		triggerFormValueUpdate?: boolean
	) =>
		handleChange(
			value,
			name,
			clearKeys,
			additionalProps?.oneToMenyKey ? additionalProps.oneToManyLoopIndex : -1,
			additionalProps?.arrayLoop ? 0 : additionalProps?.oneToMenyKey,
			triggerFormUpdate,
			triggerFormValueUpdate
		);
	return {
		renderInput: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Input,
				{
					width: '100%',
					showLabel: true,
					onBlur: () =>
						triggerFormUpdate(
							!isEmpty(getError(name, additionalProps)),
							(additionalProps?.oneToMenyKey || '') +
								(isNil(additionalProps?.oneToManyLoopIndex)
									? ''
									: `.${additionalProps?.oneToManyLoopIndex}.`) +
								name
						),
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					label,
					ref: getRefById(
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || '')
					),
					value: getValue(name, additionalProps) || '',
					error: getError(name, additionalProps),
					showAsMandatory: true,
					onChange: (e: any) =>
						handleFieldChange(e.target.value, name, additionalProps, []),
					...getFeildProps('input'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),
		renderAddress: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Address,
				{
					width: '100%',
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					label,
					showAsMandatory: true,
					error: getError(name, additionalProps),
					onChange: (e: any) =>
						handleFieldChange(
							e,
							name,
							additionalProps,
							[],
							true,
							!isEmpty(getError(name, additionalProps))
						),
					value: getValue(name, additionalProps),
					countryList,
					stateList,
					...getFeildProps('address'),
					...(elementProps || {}),
				},
				false,
				{ ...additionalProps, doubleWrapperRequired: true }
			),
		renderCheckbox: (
			name: string,
			clearKeys: any[],
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Checkbox,
				{
					ref: null,
					id: `checkBox${name}`,
					checkBoxSize: CheckBoxSizes.Small,
					error: getError(name, additionalProps),
					label: '',
					checked: getValue(name, additionalProps, false, false),
					onChange: (e: any) =>
						handleFieldChange(
							e.target.checked,
							name,
							additionalProps,
							clearKeys,
							true,
							!isEmpty(getError(name, additionalProps))
						),
					type: CheckboxTypes.Square,
					icon: IconCodes.icon_Bd_Check,
					...getFeildProps('checkbox'),
					...(elementProps || {}),
				},
				true,
				additionalProps
			),
		renderCheckboxGroup: (
			name: string,
			label: string,
			options: any[],
			clearKeys: any[],
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				CheckboxGroup,
				{
					ref: null,
					id: `checkBoxGroup${name}`,
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					options,
					formStylesEnabled: true,
					error: getError(name, additionalProps),
					title: label,
					width: '',
					withGroupLabel: true,
					showSeparator: false,
					showAsterisk: false,
					isMulti: true,
					showAsMandatory: true,
					orientation: Orientation.Horizontal,
					defaultValue: getValue(name, additionalProps, false, false),
					onChange: (e: any) =>
						handleFieldChange(
							e,
							name,
							additionalProps,
							clearKeys,
							true,
							!isEmpty(getError(name, additionalProps))
						),
					type: CheckboxTypes.Square,
					icon: IconCodes.icon_Bd_Check,
					...getFeildProps('checkboxGroup'),
					...(elementProps || {}),
				},
				true,
				additionalProps
			),
		renderRadioGroup: (
			name: string,
			label: string,
			options: any[],
			clearKeys: any[],
			containerProps: any = {},
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Radiogroup,
				{
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					options,
					title: label,
					font: theme.fontSizes[DeviceTypes.Desktop].bSmall,
					containerProps,
					defaultValue: getValue(name, additionalProps),
					onChangeTrigger: (e: any) =>
						handleFieldChange(
							e.target.value,
							name,
							additionalProps,
							clearKeys,
							true,
							!isEmpty(getError(name, additionalProps))
						),
					orientation: Orientation.Horizontal,
					showSeparator: false,
					showAsMandatory: true,
					error: getError(name, additionalProps),
					...getFeildProps('radioGroup'),
					...(elementProps || {}),
				},
				true,
				additionalProps
			),
		renderDropdown: (
			name: string,
			label: string,
			data: any[],
			clearKeys: any[],
			valueAsId = false,
			searchable = false,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Dropdown,
				{
					data,
					width: '100%',
					showLabel: true,
					label,
					searchable,
					placeholder: 'Select',
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					showAsMandatory: true,
					selectedValues: getValue(name, additionalProps, valueAsId),
					onItemClick: (item: any) =>
						handleFieldChange(
							valueAsId ? { id: item } : item,
							name,
							additionalProps,
							clearKeys,
							true,
							!isEmpty(getError(name, additionalProps))
						),
					error: getError(name, additionalProps),
					loadVirtualList: false,
					auto: true,
					makeSelectionClearable: true,
					...getFeildProps('dropdown'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),
		renderFormattedInput: (
			name: string,
			label: string,
			mask: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				InputFormatComponent,
				{
					width: '100%',
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					label,
					showAsMandatory: true,
					showLabel: true,
					onBlur: () =>
						triggerFormUpdate(
							!isEmpty(getError(name, additionalProps)),
							(additionalProps?.oneToMenyKey || '') +
								(isNil(additionalProps?.oneToManyLoopIndex)
									? ''
									: `.${additionalProps?.oneToManyLoopIndex}.`) +
								name
						),
					mask,
					inputValue: getValue(name, additionalProps) || '',
					handleChange: (e: any) =>
						handleFieldChange(e.target.value, name, additionalProps, []),
					error: getError(name, additionalProps),
					inputProps: {
						width: '100%',
						showLabel: true,
						onBlur: () =>
							triggerFormUpdate(
								!isEmpty(getError(name, additionalProps)),
								(additionalProps?.oneToMenyKey || '') +
									(isNil(additionalProps?.oneToManyLoopIndex)
										? ''
										: `.${additionalProps?.oneToManyLoopIndex}.`) +
									name
							),
						label,
					},
					...getFeildProps('formattedInput'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),
		renderCurrency: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Currency,
				{
					width: '100%',
					inputTextAlignment: InputTextAlignment.LEFT,
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					value: getValue(name, additionalProps) || '',
					inputStyles: {
						width: '304px',
					},
					showAsMandatory: true,
					onBlur: () =>
						triggerFormUpdate(
							!isEmpty(getError(name, additionalProps)),
							(additionalProps?.oneToMenyKey || '') +
								(isNil(additionalProps?.oneToManyLoopIndex)
									? ''
									: `.${additionalProps?.oneToManyLoopIndex}.`) +
								name
						),
					onChangeTrigger: (e: any) =>
						handleFieldChange(e, name, additionalProps, []),
					showLabel: true,
					label,
					error: getError(name, additionalProps),
					showCurrencySymbol: true,
					...getFeildProps('currency'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),

		renderNumeric: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Numeric,
				{
					width: '100%',
					inputTextAlignment: InputTextAlignment.LEFT,
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					value: getValue(name, additionalProps) || '',
					inputStyles: {
						width: '304px',
					},
					showAsMandatory: true,
					onBlur: () =>
						triggerFormUpdate(
							!isEmpty(getError(name, additionalProps)),
							(additionalProps?.oneToMenyKey || '') +
								(isNil(additionalProps?.oneToManyLoopIndex)
									? ''
									: `.${additionalProps?.oneToManyLoopIndex}.`) +
								name
						),
					onChangeTrigger: (e: any) =>
						handleFieldChange(e, name, additionalProps, []),
					showLabel: true,
					label,
					error: getError(name, additionalProps),
					allowNegative: false,
					decimalScale: 5,
					...getFeildProps('numeric'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),
		renderPercentage: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Percentage,
				{
					width: '100%',
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					showAsMandatory: true,
					showLabel: true,
					onBlur: () =>
						triggerFormUpdate(
							!isEmpty(getError(name, additionalProps)),
							(additionalProps?.oneToMenyKey || '') +
								(isNil(additionalProps?.oneToManyLoopIndex)
									? ''
									: `.${additionalProps?.oneToManyLoopIndex}.`) +
								name
						),
					value: getValue(name, additionalProps) || '',
					onChange: (e: any) =>
						handleFieldChange(e.target.value, name, additionalProps, []),
					label,
					error: getError(name, additionalProps),
					...getFeildProps('percentage'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),
		renderDate: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Datepicker,
				{
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					label,
					width: '100%',
					value: getValue(name, additionalProps) || '',
					error: getError(name, additionalProps),
					initialLayout: 'YearWidget',
					dateFormat: DateFormat['MM/DD/YYYY'],
					onChange: (item: any) => {
						handleFieldChange(
							item.target.value,
							name,
							additionalProps,
							[],
							true,
							!isEmpty(getError(name, additionalProps))
						);
					},
					withLabel: true,
					showAsMandatory: true,
					...getFeildProps('date'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),
		renderPhoneNumber: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Phonenumber,
				{
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					showLabel: true,
					label,
					showAsMandatory: true,
					value: getValue(name, additionalProps) || '',
					error: getError(name, additionalProps),
					onBlur: () =>
						triggerFormUpdate(
							!isEmpty(getError(name, additionalProps)),
							(additionalProps?.oneToMenyKey || '') +
								(isNil(additionalProps?.oneToManyLoopIndex)
									? ''
									: `.${additionalProps?.oneToManyLoopIndex}.`) +
								name
						),
					onChange: (e: any) =>
						handleFieldChange(e.target.value, name, additionalProps, []),
					...getFeildProps('phoneNumber'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),
		renderEmail: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Email,
				{
					inputStyles: { width: '100%' },
					width: '100%',
					showLabel: true,
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					label,
					onBlur: () =>
						triggerFormUpdate(
							!isEmpty(getError(name, additionalProps)),
							(additionalProps?.oneToMenyKey || '') +
								(isNil(additionalProps?.oneToManyLoopIndex)
									? ''
									: `.${additionalProps?.oneToManyLoopIndex}.`) +
								name
						),
					value: getValue(name, additionalProps) || '',
					showAsMandatory: true,
					error: getError(name, additionalProps),
					onChange: (e: any) =>
						handleFieldChange(e.target.value, name, additionalProps, []),
					...getFeildProps('email'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),
		renderSsn: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				SSN,
				{
					inputStyles: { width: '100%' },
					width: '100%',
					showLabel: true,
					onBlur: () =>
						triggerFormUpdate(
							!isEmpty(getError(name, additionalProps)),
							(additionalProps?.oneToMenyKey || '') +
								(isNil(additionalProps?.oneToManyLoopIndex)
									? ''
									: `.${additionalProps?.oneToManyLoopIndex}.`) +
								name
						),
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					label,
					value: getValue(name, additionalProps) || '',
					showAsMandatory: true,
					error: getError(name, additionalProps),
					onChange: (e: any) =>
						handleFieldChange(e.target.value, name, additionalProps, []),
					...getFeildProps('ssn'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: 304, maxWidth: 304, ...additionalProps }
			),
		renderToggle: (
			name: string,
			label: string,
			elementProps: any,
			additionalProps: any
		) =>
			renderField(
				Toggle,
				{
					name:
						nameAppending + name + (additionalProps?.oneToManyLoopIndex || ''),
					label,
					showLabel: true,
					showAsMandatory: true,
					defaultChecked: getValue(name, additionalProps),
					inputOrientation: 'Horizontal',
					toggleType: type.Toggle,
					paddingBottom: '10px',
					error: getError(name, additionalProps),
					onChange: (e: any) => handleFieldChange(e, name, additionalProps, []),
					...getFeildProps('toggle'),
					...(elementProps || {}),
				},
				false,
				{ minWidth: '100%', maxWidth: '100%', ...additionalProps }
			),
	};
};

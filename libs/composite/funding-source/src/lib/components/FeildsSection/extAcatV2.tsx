import {
	createRef,
	FC,
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import Box from '@atomic/box';
import { IFeildSectionTypes } from './types';
import { styles } from './style';
import classNames from 'classnames';
import { get, isEmpty, isObject, isString, set } from 'lodash';
import { getFeildRenders, isNotEmptyAndNull } from './helper';

import { v4 as uuid } from 'uuid';
import {
	transferOptionsValueOfTransferInKindOptions,
	transferAllOptionsValueOfShareAmount,
	transferOptionsValueOfLiquidateOptions,
	bankTransferTypeOptionsValueOfLiquidateCDAtMaturityAndTransferCash,
	otherAccountType,
	externalTransferTopLevelBankIRACDSavingsTransfer,
	externalTransferTopLevelBrokerageTrustCompanyTransfer,
	externalTransferTopLevelMutualFundCompanyTransfer,
	externalTransferTopLevelAnnuityCompanyTransfer,
	externalTransferTopLevelInsuranceTransfer1035Exchange,
	externalTransferTopLevelTransferAgentTransfer,
	externalTransferTopLevelEmployerSponsoredPlanRollover,
	brokerageOrTrustCompanyOptionsPartialAccountTransfer,
	annuityTransferOptionsValueOfPartialLiquidation,
	employerSponsordPlanRolloverValueOfDirectRolloverInitiationRequest,
	employerSponsordPlanRolloverValueOfDirectRolloverDisclosureAcknowledgement,
	RolloverInstructionsValueOfLiquidateAndRolloverOfMyAssets,
	RolloverInstructionsValueOfLiquidateAndRolloverPercentageOfMyAssets,
} from '../../constants';

import RenderFeildsLoop from './renderFeildsLoop';

const accordionIds = [
	{ id: '0', key: 'receivingFirmInformation' },
	{ id: '1', key: 'deliveringAccountInformation' },
	{ id: '2', key: 'transferInstructions' },
	{ id: '3', key: 'partialAccountTransfer' },
];
const ExtAcat: FC<IFeildSectionTypes> = (props) => {
	const componentStyles = styles(props);
	const {
		onChangeFeild,
		formKeys,
		formValues = {},
		error = {},

		countryList = [],
		registrationTypes = [],
		stateList = [],
		nameAppending,
		sourceMetaData,
		enums,
		triggerFormUpdate,
		registrationType,
	} = props;
	const fieldProps = useMemo(
		() => sourceMetaData?.fieldProps || {},
		[sourceMetaData]
	);
	const feildsMetaData = useMemo(
		() => sourceMetaData?.fieldConfigs || {},
		[sourceMetaData]
	);
	const accordianMetaData = useMemo(
		() => sourceMetaData?.accordianConfigs || {},
		[sourceMetaData]
	);
	const refsById = useRef({});

	const getRefById = (id) => {
		if (!refsById.current[id]) {
			refsById.current[id] = createRef();
		}
		return refsById.current[id];
	};
	const getEnumValue = (itm) => {
		return enums[feildsMetaData[itm.id]?.enum || itm.enum];
	};

	const [expandedItem, setExpandedItem] = useState(accordionIds[1]?.id);
	const curLoopRowValue = get(
		formValues,
		feildsMetaData[formKeys.loopingRowKey.id]?.key ||
			formKeys.loopingRowKey.key,
		[{ key: uuid() }]
	);
	const curLoopRowData =
		Array.isArray(curLoopRowValue) && curLoopRowValue.length === 0
			? [{ key: uuid() }]
			: curLoopRowValue;

	const handleChange = useCallback(
		(
			value: any,
			key: any,
			clearKeys?: any[],
			loopIndex?: number,
			loopKey?: string,
			triggerFormUpdate?: boolean,
			triggerFormValueUpdate?: boolean
		) => {
			let curData = value;
			let curKey = key;
			if (loopIndex >= 0) {
				curData = get(formValues, loopKey, [{ key: uuid() }]);
				curData[loopIndex] = set(
					curData[loopIndex] || { key: uuid() },
					key,
					value
				);
				curKey = loopKey;
			}
			onChangeFeild(
				curData,
				curKey,
				clearKeys,
				triggerFormUpdate,
				triggerFormValueUpdate
			);
		},
		[onChangeFeild, formValues]
	);
	const toogleAccordion = (id) => {
		setExpandedItem(expandedItem === id ? '' : id);
	};
	const getErrorSlice = useCallback(
		(key, oneToMenyKey, oneToManyLoopIndex) => {
			const errorMessage =
				oneToMenyKey && oneToManyLoopIndex >= 0
					? get(error, `${oneToMenyKey}.${oneToManyLoopIndex}.${key}`)
					: get(error, key);

			return !isEmpty(errorMessage)
				? isString(errorMessage)
					? { message: errorMessage }
					: isObject(errorMessage) &&
					  (errorMessage as any)?.message &&
					  errorMessage
				: null;
		},
		[error]
	);
	const onAddRow = useCallback(
		(loopData, loopKey) => {
			const curValues = [...loopData];
			curValues.push({ key: uuid() });
			handleChange(curValues, loopKey);
		},
		[handleChange]
	);
	const onDeleteRow = useCallback(
		(index, loopData, loopKey) => {
			const curValues = [...loopData];
			curValues.splice(index, 1);
			handleChange(curValues, loopKey, [], -1, '', true, true);
		},
		[handleChange]
	);

	const getFormKeysArray = useCallback(
		(ids: Array<any>) => {
			return ids.map((itm: any) => feildsMetaData[itm.id]?.key || itm.key);
		},
		[feildsMetaData]
	);
	const getFormKey = useCallback(
		(idKey: any) => {
			return feildsMetaData[idKey.id]?.key || idKey.key;
		},
		[feildsMetaData]
	);

	const renderField = useCallback(
		(
			Component: React.ElementType,
			props: Record<string, any>,
			noWrapperRequired: boolean,
			additionalProps: any = {}
		) => {
			const { minWidth, maxWidth, doubleWrapperRequired } =
				additionalProps || {};

			return noWrapperRequired ? (
				<Component {...props} />
			) : doubleWrapperRequired ? (
				<Box display="block" width="100%">
					<Component {...props} />
				</Box>
			) : (
				<Box
					className={classNames(componentStyles.fieldInputWrapper)}
					minWidth={minWidth}
					maxWidth={maxWidth}
				>
					<Component {...props} />
				</Box>
			);
		},
		[componentStyles.fieldInputWrapper]
	);
	const renderFeildComponents: any = useMemo(() => {
		return getFeildRenders(
			renderField,
			formValues,
			handleChange,
			getErrorSlice,
			countryList,
			stateList,
			nameAppending,
			getRefById,
			triggerFormUpdate,
			fieldProps
		);
	}, [
		renderField,
		formValues,
		fieldProps,
		handleChange,
		getErrorSlice,
		countryList,
		stateList,
		nameAppending,
		triggerFormUpdate,
	]);

	const selectedTransferType = useMemo(() => {
		const sourceTypeValue = (sourceMetaData?.topLevel || [])[0]?.key;
		return get(formValues, sourceTypeValue || '');
	}, [formValues, sourceMetaData]);
	useEffect(() => {
		const usedForAccountFundingValue = get(
			formValues,
			getFormKey(formKeys.usedForAccountFunding)
		);
		if (
			feildsMetaData[getFormKey(formKeys.usedForAccountFunding)]?.on !==
				false &&
			usedForAccountFundingValue !== false &&
			!usedForAccountFundingValue
		) {
			handleChange(
				true,
				getFormKey(formKeys.usedForAccountFunding),
				[],
				-1,
				'',
				true
			);
		}
	}, []);
	const loopDataMap = {
		[externalTransferTopLevelBankIRACDSavingsTransfer]: [
			{
				accordianRequired: false,
				accordinaData: {},
				feildsData: [
					{
						item: formKeys.usedForAccountFunding,
						renderType: 'renderDropdown',
						text: 'Used to fund product',
						feildProps: [
							getEnumValue(formKeys.usedForAccountFunding),
							[getFormKey(formKeys.usedForAccountFundingAmount)],
						],
					},
					{
						text: 'Amount',
						renderType: 'renderCurrency',
						item: formKeys.usedForAccountFundingAmount,
						conditional:
							get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
							true,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[0]?.key,
					isExpanded: expandedItem === accordionIds[0]?.id,
					heading: 'Receiving Firm Information',
					onIconClick: () => toogleAccordion(accordionIds[0]?.id),
				},
				feildsData: [
					{
						isStatic: true,
						data: sourceMetaData?.firmInformation,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[1]?.key,
					isExpanded: expandedItem === accordionIds[1]?.id,
					heading: 'Delivering Account Information',
					onIconClick: () => toogleAccordion(accordionIds[1]?.id),
				},
				feildsData: [
					{
						text: 'DTC number',
						renderType: 'renderInput',
						item: formKeys.clearingNumber,
						feildProps: [
							{
								maxCharLength: 4,
								showAsMandatory: false,
							},
						],
					},
					{
						text: 'Account number',
						renderType: 'renderFormattedInput',
						feildProps: ['*************************'],
						item: formKeys.accountNumber,
					},
					{
						text: 'Account Owner/Trust/Entity Name',
						renderType: 'renderInput',
						item: formKeys.accountTitle,
					},
					{
						text: 'Account type',
						renderType: 'renderDropdown',
						item: formKeys.accountType,
						feildProps: [
							getEnumValue(formKeys.accountType),
							getFormKeysArray([formKeys.otherAccountType]),
						],
					},
					{
						text: 'Other account type',
						renderType: 'renderInput',
						item: formKeys.otherAccountType,
						conditional:
							get(formValues, getFormKey(formKeys.accountType)) ===
							otherAccountType,
					},
					{
						text: 'Firm name',
						renderType: 'renderInput',
						item: formKeys.firmName,
					},
					{
						text: 'Firm phone number',
						renderType: 'renderPhoneNumber',
						item: formKeys.firmPhoneNumber,
					},
					{
						text: 'Firm address',
						renderType: 'renderAddress',
						item: formKeys.firmAddress,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[2]?.key,
					isExpanded: expandedItem === accordionIds[2]?.id,
					heading: 'Transfer Instructions',
					onIconClick: () => toogleAccordion(accordionIds[2]?.id),
				},
				feildsData: [
					{
						text: 'Bank transfer type',
						renderType: 'renderDropdown',
						item: formKeys.bankAccountTransferType,
						feildProps: [
							getEnumValue(formKeys.bankAccountTransferType),
							getFormKeysArray([formKeys.cdMaturityDate]),
						],
					},
					{
						text: 'Maturity date',
						renderType: 'renderDate',
						item: formKeys.cdMaturityDate,
						conditional:
							get(formValues, getFormKey(formKeys.bankAccountTransferType)) ===
							bankTransferTypeOptionsValueOfLiquidateCDAtMaturityAndTransferCash,
					},
				],
			},
		],
		[externalTransferTopLevelBrokerageTrustCompanyTransfer]: [
			{
				accordianRequired: false,
				accordinaData: {},
				feildsData: [
					{
						item: formKeys.usedForAccountFunding,
						renderType: 'renderDropdown',
						text: 'Used to fund product',
						feildProps: [
							getEnumValue(formKeys.usedForAccountFunding),
							[getFormKey(formKeys.usedForAccountFundingAmount)],
						],
					},
					{
						text: 'Amount',
						renderType: 'renderCurrency',
						item: formKeys.usedForAccountFundingAmount,
						conditional:
							get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
							true,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[0]?.key,
					isExpanded: expandedItem === accordionIds[0]?.id,
					heading: 'Receiving Firm Information',
					onIconClick: () => toogleAccordion(accordionIds[0]?.id),
				},
				feildsData: [
					{
						isStatic: true,
						data: sourceMetaData?.firmInformation,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[1]?.key,
					isExpanded: expandedItem === accordionIds[1]?.id,
					heading: 'Delivering Account Information',
					onIconClick: () => toogleAccordion(accordionIds[1]?.id),
				},
				feildsData: [
					{
						text: 'DTC number',
						renderType: 'renderInput',
						item: formKeys.clearingNumber,
						feildProps: [
							{
								maxCharLength: 4,
								showAsMandatory: false,
							},
						],
					},
					{
						text: 'Account number',
						renderType: 'renderFormattedInput',
						feildProps: ['*************************'],
						item: formKeys.accountNumber,
					},
					{
						text: 'Account Owner/Trust/Entity Name',
						renderType: 'renderInput',
						item: formKeys.accountTitle,
					},
					{
						text: 'Account type',
						renderType: 'renderDropdown',
						item: formKeys.accountType,
						feildProps: [
							getEnumValue(formKeys.accountType),
							getFormKeysArray([formKeys.otherAccountType]),
						],
					},
					{
						text: 'Other account type',
						renderType: 'renderInput',
						item: formKeys.otherAccountType,
						conditional:
							get(formValues, getFormKey(formKeys.accountType)) ===
							otherAccountType,
					},
					{
						text: 'Firm name',
						renderType: 'renderInput',
						item: formKeys.firmName,
					},
					{
						text: 'Firm phone number',
						renderType: 'renderPhoneNumber',
						item: formKeys.firmPhoneNumber,
					},
					{
						text: 'Firm address',
						renderType: 'renderAddress',
						item: formKeys.firmAddress,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[2]?.key,
					isExpanded: expandedItem === accordionIds[2]?.id,
					heading: 'Transfer Instructions',
					onIconClick: () => toogleAccordion(accordionIds[2]?.id),
				},
				feildsData: [
					{
						text: 'Brokerage/Trust Company',
						renderType: 'renderDropdown',
						item: formKeys.brokerageOrTrustCompany,
						feildProps: [
							getEnumValue(formKeys.brokerageOrTrustCompany),
							getFormKeysArray([formKeys.loopingRowKey]),
						],
					},
					{
						isLoop: true,
						loopItem: formKeys.loopingRowKey,
						data: curLoopRowData,
						conditional:
							get(formValues, getFormKey(formKeys.brokerageOrTrustCompany)) ===
							brokerageOrTrustCompanyOptionsPartialAccountTransfer,
						feilds: [
							{
								text: 'Asset description',
								renderType: 'renderInput',
								item: formKeys.description,
								feildProps: (loopIndex: number) => [{}],
							},
							{
								text: 'Symbol',
								renderType: 'renderInput',
								item: formKeys.symbol,
								feildProps: (loopIndex: number) => [
									{
										allowedCharacters: 'text_and_number',
										textTransform: 'uppercase',
										maxCharLength: 9,
									},
								],
							},
							{
								text: 'Quantity',
								renderType: 'renderInput',
								item: formKeys.quantity,
								feildProps: (loopIndex: number) => [
									{
										allowedCharacters: 'number_only',
									},
								],
							},
						],
					},
				],
			},
		],
		[externalTransferTopLevelMutualFundCompanyTransfer]: [
			{
				accordianRequired: false,
				accordinaData: {},
				feildsData: [
					{
						item: formKeys.usedForAccountFunding,
						renderType: 'renderDropdown',
						text: 'Used to fund product',
						feildProps: [
							getEnumValue(formKeys.usedForAccountFunding),
							[getFormKey(formKeys.usedForAccountFundingAmount)],
						],
					},
					{
						text: 'Amount',
						renderType: 'renderCurrency',
						item: formKeys.usedForAccountFundingAmount,
						conditional:
							get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
							true,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[0]?.key,
					isExpanded: expandedItem === accordionIds[0]?.id,
					heading: 'Receiving Firm Information',
					onIconClick: () => toogleAccordion(accordionIds[0]?.id),
				},
				feildsData: [
					{
						isStatic: true,
						data: sourceMetaData?.firmInformation,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[1]?.key,
					isExpanded: expandedItem === accordionIds[1]?.id,
					heading: 'Delivering Account Information',
					onIconClick: () => toogleAccordion(accordionIds[1]?.id),
				},
				feildsData: [
					{
						text: 'DTC number',
						renderType: 'renderInput',
						item: formKeys.clearingNumber,
						feildProps: [
							{
								maxCharLength: 4,
								showAsMandatory: false,
							},
						],
					},
					{
						text: 'Account number',
						renderType: 'renderFormattedInput',
						feildProps: ['*************************'],
						item: formKeys.accountNumber,
					},
					{
						text: 'Account Owner/Trust/Entity Name',
						renderType: 'renderInput',
						item: formKeys.accountTitle,
					},
					{
						text: 'Account type',
						renderType: 'renderDropdown',
						item: formKeys.accountType,
						feildProps: [
							getEnumValue(formKeys.accountType),
							getFormKeysArray([formKeys.otherAccountType]),
						],
					},
					{
						text: 'Other account type',
						renderType: 'renderInput',
						item: formKeys.otherAccountType,
						conditional:
							get(formValues, getFormKey(formKeys.accountType)) ===
							otherAccountType,
					},
					{
						text: 'Firm name',
						renderType: 'renderInput',
						item: formKeys.firmName,
					},
					{
						text: 'Firm phone number',
						renderType: 'renderPhoneNumber',
						item: formKeys.firmPhoneNumber,
					},
					{
						text: 'Firm address',
						renderType: 'renderAddress',
						item: formKeys.firmAddress,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[2]?.key,
					isExpanded: expandedItem === accordionIds[2]?.id,
					heading: 'Transfer Instructions',
					onIconClick: () => toogleAccordion(accordionIds[2]?.id),
				},
				feildsData: [
					{
						isLoop: true,
						loopItem: formKeys.loopingRowKey,
						data: curLoopRowData,
						feilds: [
							{
								text: 'Fund name',
								renderType: 'renderInput',
								item: formKeys.description,
								feildProps: (loopIndex: number) => [{}],
							},
							{
								text: 'Symbol',
								renderType: 'renderInput',
								item: formKeys.symbol,
								feildProps: (loopIndex: number) => [
									{
										allowedCharacters: 'text_and_number',
										textTransform: 'uppercase',
										maxCharLength: 9,
									},
								],
							},
							{
								text: 'Fund account number',
								renderType: 'renderFormattedInput',
								item: formKeys.fundAccountNumber,
								feildProps: (loopIndex: number) => [
									'*************************',
									{},
								],
							},
							{
								text: 'Transfer option',
								renderType: 'renderDropdown',
								item: formKeys.mutualFundTransferOption,
								feildProps: (loopIndex: number) => [
									getEnumValue(formKeys.mutualFundTransferOption),
									[
										{
											loopKey: getFormKey(formKeys.loopingRowKey),
											loopIndex: loopIndex,
											key: getFormKey(formKeys.transferAll),
										},
									],
									false,
									false,
									{},
								],
							},
							{
								text: 'Transfer in kind options',
								renderType: 'renderDropdown',
								item: formKeys.transferAll,
								feildProps: (loopIndex: number) => [
									getEnumValue(formKeys.transferAll),
									[
										{
											loopKey: getFormKey(formKeys.loopingRowKey),
											loopIndex: loopIndex,
											key: getFormKey(formKeys.quantity),
										},
									],
									false,
									false,
									{},
								],
								conditional: (loopIndex: number) =>
									get(
										formValues,
										`${getFormKey(
											formKeys.loopingRowKey
										)}[${loopIndex}].${getFormKey(
											formKeys.mutualFundTransferOption
										)}`
									) === transferOptionsValueOfTransferInKindOptions,
							},
							{
								text: 'Liquidate options',
								renderType: 'renderDropdown',
								item: formKeys.transferAll,
								conditional: (loopIndex: number) =>
									get(
										formValues,
										`${getFormKey(
											formKeys.loopingRowKey
										)}[${loopIndex}].${getFormKey(
											formKeys.mutualFundTransferOption
										)}`
									) === transferOptionsValueOfLiquidateOptions,
								feildProps: (loopIndex: number) => [
									getEnumValue(formKeys.transferAll),
									[
										{
											loopKey: getFormKey(formKeys.loopingRowKey),
											loopIndex: loopIndex,
											key: getFormKey(formKeys.quantity),
										},
									],
									false,
									false,
									{},
								],
							},
							{
								text: 'Number of shares',
								renderType: 'renderInput',
								item: formKeys.quantity,
								feildProps: (loopIndex: number) => [
									{
										decimalScale: 5,
										allowNegative: false,
									},
								],
								conditional: (loopIndex: number) =>
									get(
										formValues,
										`${getFormKey(
											formKeys.loopingRowKey
										)}[${loopIndex}].${getFormKey(formKeys.transferAll)}`
									) === transferAllOptionsValueOfShareAmount,
							},
						],
					},
				],
			},
		],
		[externalTransferTopLevelAnnuityCompanyTransfer]: [
			{
				accordianRequired: false,
				accordinaData: {},
				feildsData: [
					{
						item: formKeys.usedForAccountFunding,
						renderType: 'renderDropdown',
						text: 'Used to fund product',
						feildProps: [
							getEnumValue(formKeys.usedForAccountFunding),
							[getFormKey(formKeys.usedForAccountFundingAmount)],
						],
					},
					{
						text: 'Amount',
						renderType: 'renderCurrency',
						item: formKeys.usedForAccountFundingAmount,
						conditional:
							get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
							true,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[0]?.key,
					isExpanded: expandedItem === accordionIds[0]?.id,
					heading: 'Receiving Firm Information',
					onIconClick: () => toogleAccordion(accordionIds[0]?.id),
				},
				feildsData: [
					{
						isStatic: true,
						data: sourceMetaData?.firmInformation,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[1]?.key,
					isExpanded: expandedItem === accordionIds[1]?.id,
					heading: 'Delivering Account Information',
					onIconClick: () => toogleAccordion(accordionIds[1]?.id),
				},
				feildsData: [
					{
						text: 'DTC number',
						renderType: 'renderInput',
						item: formKeys.clearingNumber,
						feildProps: [
							{
								maxCharLength: 4,
								showAsMandatory: false,
							},
						],
					},
					{
						text: 'Account number',
						renderType: 'renderFormattedInput',
						feildProps: ['*************************'],
						item: formKeys.accountNumber,
					},
					{
						text: 'Account Owner/Trust/Entity Name',
						renderType: 'renderInput',
						item: formKeys.accountTitle,
					},
					{
						text: 'Account type',
						renderType: 'renderDropdown',
						item: formKeys.accountType,
						feildProps: [
							getEnumValue(formKeys.accountType),
							getFormKeysArray([formKeys.otherAccountType]),
						],
					},
					{
						text: 'Other account type',
						renderType: 'renderInput',
						item: formKeys.otherAccountType,
						conditional:
							get(formValues, getFormKey(formKeys.accountType)) ===
							otherAccountType,
					},
					{
						text: 'Firm name',
						renderType: 'renderInput',
						item: formKeys.firmName,
					},
					{
						text: 'Firm phone number',
						renderType: 'renderPhoneNumber',
						item: formKeys.firmPhoneNumber,
					},
					{
						text: 'Firm address',
						renderType: 'renderAddress',
						item: formKeys.firmAddress,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[2]?.key,
					isExpanded: expandedItem === accordionIds[2]?.id,
					heading: 'Transfer Instructions',
					onIconClick: () => toogleAccordion(accordionIds[2]?.id),
				},
				feildsData: [
					{
						text: 'Annuity transfer',
						renderType: 'renderDropdown',
						item: formKeys.annuityTransfer,
						feildProps: [
							getEnumValue(formKeys.annuityTransfer),
							getFormKeysArray([formKeys.cdMaturityDate]),
						],
					},
					{
						text: 'Partial liquidation',
						renderType: 'renderCurrency',
						item: formKeys.partialLiquidation,
						conditional:
							get(formValues, getFormKey(formKeys.annuityTransfer)) ===
							annuityTransferOptionsValueOfPartialLiquidation,
					},
				],
			},
		],
		[externalTransferTopLevelInsuranceTransfer1035Exchange]: [
			{
				accordianRequired: false,
				accordinaData: {},
				feildsData: [
					{
						item: formKeys.usedForAccountFunding,
						renderType: 'renderDropdown',
						text: 'Used to fund product',
						feildProps: [
							getEnumValue(formKeys.usedForAccountFunding),
							[getFormKey(formKeys.usedForAccountFundingAmount)],
						],
					},
					{
						text: 'Amount',
						renderType: 'renderCurrency',
						item: formKeys.usedForAccountFundingAmount,
						conditional:
							get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
							true,
					},
				],
			},
		],
		[externalTransferTopLevelTransferAgentTransfer]: [
			{
				accordianRequired: false,
				accordinaData: {},
				feildsData: [
					{
						item: formKeys.usedForAccountFunding,
						renderType: 'renderDropdown',
						text: 'Used to fund product',
						feildProps: [
							getEnumValue(formKeys.usedForAccountFunding),
							[getFormKey(formKeys.usedForAccountFundingAmount)],
						],
					},
					{
						text: 'Amount',
						renderType: 'renderCurrency',
						item: formKeys.usedForAccountFundingAmount,
						conditional:
							get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
							true,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[0]?.key,
					isExpanded: expandedItem === accordionIds[0]?.id,
					heading: 'Receiving Firm Information',
					onIconClick: () => toogleAccordion(accordionIds[0]?.id),
				},
				feildsData: [
					{
						isStatic: true,
						data: sourceMetaData?.firmInformation,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[1]?.key,
					isExpanded: expandedItem === accordionIds[1]?.id,
					heading: 'Delivering Account Information',
					onIconClick: () => toogleAccordion(accordionIds[1]?.id),
				},
				feildsData: [
					{
						text: 'DTC number',
						renderType: 'renderInput',
						item: formKeys.clearingNumber,
						feildProps: [
							{
								maxCharLength: 4,
								showAsMandatory: false,
							},
						],
					},
					{
						text: 'Account number',
						renderType: 'renderFormattedInput',
						feildProps: ['*************************'],
						item: formKeys.accountNumber,
					},
					{
						text: 'Account Owner/Trust/Entity Name',
						renderType: 'renderInput',
						item: formKeys.accountTitle,
					},
					{
						text: 'Account type',
						renderType: 'renderDropdown',
						item: formKeys.accountType,
						feildProps: [
							getEnumValue(formKeys.accountType),
							getFormKeysArray([formKeys.otherAccountType]),
						],
					},
					{
						text: 'Other account type',
						renderType: 'renderInput',
						item: formKeys.otherAccountType,
						conditional:
							get(formValues, getFormKey(formKeys.accountType)) ===
							otherAccountType,
					},
					{
						text: 'Firm name',
						renderType: 'renderInput',
						item: formKeys.firmName,
					},
					{
						text: 'Firm phone number',
						renderType: 'renderPhoneNumber',
						item: formKeys.firmPhoneNumber,
					},
					{
						text: 'Firm address',
						renderType: 'renderAddress',
						item: formKeys.firmAddress,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[2]?.key,
					isExpanded: expandedItem === accordionIds[2]?.id,
					heading: 'Transfer Instructions',
					onIconClick: () => toogleAccordion(accordionIds[2]?.id),
				},
				feildsData: [
					{
						text: 'Transfer agent transfer',
						renderType: 'renderDropdown',
						item: formKeys.transferAgentTransfer,
						feildProps: [getEnumValue(formKeys.transferAgentTransfer)],
					},
					{
						isLoop: true,
						loopItem: formKeys.loopingRowKey,
						data: curLoopRowData,
						feilds: [
							{
								text: 'Asset description',
								renderType: 'renderInput',
								item: formKeys.description,
								feildProps: (loopIndex: number) => [{}],
							},
							{
								text: 'Symbol',
								renderType: 'renderInput',
								item: formKeys.symbol,
								feildProps: (loopIndex: number) => [
									{
										allowedCharacters: 'text_and_number',
										textTransform: 'uppercase',
										maxCharLength: 9,
									},
								],
							},
							{
								text: 'Number of shares',
								renderType: 'renderInput',
								item: formKeys.quantity,
								feildProps: (loopIndex: number) => [
									{
										allowDecimalPlaces: false,
										allowNegative: false,
									},
								],
							},
						],
					},
				],
			},
		],
		[externalTransferTopLevelEmployerSponsoredPlanRollover]: [
			{
				accordianRequired: false,
				accordinaData: {},
				feildsData: [
					{
						item: formKeys.usedForAccountFunding,
						renderType: 'renderDropdown',
						text: 'Used to fund product',
						feildProps: [
							getEnumValue(formKeys.usedForAccountFunding),
							[getFormKey(formKeys.usedForAccountFundingAmount)],
						],
					},
					{
						text: 'Amount',
						renderType: 'renderCurrency',
						item: formKeys.usedForAccountFundingAmount,
						conditional:
							get(formValues, getFormKey(formKeys.usedForAccountFunding)) ===
							true,
					},
				],
			},
			{
				accordianRequired: true,
				accordinaData: {
					key: accordionIds[1]?.key,
					isExpanded: expandedItem === accordionIds[1]?.id,
					heading: 'Existing Plan Administrator /Employer Information',
					onIconClick: () => toogleAccordion(accordionIds[1]?.id),
				},
				feildsData: [
					{
						item: formKeys.ibdToInitiateTransfer,
						renderType: 'renderDropdown',
						text: 'Plan administrator require Citizens initiate this rollover?',
						feildProps: [
							getEnumValue(formKeys.ibdToInitiateTransfer),
							getFormKeysArray([
								formKeys.rolloverDetails,
								formKeys.rolloverQuantityChoice,
								formKeys.partialTransferPercent,
								formKeys.partialTransferAmount,
							]),
							false,
							false,
							{},
							{ minWidth: 619, maxWidth: 619 },
						],
					},
					{
						text: 'Name of firm holding assets',
						renderType: 'renderInput',
						item: formKeys.firmName,
					},
					{
						text: 'Account number with current plan administrator',
						renderType: 'renderInput',
						item: formKeys.accountNumber,
						feildProps: [
							{ showAsMandatory: false },
							{
								minWidth: 619,
								maxWidth: 619,
							},
						],
					},
					{
						text: 'Address',
						renderType: 'renderAddress',
						item: formKeys.firmAddress,
						feildProps: [{ showAsMandatory: false }],
					},
					{
						text: 'Phone number for current plan administrator',
						renderType: 'renderPhoneNumber',
						item: formKeys.firmPhoneNumber,
						feildProps: [
							{ showAsMandatory: false },
							{
								minWidth: 619,
								maxWidth: 619,
							},
						],
					},
					{
						text: 'Rollover Details',
						renderType: 'renderDropdown',
						item: formKeys.rolloverDetails,
						feildProps: [getEnumValue(formKeys.rolloverDetails)],
						conditional:
							get(formValues, getFormKey(formKeys.ibdToInitiateTransfer)) ===
							employerSponsordPlanRolloverValueOfDirectRolloverDisclosureAcknowledgement,
					},
					{
						text: 'Rollover instructions',
						renderType: 'renderDropdown',
						item: formKeys.rolloverQuantityChoice,
						feildProps: [
							getEnumValue(formKeys.rolloverQuantityChoice),
							getFormKeysArray([
								formKeys.partialTransferPercent,
								formKeys.partialTransferAmount,
							]),
						],
						conditional:
							get(formValues, getFormKey(formKeys.ibdToInitiateTransfer)) ===
							employerSponsordPlanRolloverValueOfDirectRolloverInitiationRequest,
					},
					{
						text: 'Amount ($)',
						renderType: 'renderCurrency',
						item: formKeys.partialTransferAmount,
						conditional:
							get(formValues, getFormKey(formKeys.ibdToInitiateTransfer)) ===
								employerSponsordPlanRolloverValueOfDirectRolloverInitiationRequest &&
							get(formValues, getFormKey(formKeys.rolloverQuantityChoice)) ===
								RolloverInstructionsValueOfLiquidateAndRolloverOfMyAssets,
					},
					{
						text: '% of my assets',
						renderType: 'renderPercentage',
						item: formKeys.partialTransferPercent,
						conditional:
							get(formValues, getFormKey(formKeys.ibdToInitiateTransfer)) ===
								employerSponsordPlanRolloverValueOfDirectRolloverInitiationRequest &&
							get(formValues, getFormKey(formKeys.rolloverQuantityChoice)) ===
								RolloverInstructionsValueOfLiquidateAndRolloverPercentageOfMyAssets,
					},
				],
			},
		],
	};

	return (
		<RenderFeildsLoop
			loopData={loopDataMap[selectedTransferType]}
			renderFeildComponents={renderFeildComponents}
			feildsMetaData={feildsMetaData}
			accordianMetaData={accordianMetaData}
			onAddRow={onAddRow}
			onDeleteRow={onDeleteRow}
			getErrorSlice={getErrorSlice}
			registrationType={registrationType}
			registrationTypes={registrationTypes}
		/>
	);
};
export default ExtAcat;

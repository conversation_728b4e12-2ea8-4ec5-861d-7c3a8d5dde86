import { IconCodes } from '@base/icon';
import { jiffy as theme } from '@base/theme';
import ACHImage from './assets/ach.png';
import CheckImage from './assets/check.png';
import ExternalACATImage from './assets/externalACAT.png';
import ExternalNONACATImage from './assets/extNonACTAT.png';
import InternalTransferImage from './assets/intTransfer.png';
import WireTransferImage from './assets/wireTransfer.png';
import ACHImage2 from './assets/ach2.png';
import CheckImage2 from './assets/check2.png';
import ExternalACATImage2 from './assets/externalACAT2.png';
import ExternalNONACATImage2 from './assets/extNonACTAT2.png';
import InternalTransferImage2 from './assets/intTransfer2.png';
import WireTransferImage2 from './assets/wireTransfer2.png';
import { formatCurrency } from './helpers';

export const sourceToColor = {
	ach: theme.colors.orientBlue.darkmode,
	check: theme.colors.secondary.dark,
	wire_transfer: theme.colors.primary.dark,
	'external_transfer_-_acat': theme.colors.orange[700],
	'external_transfer_-_non-acat': '#21767B', //not in theme
	internal_transfer: theme.colors.lavender[700],
};

export const sourceToIcon = {
	ach: ACHImage,
	check: CheckImage,
	wire_transfer: WireTransferImage,
	'external_transfer_-_acat': ExternalACATImage,
	'external_transfer_-_non-acat': ExternalNONACATImage,
	internal_transfer: InternalTransferImage,
};
export const sourceToIcon2 = {
	ach: ACHImage2,
	check: CheckImage2,
	wire_transfer: WireTransferImage2,
	'external_transfer_-_acat': ExternalACATImage2,
	'external_transfer_-_non-acat': ExternalNONACATImage2,
	internal_transfer: InternalTransferImage2,
};

export const sourceToLabel = {
	ach: 'ACH',
	check: 'Check',
	wire_transfer: 'Wire Transfer',
	'external_transfer_-_acat': 'External Transfer - ACAT',
	'external_transfer_-_non-acat': 'External Transfer - Non-ACAT',
	internal_transfer: 'Internal Transfer',
};
export const labelToSource = {
	ACH: 'ach',
	Check: 'check',
	'Wire Transfer': 'wire_transfer',
	'External Transfer - ACAT': 'external_transfer_-_acat',
	'External Transfer - Non-ACAT': 'external_transfer_-_non-acat',
	'Internal Transfer': 'internal_transfer',
};
export const labelToShowText = {
	ACH: 'ACH',
	Check: 'Check',
	'Wire Transfer': 'Wire Transfer',
	'External Transfer - ACAT': 'ACAT',
	'External Transfer - Non-ACAT': 'Non-ACAT',
	'Internal Transfer': 'Internal Transfer',
};

export const acatAndNonAcatStaticValues = [
	{
		name: 'Firm Name',
		value: 'Axos Clearing, LLC',
		icon: IconCodes.icon_Tb_building_bank,
	},
	{
		name: 'Clearing Number',
		value: 'DTCC - 0052   OCC - 0052',
		icon: IconCodes.icon_Tb_input_check,
	},
	{
		name: 'Phone Number',
		value: '(*************',
		icon: IconCodes.icon_Tb_phone,
	},
	{
		name: 'Address',
		value: '15950 West Dodge Road, Suite 300, Omaha, NE  68118',
		icon: IconCodes.icon_Tb_map_pin,
	},
];

export const selectedSourceFormKey = 'bankAccount.fundingMethod';

export const formValueKeys = {
	ach: {
		bankAccountNumber: {
			id: 'bankAccount.bankAccountNumber',
			key: 'bankAccount.bankAccountNumber',
		},
		bankName: { id: 'bankAccount.bankName', key: 'bankAccount.bankName' },
		bankBranch: { id: 'bankAccount.bankBranch', key: 'bankAccount.bankBranch' },
		personalOrCorporateAccount: {
			id: 'isPersonalAccount',
			key: 'bankAccount.isPersonalAccount',
		},
		routingNumber: {
			id: 'bankAccount.routingNumber',
			key: 'bankAccount.routingNumber',
		},
		bankAccountType: {
			id: 'bankAccount.bankAccountType',
			key: 'bankAccount.bankAccountType',
			enum: 'BankAccountType',
		},
		bankAccountTitle: {
			id: 'bankAccount.bankAccountTitle',
			key: 'bankAccount.bankAccountTitle',
		},
		bankAccountHasOtherOwners: {
			id: 'bankAccount.bankAccountHasOtherOwners',
			key: 'bankAccount.bankAccountHasOtherOwners',
			enum: 'YesOrNo',
		},
		detailsToBeProvided: {
			id: 'detailsToBeProvided',
			key: 'detailsToBeProvided',
		},
		otherOwnerEmail: {
			id: 'bankAccount.otherOwnerEmail',
			key: 'bankAccount.otherOwnerEmail',
		},
		otherOwnerName: {
			id: 'bankAccount.otherOwnerName',
			key: 'bankAccount.otherOwnerName',
		},
		IRADistributionReasons: {
			id: 'irADistributionReason',
			key: 'irADistributionReason',
			enum: 'IRADistributionReasons',
		},
		fiveYearRequirementMet: {
			id: 'rothIRA5YearRequirementMet',
			key: 'rothIRA5YearRequirementMet',
			enum: 'YesOrNo',
		},
		meetsAgeRequirement: {
			id: 'accountHolderMeetsAgeRequirement',
			key: 'accountHolderMeetsAgeRequirement',
			enum: 'MeetsAgeRequirementOptions',
		},
		originalFundingDate: {
			id: 'siMPLEIRAOriginalFundingDate',
			key: 'siMPLEIRAOriginalFundingDate',
		},
		taxyear: { id: 'taxYear', key: 'taxYear' },
		federalIncomeWithholding: {
			id: 'federalIncomeWithholding',
			key: 'federalIncomeWithholding',
			enum: 'FederalIncomeWithholding',
		},
		isAPriorYearExcess: {
			id: 'isPriorYearExcessContribution',
			key: 'isPriorYearExcessContribution',
			enum: 'YesOrNo',
		},
		excessContributionAmount: {
			id: 'excessContributionAmount',
			key: 'excessContributionAmount',
		},
		dateOfexcessContribution: {
			id: 'excessContributionDate',
			key: 'excessContributionDate',
		},
		earningAmountFromExcess: {
			id: 'excessContributionEarnings',
			key: 'excessContributionEarnings',
		},
		netIncomeAttribute: {
			id: 'netAttributableIncome',
			key: 'netAttributableIncome',
		},
		recharacterizationYear: {
			id: 'reCharacterizationYear',
			key: 'reCharacterizationYear',
			enum: 'RecharacterizationYear',
		},
		iraDistributionAmount: {
			id: 'irADistributionAmount',
			key: 'irADistributionAmount',
			enum: 'IraDistributionAmountOptions',
		},
		amount: { id: 'amount', key: 'amount' },
		frequency: { id: 'frequency', key: 'frequency', enum: 'Frequency' },
		startDate: { id: 'startDate', key: 'startDate' },
		federalWithholdingAmount: {
			id: 'federalWithholdingAmount',
			key: 'federalWithholdingAmount',
		},
		federalWithholdingPercent: {
			id: 'federalWithholdingPercent',
			key: 'federalWithholdingPercent',
		},
		taxFilingState: { id: 'taxFilingState', key: 'taxFilingState' },
		stateWithholdingType: {
			id: 'withholdingType',
			key: 'withholdingType',
			enum: 'WithholdingType',
			enum2: 'WithholdingTypeWithoutStateCA',
		},
		optOutOfStateWithholding: {
			id: 'optOutOfStateWithholding',
			key: 'optOutOfStateWithholding',
		},
		stateWithholdingAmount: {
			id: 'stateWithholdingAmount',
			key: 'stateWithholdingAmount',
		},
		stateWithholdingPercent: {
			id: 'stateWithholdingPercent',
			key: 'stateWithholdingPercent',
		},
		stateWithholdingPercentOfFederal: {
			id: 'stateWithholdingPercentOfFederal',
			key: 'stateWithholdingPercentOfFederal',
		},
		deliveryMethod: {
			id: 'otherAccount.deliveryMethod',
			key: 'otherAccount.deliveryMethod',
			enum: 'DelivaryMethodOptionsACH',
		},
		otherAccountToAccountNumber: {
			id: 'otherAccount.accountNumber',
			key: 'otherAccount.accountNumber',
		},
		otherAccountPayeeName: {
			id: 'otherAccount.payeeName',
			key: 'otherAccount.payeeName',
		},
		otherAccountPayeePhoneNumber: {
			id: 'otherAccount.payeePhoneNumber',
			key: 'otherAccount.payeePhoneNumber',
		},
		otherAccountPayeeAddress: {
			id: 'otherAccount.payeeAddress',
			key: 'otherAccount.payeeAddress',
		},
		specialDelivery: {
			id: 'specialDelivery',
			key: 'specialDelivery',
			enum: 'SpacielDelivaryOptions',
		},
		useForBankAccountTransfers: {
			id: 'retainForFutureTransfers',
			key: 'retainForFutureTransfers',
			enum: 'YesOrNo',
		},
		usedForAccountFundingAmount: {
			id: 'usedForAccountFundingAmount',
			key: 'amount',
		},
		usedToFundProduct: {
			id: 'isForProductPurchase',
			key: 'useForAccountFunding',
			enum: 'YesOrNo',
		},
		areTheNameAndTaxIdSame: {
			id: 'deliveringAccountTitleSameAsReceiving',
			key: 'deliveringAccountTitleSameAsReceiving',
			enum: 'YesOrNo',
		},
		areYouAnOwnerOnTheBankAccount: {
			id: 'receivingAccountHolderIsOwner',
			key: 'receivingAccountHolderIsOwner',
			enum: 'YesOrNo',
		},
		doYouWantToScheduleARecurringTransfer: {
			id: 'doYouWantToScheduleARecurringTransfer',
			key: 'doYouWantToScheduleARecurringTransfer',
			enum: 'YesOrNo',
		},
		transferDirection: {
			id: 'transferDirection',
			key: 'transferDirection',
			enum: 'TransferDirection',
		},
		recurringTransferAmount: {
			id: 'recurringTransferAmount',
			key: 'recurringTransferAmount',
		},
		recurringTransferFrequency: {
			id: 'recurringTransferFrequency',
			key: 'recurringTransferFrequency',
			enum: 'Frequency',
		},

		recurringTransferStartDate: {
			id: 'recurringTransferStartDate',
			key: 'recurringTransferStartDate',
		},
		recurringTransferSecondDate: {
			id: 'recurringTransferSecondDate',
			key: 'recurringTransferSecondDate',
		},
		recurringTransferEndDate: {
			id: 'recurringTransferEndDate',
			key: 'recurringTransferEndDate',
		},
	},

	check: {
		usedForAccountFunding: {
			id: 'usedForAccountFunding',
			key: 'useForAccountFunding',
			enum: 'YesOrNo',
		},
		usedForAccountFundingAmount: {
			id: 'usedForAccountFundingAmount',
			key: 'amount',
		},
		isTheCheckAccompanyingApplication: {
			id: 'bankAccount.checkIncluded',
			key: 'bankAccount.checkIncluded',
			enum: 'YesOrNo',
		},
		amount: { id: 'amount', key: 'amount' },
		depositMethod: {
			id: 'checkDepositOption',
			key: 'checkDepositOption',
			enum: 'CheckDepositOptions',
		},
		payableTo: {
			id: 'checkPayableTo',
			key: 'checkPayableTo',
			enum: 'PayableToOptions',
		},
	},
	wire_transfer: {
		amount: { id: 'amount', key: 'amount' },
		usedForAccountFundingAmount: {
			id: 'usedForAccountFundingAmount',
			key: 'amount',
		},
		usedForAccountFunding: {
			id: 'usedForAccountFunding',
			key: 'useForAccountFunding',
			enum: 'YesOrNo',
		},
	},
	'external_transfer_-_acat': {
		detailsToBeProvided: {
			id: 'detailsToBeProvided',
			key: 'detailsToBeProvided',
		},
		clearingNumber: {
			id: 'otherAccount.clearingNumber',
			key: 'otherAccount.clearingNumber',
		},
		accountType: {
			id: 'otherAccount.accountType',
			key: 'otherAccount.accountType',
			enum: 'AccountTypeOptions',
		},
		otherAccountType: {
			id: 'otherAccount.otherAccountType',
			key: 'otherAccount.otherAccountType',
		},
		firmName: {
			id: 'otherAccount.firmName',
			key: 'otherAccount.firmName',
		},
		firmPhoneNumber: {
			id: 'otherAccount.firmPhoneNumber',
			key: 'otherAccount.firmPhoneNumber',
		},
		firmAddress: {
			id: 'otherAccount.firmAddress',
			key: 'otherAccount.firmAddress',
		},
		accountNumber: {
			id: 'otherAccount.accountNumber',
			key: 'otherAccount.accountNumber',
		},
		ssn: {
			id: 'otherAccount.ssn',
			key: 'otherAccount.ssn',
		},
		accountTitle: {
			id: 'otherAccount.accountTitle',
			key: 'otherAccount.accountTitle',
		},
		transferType: {
			id: 'transferType',
			key: 'transferType',
			enum: 'TransferTypeOpionsIRA',
			enum2: 'TransferType',
		},
		brokerageOrTrustCompany: {
			id: 'brokerageOrTrustCompany',
			key: 'securitiesTransferType',
			enum: 'BrokerageOrTrustCompanyOptions',
		},
		cashAmount: {
			id: 'cashAmount',
			key: 'amount',
		},
		mutualFundTransferType: {
			id: 'securitiesTransferType',
			key: 'securitiesTransferType',
			enum: 'MutualFundTransferType',
		},
		loopingRowKey: {
			id: 'securities',
			key: 'securities',
		},
		description: {
			id: 'description',
			key: 'description',
		},
		cusip: {
			id: 'cusip',
			key: 'cusip',
		},
		quantity: {
			id: 'quantity',
			key: 'quantity',
		},
		symbol: {
			id: 'symbol',
			key: 'symbol',
		},
		deliveringAccountTitleSameAsReceiving: {
			id: 'otherAccount.deliveringAccountTitleSameAsReceiving',
			key: 'otherAccount.deliveringAccountTitleSameAsReceiving',
			enum: 'YesOrNo',
		},
		liquidationAndTransferType: {
			id: 'liquidateAndTransferOption',
			key: 'liquidateAndTransferOption',
			enum: 'LiquidateAndTransferOption',
		},
		firstMiddleNameChanged: {
			id: 'otherAccount.firstMiddleNameChanged',
			key: 'otherAccount.firstMiddleNameChanged',
			enum: 'YesOrNo',
		},
		lastNameChanged: {
			id: 'otherAccount.lastNameChanged',
			key: 'otherAccount.lastNameChanged',
			enum: 'YesOrNo',
		},
		accountTitleOfDeliveringAccount: {
			id: 'otherAccount.accountTitle',
			key: 'otherAccount.accountTitle',
		},
		fundAccountNumber: {
			id: 'fundAccountNumber',
			key: 'fundAccountNumber',
		},
		mutualFundTransferOption: {
			id: 'mutualFundTransferOption',
			key: 'mutualFundTransferOption',
			enum: 'TransferOptions',
		},
		transferAll: {
			id: 'transferAll',
			key: 'transferAll',
			enum: 'TransferAllOptions',
		},
		dividendOption: {
			id: 'dividendOption',
			key: 'dividendOption',
			enum: 'CapitalAndDvidentOptions',
		},
		capitalGainsOption: {
			id: 'capitalGainsOption',
			key: 'capitalGainsOption',
			enum: 'CapitalAndDvidentOptions',
		},
		bankAccountTransferType: {
			id: 'bankAccountTransferType',
			key: 'bankAccountTransferType',
			enum: 'BankTransferTypeOptions',
			enum2: 'BankTransferTypeOptionsIRA',
		},
		partialTransferAmount: {
			id: 'partialTransferAmount',
			key: 'partialTransferAmount',
		},
		cdMaturityDate: { id: 'cdMaturityDate', key: 'cdMaturityDate' },
		loppingRow2Key: {
			id: 'otherAccount.accountOwners',
			key: 'otherAccount.accountOwners',
		},
		accountOwnerOfDeliveringAccount: {
			id: 'fullName',
			key: 'fullName',
		},
		emailAddress: { id: 'primaryEmail', key: 'primaryEmail' },
		printNameOnAccount: {
			id: 'otherAccount.payeeName',
			key: 'otherAccount.payeeName',
		},
		printAlternateNameOnDeliveringAccount: {
			id: 'otherAccount.accountHolderAlternateName',
			key: 'otherAccount.accountHolderAlternateName',
		},
		usedForAccountFunding: {
			id: 'usedForAccountFunding',
			key: 'useForAccountFunding',
			enum: 'YesOrNo',
		},
		usedForAccountFundingAmount: {
			id: 'usedForAccountFundingAmount',
			key: 'amount',
		},
		mutualFundTransferOptionTransferInKind: {
			id: 'mutualFundTransferOptionTransferInKind',
			key: 'mutualFundTransferOptionTransferInKind',
			enum: 'MutualFundTransferOptionTransferInKind',
		},
		annuityTransfer: {
			id: 'AnnuityTransfer',
			key: 'annuityTransferOption',
			enum: 'AnnuityTransferOptions',
		},
		partialLiquidation: {
			id: 'PartialLiquidation',
			key: 'partialTransferAmount',
		},
		transferAgentTransfer: {
			id: 'transferAgentTransfer',
			key: 'transferAgentTransferInstructions',
			enum: 'TransferAgentTransfer',
		},
		ibdToInitiateTransfer: {
			id: 'ibdToInitiateTransfer',
			key: 'ibDToInitiateTransfer',
			enum: 'YesOrNo',
		},
		rolloverDetails: {
			id: 'rolloverDetails',
			key: 'rolloverSolicited',
			enum: 'RolloverDetails',
		},
		rolloverQuantityChoice: {
			id: 'rolloverQuantityChoice',
			key: 'rolloverQuantityChoice',
			enum: 'RolloverInstructions',
		},
		partialTransferPercent: {
			id: 'partialTransferPercent',
			key: 'partialTransferPercent',
		},
		amount: { id: 'amount', key: 'amount' },
		sequiritiesAmount: { id: 'securities.amount', key: 'amount' },
	},
	'external_transfer_-_non-acat': {
		detailsToBeProvided: {
			id: 'detailsToBeProvided',
			key: 'detailsToBeProvided',
		},
		clearingNumber: {
			id: 'otherAccount.clearingNumber',
			key: 'otherAccount.clearingNumber',
		},
		accountType: {
			id: 'otherAccount.accountType',
			key: 'otherAccount.accountType',
			enum: 'AccountTypeOptions',
		},
		firmName: {
			id: 'otherAccount.firmName',
			key: 'otherAccount.firmName',
		},
		firmPhoneNumber: {
			id: 'otherAccount.firmPhoneNumber',
			key: 'otherAccount.firmPhoneNumber',
		},
		deliveringAccountTitleSameAsReceiving: {
			id: 'otherAccount.deliveringAccountTitleSameAsReceiving',
			key: 'otherAccount.deliveringAccountTitleSameAsReceiving',
			enum: 'YesOrNo',
		},
		firmAddress: {
			id: 'otherAccount.firmAddress',
			key: 'otherAccount.firmAddress',
		},
		accountNumber: {
			id: 'otherAccount.accountNumber',
			key: 'otherAccount.accountNumber',
		},
		ssn: {
			id: 'otherAccount.ssn',
			key: 'otherAccount.ssn',
		},
		accountTitle: {
			id: 'otherAccount.accountTitle',
			key: 'otherAccount.accountTitle',
		},
		transferType: {
			id: 'transferType',
			key: 'transferType',
			enum: 'TransferTypeNonACAT',
		},
		loopingRowKey: {
			id: 'securities',
			key: 'securities',
		},
		transferInstructionsDescription: {
			id: 'description',
			key: 'description',
		},
		transferInstructionsSymbol: {
			id: 'cusip',
			key: 'cusip',
		},
		transferInstructionsQuantity: {
			id: 'quantity',
			key: 'quantity',
		},
		accountTitleOfDeliveringAccount: {
			id: 'otherAccount.accountTitle',
			key: 'otherAccount.accountTitle',
		},
		loppingRow2Key: {
			id: 'otherAccount.accountOwners',
			key: 'otherAccount.accountOwners',
		},
		accountOwnerOfDeliveringAccount: {
			id: 'fullName',
			key: 'fullName',
		},
		emailAddress: { id: 'primaryEmail', key: 'primaryEmail' },
		printNameOnAccount: {
			id: 'otherAccount.payeeName',
			key: 'otherAccount.payeeName',
		},
		printAlternateNameOnDeliveringAccount: {
			id: 'otherAccount.accountHolderAlternateName',
			key: 'otherAccount.accountHolderAlternateName',
		},
	},
	internal_transfer: {
		internalTransferTopLevel: {
			id: 'internalTransferTopLevel',
			key: 'internalTransferType',
			enum: 'InternalTransferTopLevel',
		},
		detailsToBeProvided: {
			id: 'detailsToBeProvided',
			key: 'detailsToBeProvided',
		},
		deliveringAccountAccountNumber: {
			id: 'otherAccount.accountNumber',
			key: 'otherAccount.accountNumber',
		},
		deliveringAccountAccountTitle: {
			id: 'otherAccount.accountTitle',
			key: 'otherAccount.accountTitle',
		},
		dateOfDeath: { id: 'dateOfDeath', key: 'dateOfDeath' },
		requireCostBasisStepUp: {
			id: 'costBasisStepUp',
			key: 'costBasisStepUp',
			enum: 'YesOrNo',
		},
		transferInstructionsTransferType: {
			id: 'transferType',
			key: 'transferType',
			enum: 'TransferTypeOptionsInernalTrans',
		},
		transferInstructionsCloseAccount: {
			id: 'closeAccount',
			key: 'closeAccount',
			enum: 'YesOrNo',
		},
		transferInstructionsTypeofInstructions: {
			id: 'transferInstructionsTypeofInstructions',
			key: 'transferInstructionsTypeofInstructions',
		},
		transferIsIraContribution: {
			id: 'isIRAContribution',
			key: 'isIRAContribution',
			enum: 'YesOrNo',
		},
		transferInstructionsContributionYear: {
			id: 'contributionYear',
			key: 'contributionYear',
			enum: 'IRAContributionYears',
		},
		transferInstructionsWhatAreYouTransfering: {
			id: 'internalTransferAssetTypeOptions',
			key: 'internalTransferAssetTypeOptions',
			enum: 'InternalTransferTypeOptions',
			enum2: 'InternalTransferTypeOptionsForNonIraContributions',
		},
		transferInstructionsCashTransferMethod: {
			id: 'bankAccountTransferType',
			key: 'bankAccountTransferType',
			enum: 'InternalTransferTypeOptionsForCashTransfer',
		},
		transferInstructionsCashAmount: { id: 'amount', key: 'amount' },
		transferInstructionsCashAmountV2: {
			id: 'cashAmount',
			key: 'cashAmount',
		},
		loopingRowKey: { id: 'securities', key: 'securities' },
		partialAccountTransferDescription: {
			id: 'description',
			key: 'description',
		},
		partialAccountTransferSymbol: { id: 'cusip', key: 'symbol' },
		partialAccountTransferQuantity: { id: 'quantity', key: 'quantity' },
		partialAccountTransferAmountOfShares: {
			id: 'amountOfShares',
			key: 'quantity',
		},
		partialLiquidationAmountType: {
			id: 'transferUnits',
			key: 'transferUnits',
			enum: 'AmountTypeOptions',
		},

		partialLiquidationAmount: {
			id: 'securities.amount',
			key: 'amount',
		},

		usedForAccountFunding: {
			id: 'usedForAccountFunding',
			key: 'useForAccountFunding',
			enum: 'YesOrNo',
		},
		amount: { id: 'amount', key: 'amount' },
		existingAccountType: {
			id: 'sourceIRAAccountType',
			key: 'sourceIRAAccountType',
			enum: 'ExistingAccountTypeOptions',
		},

		loopingRow2Key: {
			id: 'accountCreationDetail.sourceOfFunds',
			key: 'accountCreationDetail.sourceOfFunds',
		},
		transferInstructionsAccountNumber: {
			id: 'transferInstructionsAccountNumber',
			key: 'accountNumbers',
		},

		transferInstructions: {
			id: 'bankAccountTransferType',
			key: 'bankAccountTransferType',
			enum: 'TransferInstructions',
		},
		maturityDate: {
			id: 'cdMaturityDate',
			key: 'cdMaturityDate',
		},
		liquidationInstructions: {
			id: 'securitiesTransferType',
			key: 'securitiesTransferType',
			enum: 'LiquidationInstructions',
		},
		partialLiquidationAmount2: {
			id: 'partialTransferAmount',
			key: 'partialTransferAmount',
		},
		RMDOptions: {
			id: 'rmdStatus',
			key: 'rmdStatus',
			enum: 'RMDOptions',
		},
		note: {
			id: 'notes',
			key: 'notes',
		},
		usedToFundProduct: {
			id: 'isForProductPurchase',
			key: 'isForProductPurchase',
			enum: 'YesOrNo',
		},
		transferInstructions60DayRollover: {
			id: 'transferInstructions60DayRollover',
			key: 'isRolloverWithin60Days',
			enum: 'YesOrNo',
		},
	},
};

export const sourceOfFundToMetadataKey = {
	ach: { key: 'enableAch', name: 'Ach' },
	check: { key: 'enableCheck', name: 'Check' },
	wire_transfer: { key: 'enableWireTransfer', name: 'Wire Transfer' },
	'external_transfer_-_acat': {
		key: 'enableExtAcat',
		name: 'External Transfer - ACAT',
	},
	'external_transfer_-_non-acat': {
		key: 'enableExtNonAcat',
		name: 'External Transfer - Non-ACAT',
	},
	internal_transfer: { key: 'enableIntTransfer', name: 'Internal Transfer' },
};
export const collapseData = {
	ach: [
		{
			icon: IconCodes.icon_Tb_building_bank,
			formKey: formValueKeys.ach.bankName,
			name: 'Bank',
		},
		{
			icon: IconCodes['icon_Ax_bank-check'],
			formKey: formValueKeys.ach.bankAccountNumber,
			name: 'Account',
		},
	],
	check: [
		{
			icon: IconCodes.icon_Tb_currency_dollar,
			formKey: formValueKeys.check.amount,
			name: 'Amount',
			formatFunction: formatCurrency,
		},
	],
	wire_transfer: [
		{
			icon: IconCodes.icon_Tb_currency_dollar,
			formKey: formValueKeys.wire_transfer.amount,
			name: 'Amount',
			formatFunction: formatCurrency,
		},
	],
	'external_transfer_-_acat': [
		{
			icon: IconCodes.icon_Tb_input_check,
			formKey: formValueKeys['external_transfer_-_acat'].clearingNumber,
			name: 'Clearing Number',
		},
	],
	'external_transfer_-_non-acat': [
		{
			icon: IconCodes.icon_Tb_input_check,
			formKey: formValueKeys['external_transfer_-_acat'].clearingNumber,
			name: 'Clearing Number',
		},
	],
	internal_transfer: [
		{
			icon: IconCodes['icon_Ax_bank-check'],
			formKey: formValueKeys.internal_transfer.deliveringAccountAccountNumber,
			name: 'Account',
		},
	],
};

export const iraCodes = [
	'IRA-TRADITIONAL',
	'IRA-ROLL-OVER',
	'IRA-ROTH',
	'IRA-SIMPLE',
	'IRA-SEP',
	'IRA-INHERITED',
	'IRA-INHERITED-ROTH',
];

export const requuestReasonValueOfRoth = 'Roth';
export const requuestReasonValueOfSimpleIra = 'SIMPLE IRA';
export const requuestReasonValueOfReturnOfExcessContribution =
	'Return of Excess Contribution';
export const requuestReasonValueOfRecharacterization = 'Recharacterization';
export const requuestReasonValues = [
	{
		label: 'Normal - age 59 1/2 and older',
		value: 'Normal - age 59 1/2 and older',
	},
	{ label: 'Early', value: 'Early' },
	{ label: 'Roth', value: 'Roth' },
	{ label: 'SIMPLE IRA', value: 'SIMPLE IRA' },
	{ label: 'Substantially Equal Series', value: 'Substantially Equal Series' },
	{ label: 'Permanent Disability', value: 'Permanent Disability' },
	{ label: 'Roth Conversion', value: 'Roth Conversion' },
	{
		label: 'Due to Death from Inherited Beneficiary Account',
		value: 'Due to Death from Inherited Beneficiary Account',
	},
	{
		label: 'Direct Transfer to an Eligible Employer-Sponsored Retirement Plan',
		value: 'Direct Transfer to an Eligible Employer-Sponsored Retirement Plan',
	},
	{
		label: 'Return of Excess Contribution',
		value: 'Return of Excess Contribution',
	},
	{ label: 'Recharacterization', value: 'Recharacterization' },
];

export const methodeOfDelivaryJouryeEntryValue = 'Journal entry';
export const methodeOfDelevaryDelivarCheckOrAddress =
	'Deliver check or scurities in name of the account owner address of record';

export const delivaryMethodOptionsACH = [
	{ label: 'Journal entry', value: 'Journal entry' },
	{
		label:
			'Deliver check or scurities in name of the account owner address of record',
		value:
			'Deliver check or scurities in name of the account owner address of record',
	},

	{ label: 'ACH', value: 'ACH' },
];

export const specialDelivaryAlternatePayeValue = 'Alternate Payee';

export const yesOrNoLabelValueOptions = [
	{ label: 'Yes', value: true },
	{ label: 'No', value: false },
];

export const distributionAmountFormValue = 'Distribute Amount';

export const recharacterizationYearOptions = [
	{ label: 'Same year', value: 'Same year' },
	{ label: 'Prior year', value: 'Prior year' },
];
export const meetsAgeRequirementOptions = [
	{ label: 'Checking', value: true },
	{ label: 'Savings', value: false },
];

export const bankAccountTypeOptions = [
	{ label: 'Checking', value: 'Checking' },
	{ label: 'Savings', value: 'Savings' },
];
export const bankAccountTypeOptionsCitizen = [
	{ label: 'Citizens-Checking', value: 'Citizens-Checking' },
	{ label: 'Citizens-Savings', value: 'Citizens-Savings' },
	{ label: 'Outside Bank-Checking', value: 'Outside Bank-Checking' },
	{ label: 'Outside Bank-Savings', value: 'Outside Bank-Savings' },
];
export const externalTransferAnnuityTransferOptionsFullMeturity =
	'Full at Maturity - Liquidate Annuity and transfer all cash';
export const externalTransferAnnuityTransferOptionsPartialMeturity =
	'Partial at Maturity - Liquidate Annuity and transfer cash';
export const externalTransferCeritificateOfDepositeLiquidateAndTransferOptionFullAtMaturityLiquidateCDAndTransferAllCash =
	'Full at Maturity - Liquidate CD and transfer all cash';
export const externalTransferCeritificateOfDepositeLiquidateAndTransferOptionPartialAtMaturityLiquidateCDAndTransferCash =
	'Partial at Maturity - Liquidate CD and transfer cash';
export const transferTypeValueOfBrokerageTrustCompanyPartialAccountTransfer =
	'Brokerage/Trust Company Partial Account Transfer';
export const transferTypeValueOfMutualFundCompanyTransferLiquidation =
	'Mutual Fund Company Transfer /Liquidation';
export const transferTypeOpionsIRAValueofBankOrCreditUnionTransfer =
	'Bank or Credit Union Transfer';
export const transferTypeValueOfAnnuityPartialLiquidation =
	'Annuity Partial Liquidation';
export const transferTypeValueOfTransferAgentTransfer =
	'Transfer Agent Transfer';
export const transferTypeNonACATValueOfBrockeragePartial =
	'Brokerage/Trust Company Partial Account Transfer';
export const transferTypeNonACAT = [
	{
		label: 'Brokerage/Trust Company Full Account Transfer',
		value: 'Brokerage/Trust Company Full Account Transfer',
		icon: IconCodes.icon_Tb_switch_horizontal,
	},
	{
		label: 'Brokerage/Trust Company Partial Account Transfer',
		value: 'Brokerage/Trust Company Partial Account Transfer',
		icon: IconCodes.icon_Tb_switch_horizontal,
	},
];
export const doNotShowAlertOnSourceSelectionStoargeKey =
	'doNotShowAlertOnSourceSelection';
export const transferTypeOptions = [
	...transferTypeNonACAT,
	{
		label: 'Mutual Fund Company Transfer /Liquidation',
		value: 'Mutual Fund Company Transfer /Liquidation',
		icon: IconCodes.icon_Tb_chart_line,
	},
	{
		label: 'Annuity Full Liquidation',
		value: 'Annuity Full Liquidation',
		icon: IconCodes.icon_Tb_coin,
	},
	{
		label: 'Annuity Partial Liquidation',
		value: 'Annuity Partial Liquidation',
		icon: IconCodes.icon_Tb_coin,
	},
	{
		label: 'Transfer Agent Transfer',
		value: 'Transfer Agent Transfer',
		icon: IconCodes.icon_Tb_transfer,
	},
];
export const otherAccountType = 'Other';
export const transferTypeOpionsIRA = [
	...transferTypeOptions,
	{
		label: 'Bank or Credit Union Transfer',
		value: 'Bank or Credit Union Transfer',
		icon: IconCodes.icon_Tb_building_bank,
	},
];

export const transferInstructionsTransferTypeFullAccountValue =
	'Full Account Transfer';
export const transferInstructionsTransferTypePartialAccountValue =
	'Partial Account Transfer';
export const transferTypeOptionsInernalTrans = [
	{ label: 'Full Account Transfer', value: 'Full Account Transfer' },
	{ label: 'Partial Account Transfer', value: 'Partial Account Transfer' },
];

export const iraContributionYears = [
	{ label: 'Current Year', value: 'Current Year' },
	{ label: 'Prior Year', value: 'Prior Year' },
];
export const internalTransferTypeOptionsValueOfAllCash = 'Cash';

export const internalTransferTypeOptionsValueOfSpecificAmountOfCash =
	'Specific Amount Of Cash';
export const internalTransferTypeOptionsValueOfSecurities = 'Securities';
export const internalTransferTypeOptionsValueOfCash = 'Cash';
export const internalTransferTypeOptions = [{ label: 'Cash', value: 'Cash' }];
export const internalTransferTypeOptionsForCashTransfer = [
	{ label: 'All Cash', value: 'All Cash' },
	{ label: 'Specific Amount Of Cash', value: 'Specific Amount Of Cash' },
];
export const internalTransferTypeOptionsForNonIraContributions = [
	...internalTransferTypeOptions,
	{ label: 'Securities', value: 'Securities' },
];
export const transferAllOptionsValueOfShareAmount = false;
export const transferAllOptions = [
	{ label: 'All', value: true },
	{ label: 'Share Amount', value: false },
];
export const transferOptionsValueOfTransferInKindOptions = 'Transfer in Kind';
export const transferOptionsValueOfLiquidateOptions = 'Liquidate';

export const transferOptions = [
	{ label: 'Transfer in Kind', value: 'Transfer in Kind' },
	{ label: 'Liquidate', value: 'Liquidate' },
];
export const bankTransferTypeOptionsValueOfTransferPartialCash =
	'Transfer Partial Cash';
export const bankTransferTypeOptionsValueOfLiquidateCDAtMaturityAndTransferCash =
	'Liquidate CD at Maturity and Transfer Cash';

export const bankTransferTypeOptions = [
	{ label: 'Transfer All Cash', value: 'Transfer All Cash' },
	{ label: 'Transfer Partial Cash', value: 'Transfer Partial Cash' },
	{
		label: 'Liquidate CD Immediately and Transfer Cash',
		value: 'Liquidate CD Immediately and Transfer Cash',
	},
	{
		label: 'Liquidate CD at Maturity and Transfer Cash',
		value: 'Liquidate CD at Maturity and Transfer Cash',
	},
];

export const sourceToInlineSelections = {
	ach: [],
	check: [],
	wire_transfer: [],
	'external_transfer_-_acat': [
		{
			id: 'acat-1',
			heading: 'Select a Transfer Instruction',
			options: 'BankTransferTypeCitizen',
			optionsIRA: 'BankTransferTypeOptionsIRA',
			formKey: formValueKeys['external_transfer_-_acat'].transferType.key,
		},
	],
	'external_transfer_-_non-acat': [
		{
			id: 'non-acat-1',
			heading: 'Select a Transfer Instruction',
			options: 'TransferTypeNonACAT',
			optionsIRA: 'TransferTypeNonACAT',
			formKey: formValueKeys['external_transfer_-_acat'].transferType.key,
		},
	],
	internal_transfer: [],
};
export const fedaralWithHoldingValueOfA = 'Withhold 10% federal income tax';
export const fedaralWithHoldingValueOfB = 'Withhold federal income tax %';
export const fedaralWithHoldingValues = [
	{
		value: 'Withhold 10% federal income tax',
		label: 'Withhold 10% federal income tax',
	},
	{
		value: 'Withhold federal income tax %',
		label: 'Withhold federal income tax %',
	},
];
export const withholdingTypeOptionsValueOFDoNotWithHold = 'Do not withhold';
export const withholdingTypeOptionsValueOFWithholdAmount = 'Withhold $';
export const withholdingTypeOptionsValueOfWithholdPercentage = 'Withhold %';
export const withholdingTypeOptionsValueOWithholdPercOfFederalWithholding =
	'Withhold % of federal withholding';
export const withholdingTypeOptionsWithoutStateCA = [
	{ value: 'Do not withhold', label: 'Do not withhold' },
	{ value: 'Withhold $', label: 'Withhold $' },
	{ value: 'Withhold %', label: 'Withhold %' },
];
export const withholdingTypeOptions = [
	...withholdingTypeOptionsWithoutStateCA,
	{
		value: 'Withhold % of federal withholding',
		label: 'Withhold % of federal withholding',
	},
];

export const capitalAndDvidentOptions = [
	{ value: 'Transfer Immediately', label: 'Transfer Immediately' },
	{ value: 'Transfer at maturity date', label: 'Transfer at maturity date' },
];

export const RMDOptions = [
	{
		value: 'RMD Satisfied (RMD prior to transfer)',
		label: 'RMD Satisfied (RMD prior to transfer)',
	},
	{
		value: 'Transfer (RMD will transferred to Citizens Securities)',
		label: 'Transfer (RMD will transferred to Citizens Securities)',
	},
];

export const transferInstructionsMeturityDate = 'Transfer at maturity date';
export const transferInstructions = [
	{ value: 'Transfer Immediately', label: 'Transfer Immediately' },
	{ value: 'Transfer at maturity date', label: 'Transfer at maturity date' },
];

export const liquidationInstructionsValueOfLiquidateAndTransferOfMyAssets =
	'Liquidate and transfer $ of my assets';
export const liquidationInstructions = [
	{
		value: 'Liquidate and transfer 100% of my assets',
		label: 'Liquidate and transfer 100% of my assets',
	},
	{
		value: 'Liquidate and transfer $ of my assets',
		label: 'Liquidate and transfer $ of my assets',
	},
];
export const isTDepositMethodePhysical = 'Physical';
export const depositMethodOptions = [
	{ value: 'Physical', label: 'Physical' },
	{ value: 'Remote Deposit', label: 'Remote Deposit' },
];
export const payableToValueOfNationalFinancialService =
	'National Financial Services';
export const payableToOptions = [
	{
		value: 'National Financial Services',
		label: 'National Financial Services',
	},
	{ value: 'Insurance Company', label: 'Insurance Company' },
];

export const internalTransferTopLevelEnum = [
	{
		name: 'Internal_Bank IRA CD_Savg ',
		value: 'Internal_Bank IRA CD_Savg ',
	},
	{
		name: 'Internal_Existing CSI Journal',
		value: 'Internal_Existing CSI Journal',
	},
];
export const existingAccountTypeOptions = [
	{ label: 'Traditional IRA', value: 'Traditional IRA' },
	{ label: 'Roth IRA', value: 'Roth IRA' },
	{ label: 'SEP IRA', value: 'SEP IRA' },
	{ label: 'Beneficiary IRA', value: 'Beneficiary IRA' },
];
export const amountTypeOptionsValueOfShares = 'Shares';
export const amountTypeOptionsValueOfDollar = '$';
export const amountTypeOptions = [
	{ value: 'Shares', label: 'Shares' },
	{ value: '$', label: '$' },
];
export const brokerageOrTrustCompanyOptionsPartialAccountTransfer =
	'Partial Account Transfer,In Kind';
export const internalTransferTopLevelInternalBanIRACDSavg =
	'Bank IRA CD/Savings Transfer';
export const internalTransferTopLevelInternalExistingCSIJournal =
	'Existing CSI Account Journal';
export const annuityTransferOptionsValueOfPartialLiquidation =
	'Partial Annuity Surrender';
export const employerSponsordPlanRolloverValueOfDirectRolloverInitiationRequest =
	true;
export const employerSponsordPlanRolloverValueOfDirectRolloverDisclosureAcknowledgement =
	false;
export const workflowIdForRoutingNumberBasedBank = 'cv7r7cnjdceio0onudgg';

export const workflowApiUrlForRoutingNumberBasedBank = `/instances/execute/sync/domain/wealthdomain/${workflowIdForRoutingNumberBasedBank}`;

export const RolloverInstructionsValueOfLiquidateAndRolloverOfMyAssets =
	'Liquidate and Rollover $ of My Assets';
export const RolloverInstructionsValueOfLiquidateAndRolloverPercentageOfMyAssets =
	'Liquidate and Rollover % of My Assets';
export const defaultEnums = {
	LiquidateAndTransferOption: [
		{
			optionLabel: 'Full Immediate - Liquidate CD and transfer all cash',
			optionValue: 'Full Immediate - Liquidate CD and transfer all cash',
		},
		{
			optionLabel: 'Partial Immediate - Liquidate CD and transfer cash',
			optionValue: 'Partial Immediate - Liquidate CD and transfer cash',
		},
		{
			optionLabel: 'Full at Maturity - Liquidate CD and transfer all cash',
			optionValue: 'Full at Maturity - Liquidate CD and transfer all cash',
		},
		{
			optionLabel: 'Partial at Maturity - Liquidate CD and transfer cash',
			optionValue: 'Partial at Maturity - Liquidate CD and transfer cash',
		},
	],
	MutualFundTransferType: [
		{
			optionLabel: 'Full Transfer',
			optionValue: 'Full Transfer',
		},
		{
			optionLabel: 'Partial Transfer',
			optionValue: 'Partial Transfer',
		},
	],
	RolloverInstructions: [
		{
			optionLabel: 'Liquidate and Rollover 100% of My Assets',
			optionValue: 'Liquidate and Rollover 100% of My Assets',
		},
		{
			optionLabel: 'Liquidate and Rollover $ of My Assets',
			optionValue: 'Liquidate and Rollover $ of My Assets',
		},
		{
			optionLabel: 'Liquidate and Rollover % of My Assets',
			optionValue: 'Liquidate and Rollover % of My Assets',
		},
	],
	RolloverDetails: [
		{ optionLabel: 'Solicited', optionValue: 'Solicited' },
		{ optionLabel: 'Unsolicited', optionValue: 'Unsolicited' },
	],
	PersonalOrCorporateAccount: [
		{ optionLabel: 'Personal', optionValue: 'Personal' },
		{ optionLabel: 'Corporate', optionValue: 'Corporate' },
	],
	TransferDirection: [
		{
			optionLabel: 'Into the Schwab account',
			optionValue: 'Into the Schwab account',
		},
		{
			optionLabel: 'Out of the Schwab account',
			optionValue: 'Out of the Schwab account',
		},
	],
	FederalIncomeWithholding: [
		{
			optionLabel: 'Withhold 10% federal income tax',
			optionValue: 'Withhold 10% federal income tax',
		},
		{
			optionLabel: 'Withhold federal income tax %',
			optionValue: 'Withhold federal income tax %',
		},
	],

	BankAccountType: [
		{ optionLabel: 'Checking', optionValue: 'Checking' },
		{ optionLabel: 'Savings', optionValue: 'Savings' },
	],
	WithholdingType: [
		{ optionLabel: 'Do not withhold', optionValue: 'Do not withhold' },
		{ optionLabel: 'Withhold $', optionValue: 'Withhold $' },
		{ optionLabel: 'Withhold %', optionValue: 'Withhold %' },
		{
			optionLabel: 'Withhold % of federal withholding',
			optionValue: 'Withhold % of federal withholding',
		},
	],
	SpacielDelivaryOptions: [
		{ optionLabel: 'Overnight deliver', optionValue: 'Overnight deliver' },
		{ optionLabel: 'Saturday overnight', optionValue: 'Saturday overnight' },
		{ optionLabel: 'Alternate Payee', optionValue: 'Alternate Payee' },
	],
	WithholdingTypeWithoutStateCA: [
		{ optionLabel: 'Do not withhold', optionValue: 'Do not withhold' },
		{ optionLabel: 'Withhold $', optionValue: 'Withhold $' },
		{ optionLabel: 'Withhold %', optionValue: 'Withhold %' },
	],
	IRADistributionReasons: [
		{ optionLabel: 'Normal - age 59 1/2 and older', optionValue: 'Normal' },
		{ optionLabel: 'Early', optionValue: 'Early' },
		{ optionLabel: 'Roth', optionValue: 'Roth' },
		{ optionLabel: 'SIMPLE IRA', optionValue: 'SimpleIRA' },
		{
			optionLabel: 'Substantially Equal Series',
			optionValue: 'SubstantiallyEqualSeries',
		},
		{ optionLabel: 'Permanent Disability', optionValue: 'PermanentDisability' },
		{ optionLabel: 'Roth Conversion', optionValue: 'RothConversion' },
		{
			optionLabel: 'Due to Death from Inherited Beneficiary Account',
			optionValue: 'InheritedBeneficiary',
		},
		{
			optionLabel:
				'Direct Transfer to an Eligible Employer-Sponsored Retirement Plan',
			optionValue: 'EmployerPlanTransfer',
		},
		{
			optionLabel: 'Return of Excess Contribution',
			optionValue: 'ExcessContributionReturn',
		},
		{ optionLabel: 'Recharacterization', optionValue: 'Recharacterization' },
	],
	MeetsAgeRequirementOptions: [
		{ optionLabel: 'Checking', optionValue: true },
		{ optionLabel: 'Savings', optionValue: false },
	],
	IraDistributionAmountOptions: [
		{ optionLabel: 'Distribute Amount', optionValue: 'Distribute Amount' },
		{ optionLabel: 'Distribute RMD', optionValue: 'Distribute RMD' },
		{
			optionLabel: 'Distribute earnings (dividends & interest)',
			optionValue: 'Distribute earnings (dividends & interest)',
		},
	],
	BrokerageOrTrustCompanyOptions: [
		{
			optionLabel: 'Full Account Transfer,In Kind',
			optionValue: 'Full Account Transfer,In Kind',
		},
		{
			optionLabel: 'Partial Account Transfer,In Kind',
			optionValue: 'Partial Account Transfer,In Kind',
		},
	],
	EmployerSponsordPlanRollover: [
		{
			optionLabel: 'Direct Rollover Initiation Request',
			optionValue: 'Direct Rollover Initiation Request',
		},
		{
			optionLabel: 'Direct Rollover Disclosure Acknowledgement',
			optionValue: 'Direct Rollover Disclosure Acknowledgement',
		},
	],
	BankTransferTypeCitizen: [
		{
			optionLabel: 'Transfer All Cash',
			optionValue: 'Transfer All Cash',
		},
		{
			optionLabel: 'Liquidate CD Immediately and Transfer Cash',
			optionValue: 'Liquidate CD Immediately and Transfer Cash',
		},
		{
			optionLabel: 'Liquidate CD at Maturity and Transfer Cash',
			optionValue: 'Liquidate CD at Maturity and Transfer Cash',
		},
	],
	TransferAgentTransfer: [
		{
			optionLabel: 'Transfer All Whole Shares and Sell Fractions',
			optionValue: 'Transfer All Whole Shares and Sell Fractions',
		},
		{
			optionLabel: 'Transfer All Whole Shares Only',
			optionValue: 'Transfer All Whole Shares Only',
		},
		{
			optionLabel:
				'Transfer All Whole Shares, Sell Fractions and Close Account',
			optionValue:
				'Transfer All Whole Shares, Sell Fractions and Close Account',
		},
		{
			optionLabel: 'Partial Transfer of Whole Shares',
			optionValue: 'Partial Transfer of Whole Shares',
		},
	],
	AmountTypeCitizen: [
		{ optionLabel: 'Shares', optionValue: 'Shares' },
		{ optionLabel: '$', optionValue: 'Dollar' },
	],
	AnnuityTransferOptions: [
		{
			optionLabel: 'Full Annuity Surrender',
			optionValue: 'Full Annuity Surrender',
		},
		{
			optionLabel: 'Penalty Free Surrender',
			optionValue: 'Penalty Free Surrender',
		},
		{
			optionLabel: 'Partial Annuity Surrender',
			optionValue: 'Partial Annuity Surrender',
		},
	],
	YesOrNo: [
		{ optionLabel: 'Yes', optionValue: true },
		{ optionLabel: 'No', optionValue: false },
	],
	Frequency: [
		{ optionLabel: 'Weekly', optionValue: 'Weekly' },
		{ optionLabel: 'Semi-Monthly', optionValue: 'SemiMonthly' },
		{ optionLabel: 'Monthly', optionValue: 'Monthly' },
		{ optionLabel: 'Quarterly', optionValue: 'Quarterly' },
		{ optionLabel: 'Semi-Annually', optionValue: 'SemiAnnually' },
		{ optionLabel: 'Annually', optionValue: 'Annually' },
	],

	TransferType: [
		{
			optionLabel: 'Brokerage/Trust Company Partial Account Transfer',
			optionValue: 'Brokerage/Trust Company Partial Account Transfer',
			icon: 'icon_Tb_switch_horizontal',
		},
		{
			optionLabel: 'Brokerage/Trust Company Full Account Transfer',
			optionValue: 'Brokerage/Trust Company Full Account Transfer',
			icon: 'icon_Tb_switch_horizontal',
		},
		{
			optionLabel: 'Mutual Fund Company Transfer /Liquidation',
			optionValue: 'Mutual Fund Company Transfer /Liquidation',
			icon: 'icon_Tb_chart_line',
		},
		{
			optionLabel: 'Annuity Full Liquidation',
			optionValue: 'Annuity Full Liquidation',
			icon: 'icon_Tb_coin',
		},
		{
			optionLabel: 'Annuity Partial Liquidation',
			optionValue: 'Annuity Partial Liquidation',
			icon: 'icon_Tb_coin',
		},
		{
			optionLabel: 'Transfer Agent Transfer',
			optionValue: 'Transfer Agent Transfer',
			icon: 'icon_Tb_transfer',
		},
	],
	TransferTypeOpionsIRA: [
		{
			optionLabel: 'Brokerage/Trust Company Partial Account Transfer',
			optionValue: 'Brokerage/Trust Company Partial Account Transfer',
			icon: 'icon_Tb_switch_horizontal',
		},
		{
			optionLabel: 'Brokerage/Trust Company Full Account Transfer',
			optionValue: 'Brokerage/Trust Company Full Account Transfer',
			icon: 'icon_Tb_switch_horizontal',
		},
		{
			optionLabel: 'Mutual Fund Company Transfer /Liquidation',
			optionValue: 'Mutual Fund Company Transfer /Liquidation',
			icon: 'icon_Tb_chart_line',
		},
		{
			optionLabel: 'Annuity Full Liquidation',
			optionValue: 'Annuity Full Liquidation',
			icon: 'icon_Tb_coin',
		},
		{
			optionLabel: 'Annuity Partial Liquidation',
			optionValue: 'Annuity Partial Liquidation',
			icon: 'icon_Tb_coin',
		},
		{
			optionLabel: 'Transfer Agent Transfer',
			optionValue: 'Transfer Agent Transfer',
			icon: 'icon_Tb_transfer',
		},
		{
			optionLabel: 'Bank or Credit Union Transfer',
			optionValue: 'Bank or Credit Union Transfer',
			icon: 'icon_Tb_building_bank',
		},
	],
	ExistingAccountTypeOptions: [
		{ optionLabel: 'Traditional IRA', optionValue: 'Traditional IRA' },
		{ optionLabel: 'Roth IRA', optionValue: 'Roth IRA' },
		{ optionLabel: 'SEP IRA', optionValue: 'SEP IRA' },
		{ optionLabel: 'Beneficiary IRA', optionValue: 'Beneficiary IRA' },
	],
	AccountTypeOptions: [
		{ optionLabel: 'Individual', optionValue: 'Individual' },
		{ optionLabel: 'Joint', optionValue: 'Joint' },
		{ optionLabel: 'Estate', optionValue: 'Estate' },
		{ optionLabel: 'Trust', optionValue: 'Trust' },
		{ optionLabel: 'Corp/Business', optionValue: 'Corp/Business' },
		{ optionLabel: 'UGMA/UTMA', optionValue: 'UGMA/UTMA' },
		{
			optionLabel: 'Traditional, SEP or Rollover IRA',
			optionValue: 'Traditional, SEP or Rollover IRA',
		},
		{ optionLabel: 'Roth IRA', optionValue: 'Roth IRA' },
		{ optionLabel: 'SIMPLE IRA', optionValue: 'SIMPLE IRA' },
		{ optionLabel: 'Beneficiary IRA', optionValue: 'Beneficiary IRA' },
		{
			optionLabel: 'Roth Beneficiary IRA',
			optionValue: 'Roth Beneficiary IRA',
		},
		{ optionLabel: 'Qualified Plan', optionValue: 'Qualified Plan' },
		{ optionLabel: 'Other', optionValue: 'Other' },
	],
	TransferInstructions: [
		{
			optionLabel: 'Transfer immediately',
			optionValue: 'Transfer immediately',
		},
		{
			optionLabel: 'Transfer at maturity date',
			optionValue: 'Transfer at maturity date',
		},
	],
	PayableToOptions: [
		{
			optionLabel: 'National Financial Services',
			optionValue: 'National Financial Services',
		},
		{ optionLabel: 'Insurance Company', optionValue: 'Insurance Company' },
	],
	TransferOptions: [
		{ optionLabel: 'Transfer in Kind', optionValue: 'Transfer in Kind' },
		{ optionLabel: 'Liquidate', optionValue: 'Liquidate' },
	],
	CheckDepositOptions: [
		{ optionLabel: 'Physical', optionValue: 'Physical' },
		{ optionLabel: 'Remote Deposit', optionValue: 'Remote Deposit' },
	],
	RecharacterizationYear: [
		{ optionLabel: 'Same year', optionValue: 'Same year' },
		{ optionLabel: 'Prior year', optionValue: 'Prior year' },
	],
	DelivaryMethodOptionsACH: [
		{ optionLabel: 'Journal entry', optionValue: 'Journal entry' },
		{
			optionLabel:
				'Deliver check or scurities in name of the account owner address of record',
			optionValue:
				'Deliver check or scurities in name of the account owner address of record',
		},

		{ optionLabel: 'ACH', optionValue: 'ACH' },
	],
	LiquidationInstructions: [
		{
			optionLabel: 'Liquidate and transfer 100% of my assets',
			optionValue: 'Liquidate and transfer 100% of my assets',
		},
		{
			optionLabel: 'Liquidate and transfer $ of my assets',
			optionValue: 'Liquidate and transfer $ of my assets',
		},
	],
	RMDOptions: [
		{
			optionLabel: 'RMD Satisfied (RMD prior to transfer)',
			optionValue: 'RMD Satisfied (RMD prior to transfer)',
		},
		{
			optionLabel: 'Transfer (RMD will transferred to Citizens Securities)',
			optionValue: 'Transfer (RMD will transferred to Citizens Securities)',
		},
	],
	TransferTypeOptionsInernalTrans: [
		{
			optionLabel: 'Full Account Transfer',
			optionValue: 'Full Account Transfer',
		},
		{
			optionLabel: 'Partial Account Transfer',
			optionValue: 'Partial Account Transfer',
		},
	],
	InternalTransferTypeOptions: [{ optionLabel: 'Cash', optionValue: 'Cash' }],
	InternalTransferTypeOptionsForNonIraContributions: [
		{ optionLabel: 'Cash', optionValue: 'Cash' },
		{ optionLabel: 'Securities', optionValue: 'Securities' },
	],
	InternalTransferTypeOptionsForCashTransfer: [
		{ optionLabel: 'All Cash', optionValue: 'All Cash' },
		{
			optionLabel: 'Specific Amount Of Cash',
			optionValue: 'Specific Amount Of Cash',
		},
	],
	IRAContributionYears: [
		{ optionLabel: 'Current Year', optionValue: 'Current Year' },
		{ optionLabel: 'Prior Year', optionValue: 'Prior Year' },
	],
	AmountTypeOptions: [
		{ optionLabel: 'Shares', optionValue: 'Shares' },
		{ optionLabel: '$', optionValue: '$' },
	],
	TransferTypeNonACAT: [
		{
			optionLabel: 'Brokerage/Trust Company Full Account Transfer',
			optionValue: 'Brokerage/Trust Company Full Account Transfer',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Brokerage/Trust Company Partial Account Transfer',
			optionValue: 'Brokerage/Trust Company Partial Account Transfer',
			icon: 'icon_-Tb_switch_horizontal',
		},
	],
	BankTransferTypeOptions: [
		{
			optionLabel: 'Brokerage/Trust Company Full Account Transfer',
			optionValue: 'Brokerage/Trust Company Full Account Transfer',
			icon: 'icon_Tb_switch_horizontal',
		},
		{
			optionLabel: 'Brokerage/Trust Company Partial Account Transfer',
			optionValue: 'Brokerage/Trust Company Partial Account Transfer',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Transfer All Cash',
			optionValue: 'Transfer All Cash',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Transfer Partial Cash',
			optionValue: 'Transfer Partial Cash',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Liquidate CD Immediately and Transfer Cash',
			optionValue: 'Liquidate CD Immediately and Transfer Cash',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Liquidate CD at Maturity and Transfer Cash',
			optionValue: 'Liquidate CD at Maturity and Transfer Cash',
			icon: 'icon_-Tb_switch_horizontal',
		},
	],
	BankTransferTypeOptionsIRA: [
		{
			optionLabel: 'Transfer All Cash',
			optionValue: 'Transfer All Cash',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Transfer Partial Cash',
			optionValue: 'Transfer Partial Cash',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Liquidate CD Immediately and Transfer Cash',
			optionValue: 'Liquidate CD Immediately and Transfer Cash',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Liquidate CD at Maturity and Transfer Cash',
			optionValue: 'Liquidate CD at Maturity and Transfer Cash',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Bank or Credit Union Transfer',
			optionValue: 'Bank or Credit Union Transfer',
			icon: 'icon_-Tb_switch_horizontal',
		},
	],
	TransferAllOptions: [
		{ optionLabel: 'All', optionValue: true },
		{ optionLabel: 'Share Amount', optionValue: false },
	],
	CapitalAndDvidentOptions: [
		{
			optionLabel: 'Cash',
			optionValue: 'Cash',
		},
		{
			optionLabel: 'Reinvest',
			optionValue: 'Reinvest',
		},
	],
	InternalTransferTopLevel: [
		{
			optionLabel: 'Bank IRA CD/Savings Transfer',
			optionValue: 'Bank IRA CD/Savings Transfer',
			icon: 'icon_-Tb_building_bank',
		},
		{
			optionLabel: 'Existing CSI Account Journal',
			optionValue: 'Existing CSI Account Journal',
			icon: 'icon_-Tb_switch_horizontal',
		},
	],
	ExternalTransferTopLevel: [
		{
			optionLabel: 'Bank IRA CD/Savings Transfer',
			optionValue: 'Bank IRA CD/Savings Transfer',
		},
		{
			optionLabel: 'Brokerage/Trust Company Transfer',
			optionValue: 'Brokerage/Trust Company Transfer',
		},
		{
			optionLabel: 'Mutual Fund Company Transfer',
			optionValue: 'Mutual Fund Company Transfer',
		},
		{
			optionLabel: 'Annuity Company Transfer',
			optionValue: 'Annuity Company Transfer',
		},
		{
			optionLabel: 'Insurance Transfer/1035 exchange',
			optionValue: 'Insurance Transfer/1035 exchange',
		},
		{
			optionLabel: 'Transfer Agent Transfer',
			optionValue: 'Transfer Agent Transfer',
		},
		{
			optionLabel: 'Employer Sponsored Plan Rollover',
			optionValue: 'Employer Sponsored Plan Rollover',
		},
	],
};

export const externalTransferTopLevelBrokerageTrustCompanyPartialAccountTransfer =
	'Brokerage/Trust Company Transfer';
export const externalTransferTopLevelCertificateOfDepositTransfer =
	'Certificate of Deposit (CD) Transfer';
export const externalTransferTopLevelBankIRACDSavingsTransfer =
	'Bank IRA CD/Savings Transfer';
export const externalTransferTopLevelBrokerageTrustCompanyTransfer =
	'Brokerage/Trust Company Transfer';
export const externalTransferTopLevelMutualFundCompanyTransfer =
	'Mutual Fund Company Transfer';
export const externalTransferTopLevelAnnuityCompanyTransfer =
	'Annuity Company Transfer';
export const externalTransferTopLevelInsuranceTransfer1035Exchange =
	'Insurance Transfer/1035 exchange';
export const externalTransferTopLevelTransferAgentTransfer =
	'Transfer Agent Transfer';
export const externalTransferTopLevelEmployerSponsoredPlanRollover =
	'Employer Sponsored Plan Rollover';
export const fundingMethodChangeEventReuiredFields = ['detailsToBeProvided'];

export const accountValueAsQualified = 'qualified';
export const SEPIRANotAllowedSourceTypes = [
	sourceToLabel.ach,
	sourceToLabel['external_transfer_-_acat'],
	sourceToLabel.internal_transfer,
];
export const SEPIRACode = 'IRA-SEP';
export const iraContributionTypeValuedirectRolloverType = 'direct_rollover';
export const iraContributionTypeValueDirectTransfer =
	'Direct Rollover from Employer Plan';

export const qualifiedAccountExcludeList = {
	ach: [],
	check: [],
	wire_transfer: [],
	'external_transfer_-_acat': [
		externalTransferTopLevelBankIRACDSavingsTransfer,
	],
	'external_transfer_-_non-acat': [],
	internal_transfer: [internalTransferTopLevelInternalBanIRACDSavg],
};
export const directRolloverExcludeList = {
	ach: [],
	check: [],
	wire_transfer: [],
	'external_transfer_-_acat': [
		externalTransferTopLevelEmployerSponsoredPlanRollover,
	],
	'external_transfer_-_non-acat': [],
	internal_transfer: [],
};

export const accountTypeStaticNameMaping = {
	'community property': 'Joint',
	individual: 'Individual',
	'individual tod': 'Individual',
	'inherited ira': 'Beneficiary IRA',
	'inherited roth ira': 'Roth Beneficiary IRA',
	'traditional ira': 'Traditional, SEP or Rollover IRA',
	'ira rollover': 'Traditional, SEP or Rollover IRA',
	jtwros: 'Joint',
	'joint with rights of survivorship': 'Joint',
	'roth ira': 'Roth IRA',
	'sep ira': 'Traditional, SEP or Rollover IRA',
	'simple ira': 'Simple IRA',
	'tenants by entirety': 'Joint',
	'tenants in common': 'Joint',
	custodial: 'UGMA/UTMA',
	estate: 'Estate',
	'revocable trust': 'Trust',
	'irrevocable trust': 'Trust',
	'c corp': 'Corporate/Business',
	's corp': 'Corporate/Business',
	'qualified plan': 'Qualified Plan',
};

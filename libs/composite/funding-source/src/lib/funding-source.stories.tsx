import { Story, Meta } from '@storybook/react';
import FundingSource, { FundingSourceProps } from './funding-source';
import { defaultProps } from './metadata';
import { useFormContext } from 'react-hook-form';
import Button, { HTMLButtonTypes } from '@atomic/button';
import Form, { IFormMode } from '@composite/form';
import { joiResolver } from '@hookform/resolvers/joi';
import Joi from 'joi';

export default {
	component: FundingSource,
	title: 'Composite/FundingSource',
	argTypes: {
		id: { type: 'number' },
	},
} as Meta;

const createRows = (range) => {
	const rows = [];
	for (let i = 0; i < range; i++) {
		rows.push({
			id: i?.toString(),
		});
	}
	return rows;
};

const Template: Story<FundingSourceProps & HTMLButtonElement> = (args) => (
	<FundingSource
		{...args}
		//rowData={rowData}
		sourceTypes={sourceTypes}
	></FundingSource>
);

export const FundingSourceStory = Template.bind({});
FundingSourceStory.args = {
	...defaultProps,
};
const sourceTypes = [
	{
		id: '09efbe60-4381-11ef-a494-33a21d88a864',
		fundingMethod: 'ACH',
		//icon: 'private/ACH.jpg',
		Description: '',
		'Sort order': 1,
	},
	{
		id: '09effb46-4381-11ef-a494-872210de6273',
		fundingMethod: 'Wire Transfer',
		//icon: 'private/Wire Transfer.jpg',
		Description: '',
		'Sort order': 2,
	},
	{
		id: '09f00316-4381-11ef-a494-c3b57eaaefc2',
		fundingMethod: 'Check',
		//icon: 'private/Cheque.jpg',
		Description: '',
		'Sort order': 3,
	},
	{
		id: '3f33f91e-9102-11ef-9d10-03be3e84d07f',
		fundingMethod: 'External Transfer - ACAT',
		icon: '',
		Description: '',
		'Sort order': 4,
	},
	{
		id: '46f7f056-9102-11ef-9d10-f3eb37d1927c',
		fundingMethod: 'External Transfer - Non-ACAT',
		icon: '',
		Description: '',
		'Sort order': 5,
	},
	{
		id: '4dedc8c2-9102-11ef-9d10-3f4686ebe3c2',
		fundingMethod: 'Internal Transfer',
		icon: '',
		Description: '',
		'Sort order': 6,
	},
];
const countryList = [
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'US',
		code3Letters: 'USA',
		fullName: 'United States of America (The)',
		id: '053cf8d8-4381-11ef-a494-b35699252f74',
		shortName: 'United States of America',
		sortOrder: 1,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CA',
		code3Letters: 'CAN',
		fullName: 'Canada',
		id: '053db4bc-4381-11ef-a494-1740a7b98af7',
		shortName: 'Canada',
		sortOrder: 2,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AF',
		code3Letters: 'AFG',
		fullName: '',
		id: 'shhshsh',
		shortName: '',
		sortOrder: 3,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AL',
		code3Letters: 'ALB',
		fullName: 'Albania',
		id: '053dc682-4381-11ef-a494-1b04dd23517d',
		shortName: 'Albania',
		sortOrder: 4,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'DZ',
		code3Letters: 'DZA',
		fullName: 'Algeria',
		id: '053dce20-4381-11ef-a494-67f82912ab3f',
		shortName: 'Algeria',
		sortOrder: 5,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AS',
		code3Letters: 'ASM',
		fullName: 'American Samoa',
		id: '053dd528-4381-11ef-a494-b3d2929d5689',
		shortName: 'American Samoa',
		sortOrder: 6,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AD',
		code3Letters: 'AND',
		fullName: 'Andorra',
		id: '053ddc4e-4381-11ef-a494-43d0bc7ba5ae',
		shortName: 'Andorra',
		sortOrder: 7,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AO',
		code3Letters: 'AGO',
		fullName: 'Angola',
		id: '053de2f2-4381-11ef-a494-23ea5ef0a8ac',
		shortName: 'Angola',
		sortOrder: 8,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AI',
		code3Letters: 'AIA',
		fullName: 'Anguilla',
		id: '053de98c-4381-11ef-a494-cbb0b5bdd641',
		shortName: 'Anguilla',
		sortOrder: 9,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AQ',
		code3Letters: 'ATA',
		fullName: 'Antarctica',
		id: '053df08a-4381-11ef-a494-a75bfcd98a4d',
		shortName: 'Antarctica',
		sortOrder: 10,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AG',
		code3Letters: 'ATG',
		fullName: 'Antigua and Barbuda',
		id: '053df79c-4381-11ef-a494-639e275a1e27',
		shortName: 'Antigua and Barbuda',
		sortOrder: 11,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AR',
		code3Letters: 'ARG',
		fullName: 'Argentina',
		id: '053dfed6-4381-11ef-a494-53418fcac8c3',
		shortName: 'Argentina',
		sortOrder: 12,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AM',
		code3Letters: 'ARM',
		fullName: 'Armenia',
		id: '053e0606-4381-11ef-a494-97daff363c34',
		shortName: 'Armenia',
		sortOrder: 13,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AW',
		code3Letters: 'ABW',
		fullName: 'Aruba',
		id: '053e0d36-4381-11ef-a494-a76deeaa5432',
		shortName: 'Aruba',
		sortOrder: 14,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AU',
		code3Letters: 'AUS',
		fullName: 'Australia',
		id: '053e13ee-4381-11ef-a494-bb7498f773c2',
		shortName: 'Australia',
		sortOrder: 15,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AT',
		code3Letters: 'AUT',
		fullName: 'Austria',
		id: '053e1a92-4381-11ef-a494-fbdd6b8a3035',
		shortName: 'Austria',
		sortOrder: 16,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AZ',
		code3Letters: 'AZE',
		fullName: 'Azerbaijan',
		id: '053e2186-4381-11ef-a494-471b7464d445',
		shortName: 'Azerbaijan',
		sortOrder: 17,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BS',
		code3Letters: 'BHS',
		fullName: 'Bahamas (The)',
		id: '053e2c58-4381-11ef-a494-03474534dbbd',
		shortName: 'Bahamas',
		sortOrder: 18,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BH',
		code3Letters: 'BHR',
		fullName: 'Bahrain',
		id: '053e33b0-4381-11ef-a494-e37df0d39bcc',
		shortName: 'Bahrain',
		sortOrder: 19,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BD',
		code3Letters: 'BGD',
		fullName: 'Bangladesh',
		id: '053e3bf8-4381-11ef-a494-53ba3a54900d',
		shortName: 'Bangladesh',
		sortOrder: 20,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BB',
		code3Letters: 'BRB',
		fullName: 'Barbados',
		id: '053e438c-4381-11ef-a494-5fe3d96d4ca4',
		shortName: 'Barbados',
		sortOrder: 21,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BY',
		code3Letters: 'BLR',
		fullName: 'Belarus',
		id: '053e4b20-4381-11ef-a494-1bbe615d6199',
		shortName: 'Belarus',
		sortOrder: 22,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BE',
		code3Letters: 'BEL',
		fullName: 'Belgium',
		id: '053e517e-4381-11ef-a494-b31b941b89df',
		shortName: 'Belgium',
		sortOrder: 23,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BZ',
		code3Letters: 'BLZ',
		fullName: 'Belize',
		id: '053e5890-4381-11ef-a494-5374c55a0b77',
		shortName: 'Belize',
		sortOrder: 24,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BJ',
		code3Letters: 'BEN',
		fullName: 'Benin',
		id: '053e5f52-4381-11ef-a494-e3464539c236',
		shortName: 'Benin',
		sortOrder: 25,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BM',
		code3Letters: 'BMU',
		fullName: 'Bermuda',
		id: '053e65ec-4381-11ef-a494-5b5b9a5fff4d',
		shortName: 'Bermuda',
		sortOrder: 26,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BT',
		code3Letters: 'BTN',
		fullName: 'Bhutan',
		id: '053e6d58-4381-11ef-a494-e7c8d95cb8f5',
		shortName: 'Bhutan',
		sortOrder: 27,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BO',
		code3Letters: 'BOL',
		fullName: 'Bolivia (Plurinational State of)',
		id: '053e746a-4381-11ef-a494-17c372c74ac2',
		shortName: 'Bolivia',
		sortOrder: 28,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BQ',
		code3Letters: 'BES',
		fullName: 'Bonaire, Sint Eustatius and Saba',
		id: '053e7b90-4381-11ef-a494-575fed3ec042',
		shortName: 'Bonaire, Sint Eustatius and Saba',
		sortOrder: 29,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BA',
		code3Letters: 'BIH',
		fullName: 'Bosnia and Herzegovina',
		id: '053e831a-4381-11ef-a494-87a03413aca4',
		shortName: 'Bosnia and Herzegovina',
		sortOrder: 30,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BW',
		code3Letters: 'BWA',
		fullName: 'Botswana',
		id: '053e8ad6-4381-11ef-a494-0f11c45698b0',
		shortName: 'Botswana',
		sortOrder: 31,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BV',
		code3Letters: 'BVT',
		fullName: 'Bouvet Island',
		id: '053e91fc-4381-11ef-a494-43a26e96c27d',
		shortName: 'Bouvet Island',
		sortOrder: 32,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BR',
		code3Letters: 'BRA',
		fullName: 'Brazil',
		id: '053e98e6-4381-11ef-a494-f78cd971d89b',
		shortName: 'Brazil',
		sortOrder: 33,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'IO',
		code3Letters: 'IOT',
		fullName: 'British Indian Ocean Territory (The)',
		id: '053e9fbc-4381-11ef-a494-9f7ab6d29733',
		shortName: 'British Indian Ocean Territory',
		sortOrder: 34,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BN',
		code3Letters: 'BRN',
		fullName: 'Brunei Darussalam',
		id: '053ea5d4-4381-11ef-a494-13131284b4a4',
		shortName: 'Brunei Darussalam',
		sortOrder: 35,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BG',
		code3Letters: 'BGR',
		fullName: 'Bulgaria',
		id: '053ead7c-4381-11ef-a494-dfcf50abb19b',
		shortName: 'Bulgaria',
		sortOrder: 36,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BF',
		code3Letters: 'BFA',
		fullName: 'Burkina Faso',
		id: '053eb4ca-4381-11ef-a494-bfe40d0e5342',
		shortName: 'Burkina Faso',
		sortOrder: 37,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BI',
		code3Letters: 'BDI',
		fullName: 'Burundi',
		id: '053ebcc2-4381-11ef-a494-fb707dc15aee',
		shortName: 'Burundi',
		sortOrder: 38,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CV',
		code3Letters: 'CPV',
		fullName: 'Cabo Verde',
		id: '053ec438-4381-11ef-a494-23a56b304f0d',
		shortName: 'Cabo Verde',
		sortOrder: 39,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KH',
		code3Letters: 'KHM',
		fullName: 'Cambodia',
		id: '053ecbcc-4381-11ef-a494-e3a0f802a2bb',
		shortName: 'Cambodia',
		sortOrder: 40,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CM',
		code3Letters: 'CMR',
		fullName: 'Cameroon',
		id: '053ed32e-4381-11ef-a494-5f73ebae5db2',
		shortName: 'Cameroon',
		sortOrder: 41,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KY',
		code3Letters: 'CYM',
		fullName: 'Cayman Islands (The)',
		id: '053eda5e-4381-11ef-a494-5f31bb89b2d8',
		shortName: 'Cayman Islands',
		sortOrder: 42,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CF',
		code3Letters: 'CAF',
		fullName: 'Central African Republic (The)',
		id: '053ee09e-4381-11ef-a494-6b6121b4ec4f',
		shortName: 'Central African Republic',
		sortOrder: 43,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TD',
		code3Letters: 'TCD',
		fullName: 'Chad',
		id: '053ee77e-4381-11ef-a494-378a1d7ccdcc',
		shortName: 'Chad',
		sortOrder: 44,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CL',
		code3Letters: 'CHL',
		fullName: 'Chile',
		id: '053eedc8-4381-11ef-a494-3742cb597c10',
		shortName: 'Chile',
		sortOrder: 45,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CN',
		code3Letters: 'CHN',
		fullName: 'China',
		id: '053ef50c-4381-11ef-a494-1f41bf18c5d0',
		shortName: 'China',
		sortOrder: 46,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CX',
		code3Letters: 'CXR',
		fullName: 'Christmas Island',
		id: '053efc8c-4381-11ef-a494-9320c86ecdb5',
		shortName: 'Christmas Island',
		sortOrder: 47,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CC',
		code3Letters: 'CCK',
		fullName: 'Cocos (Keeling) Islands (The)',
		id: '053f0362-4381-11ef-a494-97448ac6329e',
		shortName: 'Cocos Islands (The)',
		sortOrder: 48,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CO',
		code3Letters: 'COL',
		fullName: 'Colombia',
		id: '053f0a10-4381-11ef-a494-7f4390736842',
		shortName: 'Colombia',
		sortOrder: 49,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KM',
		code3Letters: 'COM',
		fullName: 'Comoros (The)',
		id: '053f1190-4381-11ef-a494-c7c9d0a614ef',
		shortName: 'Comoros',
		sortOrder: 50,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CD',
		code3Letters: 'COD',
		fullName: 'Congo (The Democratic Republic of The)',
		id: '053f18fc-4381-11ef-a494-6b65e4af950f',
		shortName: 'Congo (Democratic Republic)',
		sortOrder: 51,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CG',
		code3Letters: 'COG',
		fullName: 'Congo (The)',
		id: '053f2018-4381-11ef-a494-8bd2cfb6a6bf',
		shortName: 'Congo',
		sortOrder: 52,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CK',
		code3Letters: 'COK',
		fullName: 'Cook Islands (The)',
		id: '053f2734-4381-11ef-a494-5b0c7b475ef2',
		shortName: 'Cook Islands',
		sortOrder: 53,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CR',
		code3Letters: 'CRI',
		fullName: 'Costa Rica',
		id: '053f2da6-4381-11ef-a494-fb19a53d397c',
		shortName: 'Costa Rica',
		sortOrder: 54,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'HR',
		code3Letters: 'HRV',
		fullName: 'Croatia',
		id: '053f34b8-4381-11ef-a494-1f09dd66c576',
		shortName: 'Croatia',
		sortOrder: 55,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CU',
		code3Letters: 'CUB',
		fullName: 'Cuba',
		id: '053f3c6a-4381-11ef-a494-63033b20fcd5',
		shortName: 'Cuba',
		sortOrder: 56,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CW',
		code3Letters: 'CUW',
		fullName: 'Curaçao',
		id: '053f44d0-4381-11ef-a494-9798ba367cfe',
		shortName: 'Curaçao',
		sortOrder: 57,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CY',
		code3Letters: 'CYP',
		fullName: 'Cyprus',
		id: '053f4c3c-4381-11ef-a494-e35b522a9146',
		shortName: 'Cyprus',
		sortOrder: 58,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CZ',
		code3Letters: 'CZE',
		fullName: 'Czechia',
		id: '053f54ca-4381-11ef-a494-830a4db5d9a7',
		shortName: 'Czechia',
		sortOrder: 59,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CI',
		code3Letters: 'CIV',
		fullName: "Côte d'Ivoire",
		id: '053f5c18-4381-11ef-a494-3f08251b8738',
		shortName: "Côte d'Ivoire",
		sortOrder: 60,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'DK',
		code3Letters: 'DNK',
		fullName: 'Denmark',
		id: '053f6320-4381-11ef-a494-47c69ae0a31e',
		shortName: 'Denmark',
		sortOrder: 61,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'DJ',
		code3Letters: 'DJI',
		fullName: 'Djibouti',
		id: '053f69d8-4381-11ef-a494-eb9b624fd89e',
		shortName: 'Djibouti',
		sortOrder: 62,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'DM',
		code3Letters: 'DMA',
		fullName: 'Dominica',
		id: '053f6fe6-4381-11ef-a494-9f93e1c5238b',
		shortName: 'Dominica',
		sortOrder: 63,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'DO',
		code3Letters: 'DOM',
		fullName: 'Dominican Republic (The)',
		id: '053f75fe-4381-11ef-a494-57343f79c4e2',
		shortName: 'Dominican Republic',
		sortOrder: 64,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'EC',
		code3Letters: 'ECU',
		fullName: 'Ecuador',
		id: '053f7be4-4381-11ef-a494-7fe6f92dfb13',
		shortName: 'Ecuador',
		sortOrder: 65,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'EG',
		code3Letters: 'EGY',
		fullName: 'Egypt',
		id: '053f8288-4381-11ef-a494-a7bc61a40edb',
		shortName: 'Egypt',
		sortOrder: 66,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SV',
		code3Letters: 'SLV',
		fullName: 'El Salvador',
		id: '053f88aa-4381-11ef-a494-1fbd652633d3',
		shortName: 'El Salvador',
		sortOrder: 67,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GQ',
		code3Letters: 'GNQ',
		fullName: 'Equatorial Guinea',
		id: '053f8f58-4381-11ef-a494-036124acb44f',
		shortName: 'Equatorial Guinea',
		sortOrder: 68,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ER',
		code3Letters: 'ERI',
		fullName: 'Eritrea',
		id: '053f9566-4381-11ef-a494-d774c8348e39',
		shortName: 'Eritrea',
		sortOrder: 69,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'EE',
		code3Letters: 'EST',
		fullName: 'Estonia',
		id: '053f9bba-4381-11ef-a494-53e878829df0',
		shortName: 'Estonia',
		sortOrder: 70,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SZ',
		code3Letters: 'SWZ',
		fullName: 'Eswatini',
		id: '053fa1a0-4381-11ef-a494-fbc349bfc205',
		shortName: 'Eswatini',
		sortOrder: 71,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ET',
		code3Letters: 'ETH',
		fullName: 'Ethiopia',
		id: '053fa77c-4381-11ef-a494-c3a2ff5c7da6',
		shortName: 'Ethiopia',
		sortOrder: 72,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'FK',
		code3Letters: 'FLK',
		fullName: 'Falkland Islands (The) [Malvinas]',
		id: '053fad6c-4381-11ef-a494-6fee4d8de7b2',
		shortName: 'Falkland Islands',
		sortOrder: 73,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'FO',
		code3Letters: 'FRO',
		fullName: 'Faroe Islands (The)',
		id: '053fb334-4381-11ef-a494-772470a5f233',
		shortName: 'Faroe Islands',
		sortOrder: 74,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'FJ',
		code3Letters: 'FJI',
		fullName: 'Fiji',
		id: '053fb92e-4381-11ef-a494-0774b250d1b1',
		shortName: 'Fiji',
		sortOrder: 75,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'FI',
		code3Letters: 'FIN',
		fullName: 'Finland',
		id: '053fc11c-4381-11ef-a494-3feb4965e48f',
		shortName: 'Finland',
		sortOrder: 76,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'FR',
		code3Letters: 'FRA',
		fullName: 'France',
		id: '053fc7c0-4381-11ef-a494-3bef387b5ae6',
		shortName: 'France',
		sortOrder: 77,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GF',
		code3Letters: 'GUF',
		fullName: 'French Guiana',
		id: '053fce50-4381-11ef-a494-5b5ff741ec08',
		shortName: 'French Guiana',
		sortOrder: 78,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PF',
		code3Letters: 'PYF',
		fullName: 'French Polynesia',
		id: '053fd4c2-4381-11ef-a494-6f36edf40271',
		shortName: 'French Polynesia',
		sortOrder: 79,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TF',
		code3Letters: 'ATF',
		fullName: 'French Southern Territories (The)',
		id: '053fdac6-4381-11ef-a494-4fd2f467d6ee',
		shortName: 'French Southern Territories',
		sortOrder: 80,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GA',
		code3Letters: 'GAB',
		fullName: 'Gabon',
		id: '053fe0c0-4381-11ef-a494-7b75d01bc6af',
		shortName: 'Gabon',
		sortOrder: 81,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GM',
		code3Letters: 'GMB',
		fullName: 'Gambia (The)',
		id: '053fe6c4-4381-11ef-a494-f726498cbda7',
		shortName: 'Gambia',
		sortOrder: 82,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GE',
		code3Letters: 'GEO',
		fullName: 'Georgia',
		id: '053fecb4-4381-11ef-a494-a37d2c9cf6d1',
		shortName: 'Georgia',
		sortOrder: 83,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'DE',
		code3Letters: 'DEU',
		fullName: 'Germany',
		id: '053ff420-4381-11ef-a494-afbb178d0c6e',
		shortName: 'Germany',
		sortOrder: 84,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GH',
		code3Letters: 'GHA',
		fullName: 'Ghana',
		id: '053ffa74-4381-11ef-a494-733089ff8e1c',
		shortName: 'Ghana',
		sortOrder: 85,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GI',
		code3Letters: 'GIB',
		fullName: 'Gibraltar',
		id: '05400096-4381-11ef-a494-470f370e053a',
		shortName: 'Gibraltar',
		sortOrder: 86,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GR',
		code3Letters: 'GRC',
		fullName: 'Greece',
		id: '05400690-4381-11ef-a494-738b4856b216',
		shortName: 'Greece',
		sortOrder: 87,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GL',
		code3Letters: 'GRL',
		fullName: 'Greenland',
		id: '05400c76-4381-11ef-a494-b3f476b1c3dd',
		shortName: 'Greenland',
		sortOrder: 88,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GD',
		code3Letters: 'GRD',
		fullName: 'Grenada',
		id: '05401234-4381-11ef-a494-07b0b18bc8ef',
		shortName: 'Grenada',
		sortOrder: 89,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GP',
		code3Letters: 'GLP',
		fullName: 'Guadeloupe',
		id: '054017fc-4381-11ef-a494-5f6202990b6d',
		shortName: 'Guadeloupe',
		sortOrder: 90,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GU',
		code3Letters: 'GUM',
		fullName: 'Guam',
		id: '05401dce-4381-11ef-a494-fba1f3de194d',
		shortName: 'Guam',
		sortOrder: 91,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GT',
		code3Letters: 'GTM',
		fullName: 'Guatemala',
		id: '05402422-4381-11ef-a494-e79b2045ce96',
		shortName: 'Guatemala',
		sortOrder: 92,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GG',
		code3Letters: 'GGY',
		fullName: 'Guernsey',
		id: '05402a76-4381-11ef-a494-4fbdcc8b5334',
		shortName: 'Guernsey',
		sortOrder: 93,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GN',
		code3Letters: 'GIN',
		fullName: 'Guinea',
		id: '05403070-4381-11ef-a494-33207b44e621',
		shortName: 'Guinea',
		sortOrder: 94,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GW',
		code3Letters: 'GNB',
		fullName: 'Guinea-Bissau',
		id: '05403656-4381-11ef-a494-171138f0b368',
		shortName: 'Guinea-Bissau',
		sortOrder: 95,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GY',
		code3Letters: 'GUY',
		fullName: 'Guyana',
		id: '054048ee-4381-11ef-a494-2bc3c118ec47',
		shortName: 'Guyana',
		sortOrder: 96,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'HT',
		code3Letters: 'HTI',
		fullName: 'Haiti',
		id: '0540501e-4381-11ef-a494-03d648808f3e',
		shortName: 'Haiti',
		sortOrder: 97,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'HM',
		code3Letters: 'HMD',
		fullName: 'Heard Island and McDonald Islands',
		id: '054056fe-4381-11ef-a494-e7ed99a93559',
		shortName: 'Heard Island and McDonald Islands',
		sortOrder: 98,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'VA',
		code3Letters: 'VAT',
		fullName: 'Holy See (The)',
		id: '05405d0c-4381-11ef-a494-77b68f2d1a62',
		shortName: 'Holy See',
		sortOrder: 99,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'HN',
		code3Letters: 'HND',
		fullName: 'Honduras',
		id: '054062e8-4381-11ef-a494-6f09c2c3c6f0',
		shortName: 'Honduras',
		sortOrder: 100,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'HK',
		code3Letters: 'HKG',
		fullName: 'Hong Kong',
		id: '054068b0-4381-11ef-a494-4f9e9a4519ea',
		shortName: 'Hong Kong',
		sortOrder: 101,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'HU',
		code3Letters: 'HUN',
		fullName: 'Hungary',
		id: '05406ebe-4381-11ef-a494-871ddc235579',
		shortName: 'Hungary',
		sortOrder: 102,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'IS',
		code3Letters: 'ISL',
		fullName: 'Iceland',
		id: '05407558-4381-11ef-a494-c3d467993c22',
		shortName: 'Iceland',
		sortOrder: 103,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'IN',
		code3Letters: 'IND',
		fullName: 'India',
		id: 'e973b418-8479-11ef-8037-e311ebcf8d07',
		shortName: 'India',
		sortOrder: 104,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ID',
		code3Letters: 'IDN',
		fullName: 'Indonesia',
		id: '05408174-4381-11ef-a494-4377421b2e36',
		shortName: 'Indonesia',
		sortOrder: 105,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'IR',
		code3Letters: 'IRN',
		fullName: 'Iran (Islamic Republic of)',
		id: '05408746-4381-11ef-a494-63fdf36a5f4e',
		shortName: 'Iran',
		sortOrder: 106,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'IQ',
		code3Letters: 'IRQ',
		fullName: 'Iraq',
		id: '05408d04-4381-11ef-a494-af7e033a0f2b',
		shortName: 'Iraq',
		sortOrder: 107,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'IE',
		code3Letters: 'IRL',
		fullName: 'Ireland',
		id: '0540936c-4381-11ef-a494-bf527d32e5da',
		shortName: 'Ireland',
		sortOrder: 108,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'IM',
		code3Letters: 'IMN',
		fullName: 'Isle of Man',
		id: '0540993e-4381-11ef-a494-db7b4324c97a',
		shortName: 'Isle of Man',
		sortOrder: 109,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'IL',
		code3Letters: 'ISR',
		fullName: 'Israel',
		id: '05409ff6-4381-11ef-a494-27568a9ce840',
		shortName: 'Israel',
		sortOrder: 110,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'IT',
		code3Letters: 'ITA',
		fullName: 'Italy',
		id: '0540ab90-4381-11ef-a494-cb0839b5c80a',
		shortName: 'Italy',
		sortOrder: 111,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'JM',
		code3Letters: 'JAM',
		fullName: 'Jamaica',
		id: '0540b5b8-4381-11ef-a494-efb60b3d3880',
		shortName: 'Jamaica',
		sortOrder: 112,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'JP',
		code3Letters: 'JPN',
		fullName: 'Japan',
		id: '0540bde2-4381-11ef-a494-a3da8288a0b3',
		shortName: 'Japan',
		sortOrder: 113,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'JE',
		code3Letters: 'JEY',
		fullName: 'Jersey',
		id: '0540c3b4-4381-11ef-a494-e7560097f19c',
		shortName: 'Jersey',
		sortOrder: 114,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'JO',
		code3Letters: 'JOR',
		fullName: 'Jordan',
		id: '0540ca44-4381-11ef-a494-9b9d7ab78914',
		shortName: 'Jordan',
		sortOrder: 115,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KZ',
		code3Letters: 'KAZ',
		fullName: 'Kazakhstan',
		id: '0540d066-4381-11ef-a494-6bea9c3f6dca',
		shortName: 'Kazakhstan',
		sortOrder: 116,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KE',
		code3Letters: 'KEN',
		fullName: 'Kenya',
		id: '0540d656-4381-11ef-a494-4b1048e0bc75',
		shortName: 'Kenya',
		sortOrder: 117,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KI',
		code3Letters: 'KIR',
		fullName: 'Kiribati',
		id: '0540dc3c-4381-11ef-a494-c32d36f56462',
		shortName: 'Kiribati',
		sortOrder: 118,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KP',
		code3Letters: 'PRK',
		fullName: "Korea (The Democratic People's Republic of)",
		id: '0540e218-4381-11ef-a494-673438fa2430',
		shortName: 'North Korea',
		sortOrder: 119,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KR',
		code3Letters: 'KOR',
		fullName: 'Korea (The Republic of)',
		id: '0540e826-4381-11ef-a494-a306fd06fdae',
		shortName: 'South Korea',
		sortOrder: 120,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KW',
		code3Letters: 'KWT',
		fullName: 'Kuwait',
		id: '0540ee16-4381-11ef-a494-f34848576abe',
		shortName: 'Kuwait',
		sortOrder: 121,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KG',
		code3Letters: 'KGZ',
		fullName: 'Kyrgyzstan',
		id: '0540f3fc-4381-11ef-a494-272a4aa42995',
		shortName: 'Kyrgyzstan',
		sortOrder: 122,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LA',
		code3Letters: 'LAO',
		fullName: "Lao People's Democratic Republic (The)",
		id: '0540f9d8-4381-11ef-a494-a7619e228354',
		shortName: "Lao People's Democratic Republic",
		sortOrder: 123,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LV',
		code3Letters: 'LVA',
		fullName: 'Latvia',
		id: '0540ffaa-4381-11ef-a494-337383b56ee1',
		shortName: 'Latvia',
		sortOrder: 124,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LB',
		code3Letters: 'LBN',
		fullName: 'Lebanon',
		id: '05410590-4381-11ef-a494-e37a3c1016b0',
		shortName: 'Lebanon',
		sortOrder: 125,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LS',
		code3Letters: 'LSO',
		fullName: 'Lesotho',
		id: '05410b80-4381-11ef-a494-bbe97f20f7dc',
		shortName: 'Lesotho',
		sortOrder: 126,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LR',
		code3Letters: 'LBR',
		fullName: 'Liberia',
		id: '0541115c-4381-11ef-a494-230858cbc33f',
		shortName: 'Liberia',
		sortOrder: 127,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LY',
		code3Letters: 'LBY',
		fullName: 'Libya',
		id: '0541185a-4381-11ef-a494-ab9b907bd4cf',
		shortName: 'Libya',
		sortOrder: 128,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LI',
		code3Letters: 'LIE',
		fullName: 'Liechtenstein',
		id: '05411eae-4381-11ef-a494-437e4ed05c27',
		shortName: 'Liechtenstein',
		sortOrder: 129,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LT',
		code3Letters: 'LTU',
		fullName: 'Lithuania',
		id: '05412494-4381-11ef-a494-4b2d3779f1bc',
		shortName: 'Lithuania',
		sortOrder: 130,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LU',
		code3Letters: 'LUX',
		fullName: 'Luxembourg',
		id: '05412a7a-4381-11ef-a494-634d5249eec3',
		shortName: 'Luxembourg',
		sortOrder: 131,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MO',
		code3Letters: 'MAC',
		fullName: 'Macao',
		id: '054132cc-4381-11ef-a494-e32372fb59c6',
		shortName: 'Macao',
		sortOrder: 132,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MG',
		code3Letters: 'MDG',
		fullName: 'Madagascar',
		id: '054138da-4381-11ef-a494-9b16a4c0841d',
		shortName: 'Madagascar',
		sortOrder: 133,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MW',
		code3Letters: 'MWI',
		fullName: 'Malawi',
		id: '05413eac-4381-11ef-a494-63040fe34729',
		shortName: 'Malawi',
		sortOrder: 134,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MY',
		code3Letters: 'MYS',
		fullName: 'Malaysia',
		id: '054144a6-4381-11ef-a494-cba811eb20d1',
		shortName: 'Malaysia',
		sortOrder: 135,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MV',
		code3Letters: 'MDV',
		fullName: 'Maldives',
		id: '05414bf4-4381-11ef-a494-1f7883935d1d',
		shortName: 'Maldives',
		sortOrder: 136,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ML',
		code3Letters: 'MLI',
		fullName: 'Mali',
		id: '05415234-4381-11ef-a494-bb884362fef3',
		shortName: 'Mali',
		sortOrder: 137,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MT',
		code3Letters: 'MLT',
		fullName: 'Malta',
		id: '0541581a-4381-11ef-a494-a724f33c43cb',
		shortName: 'Malta',
		sortOrder: 138,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MH',
		code3Letters: 'MHL',
		fullName: 'Marshall Islands (The)',
		id: '05415de2-4381-11ef-a494-f387c43792db',
		shortName: 'Marshall Islands',
		sortOrder: 139,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MQ',
		code3Letters: 'MTQ',
		fullName: 'Martinique',
		id: '05416440-4381-11ef-a494-6b1637b53a5a',
		shortName: 'Martinique',
		sortOrder: 140,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MR',
		code3Letters: 'MRT',
		fullName: 'Mauritania',
		id: '05416a1c-4381-11ef-a494-7f42148c48f2',
		shortName: 'Mauritania',
		sortOrder: 141,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MU',
		code3Letters: 'MUS',
		fullName: 'Mauritius',
		id: '05417020-4381-11ef-a494-b7fcc67bed38',
		shortName: 'Mauritius',
		sortOrder: 142,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'YT',
		code3Letters: 'MYT',
		fullName: 'Mayotte',
		id: '05417624-4381-11ef-a494-47843b7d69e6',
		shortName: 'Mayotte',
		sortOrder: 143,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MX',
		code3Letters: 'MEX',
		fullName: 'Mexico',
		id: '05417c0a-4381-11ef-a494-03c9a470221f',
		shortName: 'Mexico',
		sortOrder: 144,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'FM',
		code3Letters: 'FSM',
		fullName: 'Micronesia (Federated States of)',
		id: '054181e6-4381-11ef-a494-9be03a580f14',
		shortName: 'Micronesia',
		sortOrder: 145,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MD',
		code3Letters: 'MDA',
		fullName: 'Moldova (The Republic of)',
		id: '054187ea-4381-11ef-a494-2b71ddd324a2',
		shortName: 'Moldova',
		sortOrder: 146,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MC',
		code3Letters: 'MCO',
		fullName: 'Monaco',
		id: '05418dbc-4381-11ef-a494-ef35fb3401e8',
		shortName: 'Monaco',
		sortOrder: 147,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MN',
		code3Letters: 'MNG',
		fullName: 'Mongolia',
		id: '054193ac-4381-11ef-a494-773d3046cf1b',
		shortName: 'Mongolia',
		sortOrder: 148,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ME',
		code3Letters: 'MNE',
		fullName: 'Montenegro',
		id: '05419988-4381-11ef-a494-0ff6c11ec6b6',
		shortName: 'Montenegro',
		sortOrder: 149,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MS',
		code3Letters: 'MSR',
		fullName: 'Montserrat',
		id: '05419f78-4381-11ef-a494-c37c7202e6b1',
		shortName: 'Montserrat',
		sortOrder: 150,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MA',
		code3Letters: 'MAR',
		fullName: 'Morocco',
		id: '0541a540-4381-11ef-a494-9b1cc2148762',
		shortName: 'Morocco',
		sortOrder: 151,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MZ',
		code3Letters: 'MOZ',
		fullName: 'Mozambique',
		id: '0541ab1c-4381-11ef-a494-a71703e273d5',
		shortName: 'Mozambique',
		sortOrder: 152,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MM',
		code3Letters: 'MMR',
		fullName: 'Myanmar',
		id: '0541b0f8-4381-11ef-a494-7fe9359e0e0e',
		shortName: 'Myanmar',
		sortOrder: 153,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NA',
		code3Letters: 'NAM',
		fullName: 'Namibia',
		id: '0541b6f2-4381-11ef-a494-676ed23b627c',
		shortName: 'Namibia',
		sortOrder: 154,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NR',
		code3Letters: 'NRU',
		fullName: 'Nauru',
		id: '0541bd0a-4381-11ef-a494-272a1790c763',
		shortName: 'Nauru',
		sortOrder: 155,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NP',
		code3Letters: 'NPL',
		fullName: 'Nepal',
		id: '0541c354-4381-11ef-a494-5720f5b29419',
		shortName: 'Nepal',
		sortOrder: 156,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NL',
		code3Letters: 'NLD',
		fullName: 'Netherlands (Kingdom of The)',
		id: '0541cad4-4381-11ef-a494-83489ec313aa',
		shortName: 'Netherlands',
		sortOrder: 157,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NC',
		code3Letters: 'NCL',
		fullName: 'New Caledonia',
		id: '0541d1d2-4381-11ef-a494-2f825e39db1a',
		shortName: 'New Caledonia',
		sortOrder: 158,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NZ',
		code3Letters: 'NZL',
		fullName: 'New Zealand',
		id: '0541d894-4381-11ef-a494-53bc62879c1d',
		shortName: 'New Zealand',
		sortOrder: 159,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NI',
		code3Letters: 'NIC',
		fullName: 'Nicaragua',
		id: '0541df88-4381-11ef-a494-5b4f4f3d3492',
		shortName: 'Nicaragua',
		sortOrder: 160,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NE',
		code3Letters: 'NER',
		fullName: 'Niger (The)',
		id: '0541e708-4381-11ef-a494-831c04aab902',
		shortName: 'Niger',
		sortOrder: 161,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NG',
		code3Letters: 'NGA',
		fullName: 'Nigeria',
		id: '0541edca-4381-11ef-a494-e3151e25252c',
		shortName: 'Nigeria',
		sortOrder: 162,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NU',
		code3Letters: 'NIU',
		fullName: 'Niue',
		id: '0541f43c-4381-11ef-a494-93770fc165a6',
		shortName: 'Niue',
		sortOrder: 163,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NF',
		code3Letters: 'NFK',
		fullName: 'Norfolk Island',
		id: '0541fac2-4381-11ef-a494-0f2c7049dc27',
		shortName: 'Norfolk Island',
		sortOrder: 164,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MK',
		code3Letters: 'MKD',
		fullName: 'North Macedonia',
		id: '05420120-4381-11ef-a494-b75f60d843a4',
		shortName: 'North Macedonia',
		sortOrder: 165,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MP',
		code3Letters: 'MNP',
		fullName: 'Northern Mariana Islands (The)',
		id: '05420756-4381-11ef-a494-b34096120139',
		shortName: 'Northern Mariana Islands',
		sortOrder: 166,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'NO',
		code3Letters: 'NOR',
		fullName: 'Norway',
		id: '05420d6e-4381-11ef-a494-8f8569095ed0',
		shortName: 'Norway',
		sortOrder: 167,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'OM',
		code3Letters: 'OMN',
		fullName: 'Oman',
		id: '05421368-4381-11ef-a494-9b2ff95518ea',
		shortName: 'Oman',
		sortOrder: 168,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PK',
		code3Letters: 'PAK',
		fullName: 'Pakistan',
		id: '0542193a-4381-11ef-a494-2f9f013fb0dd',
		shortName: 'Pakistan',
		sortOrder: 169,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PW',
		code3Letters: 'PLW',
		fullName: 'Palau',
		id: '05421f0c-4381-11ef-a494-db63d6e0f892',
		shortName: 'Palau',
		sortOrder: 170,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PS',
		code3Letters: 'PSE',
		fullName: 'Palestine, State of',
		id: '054224de-4381-11ef-a494-8b7f0f9404e2',
		shortName: 'Palestine, State of',
		sortOrder: 171,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PA',
		code3Letters: 'PAN',
		fullName: 'Panama',
		id: '05422bb4-4381-11ef-a494-3fcc412b500e',
		shortName: 'Panama',
		sortOrder: 172,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PG',
		code3Letters: 'PNG',
		fullName: 'Papua New Guinea',
		id: '054231ae-4381-11ef-a494-7b95aa778f9c',
		shortName: 'Papua New Guinea',
		sortOrder: 173,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PY',
		code3Letters: 'PRY',
		fullName: 'Paraguay',
		id: '054237d0-4381-11ef-a494-fbc51beff428',
		shortName: 'Paraguay',
		sortOrder: 174,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PE',
		code3Letters: 'PER',
		fullName: 'Peru',
		id: '05423dac-4381-11ef-a494-5f5c597dff91',
		shortName: 'Peru',
		sortOrder: 175,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PH',
		code3Letters: 'PHL',
		fullName: 'Philippines (The)',
		id: '054243b0-4381-11ef-a494-dbe8bf427b8e',
		shortName: 'Philippines',
		sortOrder: 176,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PN',
		code3Letters: 'PCN',
		fullName: 'Pitcairn',
		id: '05424996-4381-11ef-a494-5330ef21aed8',
		shortName: 'Pitcairn',
		sortOrder: 177,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PL',
		code3Letters: 'POL',
		fullName: 'Poland',
		id: '05424f5e-4381-11ef-a494-334775f28f08',
		shortName: 'Poland',
		sortOrder: 178,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PT',
		code3Letters: 'PRT',
		fullName: 'Portugal',
		id: '05425530-4381-11ef-a494-ff244b3adc25',
		shortName: 'Portugal',
		sortOrder: 179,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PR',
		code3Letters: 'PRI',
		fullName: 'Puerto Rico',
		id: '05425b70-4381-11ef-a494-4362a5d66dba',
		shortName: 'Puerto Rico',
		sortOrder: 180,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'QA',
		code3Letters: 'QAT',
		fullName: 'Qatar',
		id: '05426232-4381-11ef-a494-573d704980cf',
		shortName: 'Qatar',
		sortOrder: 181,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'RO',
		code3Letters: 'ROU',
		fullName: 'Romania',
		id: '0542694e-4381-11ef-a494-27d0ef7ef7f8',
		shortName: 'Romania',
		sortOrder: 182,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'RU',
		code3Letters: 'RUS',
		fullName: 'Russian Federation (The)',
		id: '05426fb6-4381-11ef-a494-17f238c20ebe',
		shortName: 'Russian Federation',
		sortOrder: 183,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'RW',
		code3Letters: 'RWA',
		fullName: 'Rwanda',
		id: '054276aa-4381-11ef-a494-a7a577f69a7f',
		shortName: 'Rwanda',
		sortOrder: 184,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'RE',
		code3Letters: 'REU',
		fullName: 'Réunion',
		id: '05427ccc-4381-11ef-a494-6b194fbda9c7',
		shortName: 'Réunion',
		sortOrder: 185,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'BL',
		code3Letters: 'BLM',
		fullName: 'Saint Barthélemy',
		id: '054282d0-4381-11ef-a494-efe44ad95c39',
		shortName: 'Saint Barthélemy',
		sortOrder: 186,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SH',
		code3Letters: 'SHN',
		fullName: 'Saint Helena, Ascension and Tristan da Cunha',
		id: '054288e8-4381-11ef-a494-4fae27ea3386',
		shortName: 'Saint Helena, Ascension and Tristan da Cunha',
		sortOrder: 187,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'KN',
		code3Letters: 'KNA',
		fullName: 'Saint Kitts and Nevis',
		id: '05428ef6-4381-11ef-a494-6ba994d1b30a',
		shortName: 'Saint Kitts and Nevis',
		sortOrder: 188,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LC',
		code3Letters: 'LCA',
		fullName: 'Saint Lucia',
		id: '05429504-4381-11ef-a494-430eb4a07570',
		shortName: 'Saint Lucia',
		sortOrder: 189,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'MF',
		code3Letters: 'MAF',
		fullName: 'Saint Martin (French part)',
		id: '05429b1c-4381-11ef-a494-1b95fe6a9efb',
		shortName: 'Saint Martin (French)',
		sortOrder: 190,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'PM',
		code3Letters: 'SPM',
		fullName: 'Saint Pierre and Miquelon',
		id: '0542a3b4-4381-11ef-a494-e7735d585656',
		shortName: 'Saint Pierre and Miquelon',
		sortOrder: 191,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'VC',
		code3Letters: 'VCT',
		fullName: 'Saint Vincent and The Grenadines',
		id: '0542aa1c-4381-11ef-a494-b3b822012443',
		shortName: 'Saint Vincent and The Grenadines',
		sortOrder: 192,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'WS',
		code3Letters: 'WSM',
		fullName: 'Samoa',
		id: '0542b00c-4381-11ef-a494-a7491a13bb50',
		shortName: 'Samoa',
		sortOrder: 193,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SM',
		code3Letters: 'SMR',
		fullName: 'San Marino',
		id: '0542b5e8-4381-11ef-a494-cfa051ac29b4',
		shortName: 'San Marino',
		sortOrder: 194,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ST',
		code3Letters: 'STP',
		fullName: 'Sao Tome and Principe',
		id: '0542bbd8-4381-11ef-a494-73b407d1f8fe',
		shortName: 'Sao Tome and Principe',
		sortOrder: 195,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SA',
		code3Letters: 'SAU',
		fullName: 'Saudi Arabia',
		id: '0542c196-4381-11ef-a494-5ff41ffdced3',
		shortName: 'Saudi Arabia',
		sortOrder: 196,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SN',
		code3Letters: 'SEN',
		fullName: 'Senegal',
		id: '0542c75e-4381-11ef-a494-dbcd84aa3134',
		shortName: 'Senegal',
		sortOrder: 197,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'RS',
		code3Letters: 'SRB',
		fullName: 'Serbia',
		id: '0542ce66-4381-11ef-a494-4ffe8c3a8057',
		shortName: 'Serbia',
		sortOrder: 198,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SC',
		code3Letters: 'SYC',
		fullName: 'Seychelles',
		id: '0542d4c4-4381-11ef-a494-1f00c3b09ce2',
		shortName: 'Seychelles',
		sortOrder: 199,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SL',
		code3Letters: 'SLE',
		fullName: 'Sierra Leone',
		id: '0542daaa-4381-11ef-a494-c3c1c0965b20',
		shortName: 'Sierra Leone',
		sortOrder: 200,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SG',
		code3Letters: 'SGP',
		fullName: 'Singapore',
		id: '0542e07c-4381-11ef-a494-570b8f755bdd',
		shortName: 'Singapore',
		sortOrder: 201,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SX',
		code3Letters: 'SXM',
		fullName: 'Sint Maarten (Dutch part)',
		id: '0542e63a-4381-11ef-a494-eb72ceed8a1e',
		shortName: 'Sint Maarten (Dutch)',
		sortOrder: 202,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SK',
		code3Letters: 'SVK',
		fullName: 'Slovakia',
		id: '0542ec20-4381-11ef-a494-afce79c96d9a',
		shortName: 'Slovakia',
		sortOrder: 203,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SI',
		code3Letters: 'SVN',
		fullName: 'Slovenia',
		id: '0542f210-4381-11ef-a494-130d4c6708ad',
		shortName: 'Slovenia',
		sortOrder: 204,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SB',
		code3Letters: 'SLB',
		fullName: 'Solomon Islands',
		id: '0542f80a-4381-11ef-a494-f3ff2098d831',
		shortName: 'Solomon Islands',
		sortOrder: 205,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SO',
		code3Letters: 'SOM',
		fullName: 'Somalia',
		id: '0542fe72-4381-11ef-a494-ef454a1d736a',
		shortName: 'Somalia',
		sortOrder: 206,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ZA',
		code3Letters: 'ZAF',
		fullName: 'South Africa',
		id: '0543058e-4381-11ef-a494-93eb8036947a',
		shortName: 'South Africa',
		sortOrder: 207,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GS',
		code3Letters: 'SGS',
		fullName: 'South Georgia and The South Sandwich Islands',
		id: '05430cb4-4381-11ef-a494-9f67f6921d51',
		shortName: 'South Georgia and The South Sandwich Islands',
		sortOrder: 208,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SS',
		code3Letters: 'SSD',
		fullName: 'South Sudan',
		id: '05431344-4381-11ef-a494-e3e0729be8c9',
		shortName: 'South Sudan',
		sortOrder: 209,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ES',
		code3Letters: 'ESP',
		fullName: 'Spain',
		id: '054319e8-4381-11ef-a494-cb5e938e303f',
		shortName: 'Spain',
		sortOrder: 210,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'LK',
		code3Letters: 'LKA',
		fullName: 'Sri Lanka',
		id: '05431fe2-4381-11ef-a494-b7af759ca967',
		shortName: 'Sri Lanka',
		sortOrder: 211,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SD',
		code3Letters: 'SDN',
		fullName: 'Sudan (The)',
		id: '0543280c-4381-11ef-a494-ebb4f726c8c6',
		shortName: 'Sudan',
		sortOrder: 212,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SR',
		code3Letters: 'SUR',
		fullName: 'Suriname',
		id: '05432faa-4381-11ef-a494-fb0cf1728532',
		shortName: 'Suriname',
		sortOrder: 213,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SJ',
		code3Letters: 'SJM',
		fullName: 'Svalbard and Jan Mayen',
		id: '0543366c-4381-11ef-a494-573c3f736b18',
		shortName: 'Svalbard and Jan Mayen',
		sortOrder: 214,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SE',
		code3Letters: 'SWE',
		fullName: 'Sweden',
		id: '05433cfc-4381-11ef-a494-cfb188a30745',
		shortName: 'Sweden',
		sortOrder: 215,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'CH',
		code3Letters: 'CHE',
		fullName: 'Switzerland',
		id: '0543433c-4381-11ef-a494-a36a35fe7f6f',
		shortName: 'Switzerland',
		sortOrder: 216,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'SY',
		code3Letters: 'SYR',
		fullName: 'Syrian Arab Republic (The)',
		id: '05434922-4381-11ef-a494-afe5934bfb19',
		shortName: 'Syrian Arab Republic',
		sortOrder: 217,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TW',
		code3Letters: 'TWN',
		fullName: 'Taiwan (Province of China)',
		id: '05434fc6-4381-11ef-a494-c71cf4f72d55',
		shortName: 'Taiwan',
		sortOrder: 218,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TJ',
		code3Letters: 'TJK',
		fullName: 'Tajikistan',
		id: '0543575a-4381-11ef-a494-2722fc419c4d',
		shortName: 'Tajikistan',
		sortOrder: 219,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TZ',
		code3Letters: 'TZA',
		fullName: 'Tanzania, The United Republic of',
		id: '05435ebc-4381-11ef-a494-f72fae3652f3',
		shortName: 'Tanzania',
		sortOrder: 220,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TH',
		code3Letters: 'THA',
		fullName: 'Thailand',
		id: '05436646-4381-11ef-a494-4fdb6ce6e292',
		shortName: 'Thailand',
		sortOrder: 221,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TL',
		code3Letters: 'TLS',
		fullName: 'Timor-Leste',
		id: '05436ce0-4381-11ef-a494-6fe66455f451',
		shortName: 'Timor-Leste',
		sortOrder: 222,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TG',
		code3Letters: 'TGO',
		fullName: 'Togo',
		id: '05437302-4381-11ef-a494-037840d8fdef',
		shortName: 'Togo',
		sortOrder: 223,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TK',
		code3Letters: 'TKL',
		fullName: 'Tokelau',
		id: '05437a96-4381-11ef-a494-fb91df898f40',
		shortName: 'Tokelau',
		sortOrder: 224,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TO',
		code3Letters: 'TON',
		fullName: 'Tonga',
		id: '05438252-4381-11ef-a494-6bc29e15aebe',
		shortName: 'Tonga',
		sortOrder: 225,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TT',
		code3Letters: 'TTO',
		fullName: 'Trinidad and Tobago',
		id: '054388e2-4381-11ef-a494-af50336056aa',
		shortName: 'Trinidad and Tobago',
		sortOrder: 226,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TN',
		code3Letters: 'TUN',
		fullName: 'Tunisia',
		id: '05438ffe-4381-11ef-a494-e7c3e6e9a461',
		shortName: 'Tunisia',
		sortOrder: 227,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TM',
		code3Letters: 'TKM',
		fullName: 'Turkmenistan',
		id: '0543967a-4381-11ef-a494-bfa57744c40c',
		shortName: 'Turkmenistan',
		sortOrder: 228,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TC',
		code3Letters: 'TCA',
		fullName: 'Turks and Caicos Islands (The)',
		id: '05439cf6-4381-11ef-a494-a3acdb708ee3',
		shortName: 'Turks and Caicos Islands',
		sortOrder: 229,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TV',
		code3Letters: 'TUV',
		fullName: 'Tuvalu',
		id: '0543a390-4381-11ef-a494-7350a3de254f',
		shortName: 'Tuvalu',
		sortOrder: 230,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'TR',
		code3Letters: 'TUR',
		fullName: 'Türkiye',
		id: '0543a980-4381-11ef-a494-fff2a1cce940',
		shortName: 'Türkiye',
		sortOrder: 231,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'UG',
		code3Letters: 'UGA',
		fullName: 'Uganda',
		id: '0543af8e-4381-11ef-a494-af629eda5599',
		shortName: 'Uganda',
		sortOrder: 232,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'UA',
		code3Letters: 'UKR',
		fullName: 'Ukraine',
		id: '0543b574-4381-11ef-a494-57cd67eb784d',
		shortName: 'Ukraine',
		sortOrder: 233,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AE',
		code3Letters: 'ARE',
		fullName: 'United Arab Emirates (The)',
		id: '0543bbfa-4381-11ef-a494-abbc1273cb24',
		shortName: 'United Arab Emirates',
		sortOrder: 234,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'GB',
		code3Letters: 'GBR',
		fullName: 'United Kingdom of Great Britain and Northern Ireland (The)',
		id: '0543c23a-4381-11ef-a494-d751cd10bb8e',
		shortName: 'United Kingdom',
		sortOrder: 235,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'UM',
		code3Letters: 'UMI',
		fullName: 'United States Minor Outlying Islands (The)',
		id: '0543c898-4381-11ef-a494-f7109bee4da8',
		shortName: 'United States Minor Outlying Islands',
		sortOrder: 236,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'UY',
		code3Letters: 'URY',
		fullName: 'Uruguay',
		id: '0543ceec-4381-11ef-a494-b7849effa31b',
		shortName: 'Uruguay',
		sortOrder: 237,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'UZ',
		code3Letters: 'UZB',
		fullName: 'Uzbekistan',
		id: '0543d54a-4381-11ef-a494-b3e417059234',
		shortName: 'Uzbekistan',
		sortOrder: 238,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'VU',
		code3Letters: 'VUT',
		fullName: 'Vanuatu',
		id: '0543db9e-4381-11ef-a494-7bf355281827',
		shortName: 'Vanuatu',
		sortOrder: 239,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'VE',
		code3Letters: 'VEN',
		fullName: 'Venezuela (Bolivarian Republic of)',
		id: '0543e18e-4381-11ef-a494-1f8acc0f4f43',
		shortName: 'Venezuela',
		sortOrder: 240,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'VN',
		code3Letters: 'VNM',
		fullName: 'Viet Nam',
		id: '0543e76a-4381-11ef-a494-b3f6e5b9099e',
		shortName: 'Viet Nam',
		sortOrder: 241,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'VG',
		code3Letters: 'VGB',
		fullName: 'Virgin Islands (British)',
		id: '0543ed64-4381-11ef-a494-bfca743cf440',
		shortName: 'Virgin Islands (British)',
		sortOrder: 242,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'VI',
		code3Letters: 'VIR',
		fullName: 'Virgin Islands (U.S.)',
		id: '0543f354-4381-11ef-a494-237997d454ed',
		shortName: 'Virgin Islands (U.S.)',
		sortOrder: 243,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'WF',
		code3Letters: 'WLF',
		fullName: 'Wallis and Futuna',
		id: '0543f91c-4381-11ef-a494-0b44477390e3',
		shortName: 'Wallis and Futuna',
		sortOrder: 244,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'EH',
		code3Letters: 'ESH',
		fullName: 'Western Sahara*',
		id: '0543fed0-4381-11ef-a494-679a03de2ebd',
		shortName: 'Western Sahara*',
		sortOrder: 245,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'YE',
		code3Letters: 'YEM',
		fullName: 'Yemen',
		id: '05440484-4381-11ef-a494-8b45ab49b2eb',
		shortName: 'Yemen',
		sortOrder: 246,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ZM',
		code3Letters: 'ZMB',
		fullName: 'Zambia',
		id: '05440a42-4381-11ef-a494-57a0eeda0e3e',
		shortName: 'Zambia',
		sortOrder: 247,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'ZW',
		code3Letters: 'ZWE',
		fullName: 'Zimbabwe',
		id: '05441096-4381-11ef-a494-7b063b1d5731',
		shortName: 'Zimbabwe',
		sortOrder: 248,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: 'AX',
		code3Letters: 'ALA',
		fullName: 'Åland Islands',
		id: '05441690-4381-11ef-a494-975dcb56b7dc',
		shortName: 'Åland Islands',
		sortOrder: 249,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: null,
		code3Letters: null,
		fullName: null,
		id: '2ff352b2-5ff0-11ef-be40-4fb583c97e5a',
		shortName: 'USA',
		sortOrder: null,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: null,
		code3Letters: null,
		fullName: null,
		id: '4806f74e-6119-11ef-98e6-27f8e6df74aa',
		shortName: 'Canada',
		sortOrder: null,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: null,
		code3Letters: null,
		fullName: null,
		id: '74055f22-5ff0-11ef-be40-07a617ab2556',
		shortName: 'USA',
		sortOrder: null,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: null,
		code3Letters: null,
		fullName: null,
		id: 'e9fc2180-5fef-11ef-be40-8ba68f1c94cc',
		shortName: 'USA',
		sortOrder: null,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: null,
		code3Letters: null,
		fullName: null,
		id: 'ec30e058-5fef-11ef-be40-0fcd991ac8ae',
		shortName: 'USA',
		sortOrder: null,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: null,
		code3Letters: null,
		fullName: null,
		id: 'f73858fa-5fef-11ef-be40-5399a0af2be5',
		shortName: 'USA',
		sortOrder: null,
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code2Letters: null,
		code3Letters: null,
		fullName: null,
		id: 'f7aff4f6-5fdf-11ef-b9ca-3f4db9e74fbc',
		shortName: 'USA',
		sortOrder: null,
	},
];
const stateList = [
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'AL',
		country: null,
		id: '082aee88-4381-11ef-a494-abf3dbd8fcee',
		name: 'Alabama',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'AK',
		country: null,
		id: '082b17be-4381-11ef-a494-abecb87b2789',
		name: 'Alaska',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'AZ',
		country: null,
		id: '082b1d86-4381-11ef-a494-a36058bb85c2',
		name: 'Arizona',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'AR',
		country: null,
		id: '082b2178-4381-11ef-a494-5335533794c1',
		name: 'Arkansas',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: '082b251a-4381-11ef-a494-5b65535e5602',
		name: 'California',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CO',
		country: null,
		id: '082b2858-4381-11ef-a494-63d481e07de1',
		name: 'Colorado',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CT',
		country: null,
		id: '082b2b82-4381-11ef-a494-577003a5c12e',
		name: 'Connecticut',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'DE',
		country: null,
		id: '082b2e7a-4381-11ef-a494-af251f5c7038',
		name: 'Delaware',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'FL',
		country: null,
		id: '082b3190-4381-11ef-a494-236b482c44c4',
		name: 'Florida',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'GA',
		country: null,
		id: '082b349c-4381-11ef-a494-cffc52094907',
		name: 'Georgia',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'HI',
		country: null,
		id: '082b37bc-4381-11ef-a494-17f8fc98122b',
		name: 'Hawaii',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'ID',
		country: null,
		id: '082b3ab4-4381-11ef-a494-eb0cbb1edf73',
		name: 'Idaho',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'IL',
		country: null,
		id: '082b3dc0-4381-11ef-a494-a7fd7f77cddb',
		name: 'Illinois',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'IN',
		country: null,
		id: '082b40c2-4381-11ef-a494-7f27b2c8e6fb',
		name: 'Indiana',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'IA',
		country: null,
		id: '082b43ce-4381-11ef-a494-475127f99c9d',
		name: 'Iowa',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'KS',
		country: null,
		id: '082b46bc-4381-11ef-a494-5fb31a1d634d',
		name: 'Kansas',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'KY',
		country: null,
		id: '082b49aa-4381-11ef-a494-db4ca5bc7a3e',
		name: 'Kentucky',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'LA',
		country: null,
		id: '082b4cc0-4381-11ef-a494-9b7fa2a1d6d9',
		name: 'Louisiana',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'ME',
		country: null,
		id: '082b4fcc-4381-11ef-a494-1fc12a435200',
		name: 'Maine',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'MD',
		country: null,
		id: '082b52c4-4381-11ef-a494-031268ad26a5',
		name: 'Maryland',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'MA',
		country: null,
		id: '082b55bc-4381-11ef-a494-ef38dafd6048',
		name: 'Massachusetts',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'MI',
		country: null,
		id: '082b58b4-4381-11ef-a494-8b5bcd502229',
		name: 'Michigan',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'MN',
		country: null,
		id: '082b5bac-4381-11ef-a494-b725b965d5ec',
		name: 'Minnesota',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'MS',
		country: null,
		id: '082b5e9a-4381-11ef-a494-cb2edc6ee89c',
		name: 'Mississippi',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'MO',
		country: null,
		id: '082b619c-4381-11ef-a494-bbc7d788984b',
		name: 'Missouri',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'MT',
		country: null,
		id: '082b6494-4381-11ef-a494-8fb507a6335e',
		name: 'Montana',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'NE',
		country: null,
		id: '082b6796-4381-11ef-a494-cf45f52e694b',
		name: 'Nebraska',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'NV',
		country: null,
		id: '082b6a7a-4381-11ef-a494-535ec90fe31d',
		name: 'Nevada',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'NH',
		country: null,
		id: '082b6e44-4381-11ef-a494-37fe041bbae8',
		name: 'New Hampshire',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'NJ',
		country: null,
		id: '082b71fa-4381-11ef-a494-ab5416296c80',
		name: 'New Jersey',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'NM',
		country: null,
		id: '082b759c-4381-11ef-a494-2f9f81fc6843',
		name: 'New Mexico',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'NY',
		country: null,
		id: '082b792a-4381-11ef-a494-2b0c2a404129',
		name: 'New York',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'NC',
		country: null,
		id: '082b7cae-4381-11ef-a494-33402ea5f287',
		name: 'North Carolina',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'ND',
		country: null,
		id: '082b7fd8-4381-11ef-a494-377771cbf15d',
		name: 'North Dakota',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'OH',
		country: null,
		id: '082b82d0-4381-11ef-a494-5bea7decf5af',
		name: 'Ohio',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'OK',
		country: null,
		id: '082b85f0-4381-11ef-a494-971bab04001c',
		name: 'Oklahoma',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'OR',
		country: null,
		id: '082b88f2-4381-11ef-a494-27e4898f7267',
		name: 'Oregon',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'PA',
		country: null,
		id: '082b8c08-4381-11ef-a494-7f1507d8a98c',
		name: 'Pennsylvania',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'RI',
		country: null,
		id: '082b8f1e-4381-11ef-a494-9b199a3dd474',
		name: 'Rhode Island',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'SC',
		country: null,
		id: '082b9220-4381-11ef-a494-835dc4205cb1',
		name: 'South Carolina',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'SD',
		country: null,
		id: '082b952c-4381-11ef-a494-6b3c301ad5fd',
		name: 'South Dakota',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'TN',
		country: null,
		id: '082b982e-4381-11ef-a494-234a66c10f01',
		name: 'Tennessee',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'TX',
		country: null,
		id: '082b9b58-4381-11ef-a494-2fa84305252e',
		name: 'Texas',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'UT',
		country: null,
		id: '082b9e5a-4381-11ef-a494-c3c07283dae3',
		name: 'Utah',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'VT',
		country: null,
		id: '082ba1d4-4381-11ef-a494-7b593f760bbf',
		name: 'Vermont',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'VA',
		country: null,
		id: '082ba526-4381-11ef-a494-3f808ad3c7e6',
		name: 'Virginia',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'WA',
		country: null,
		id: '082ba83c-4381-11ef-a494-df509fbc6358',
		name: 'Washington',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'WV',
		country: null,
		id: '082bab5c-4381-11ef-a494-33cc1debf88f',
		name: 'West Virginia',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'WI',
		country: null,
		id: '082bae68-4381-11ef-a494-8f5c95c21abe',
		name: 'Wisconsin',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'WY',
		country: null,
		id: '082bb19c-4381-11ef-a494-777685d606ae',
		name: 'Wyoming',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'AS',
		country: null,
		id: '082bb4a8-4381-11ef-a494-9bb0661edc19',
		name: 'American Samoa',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'GU',
		country: null,
		id: '082bb7b4-4381-11ef-a494-1f3780facf20',
		name: 'Guam',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'MP',
		country: null,
		id: '082bbaca-4381-11ef-a494-4f51b5c7db75',
		name: 'Northern Mariana Islands',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'PR',
		country: null,
		id: '082bbde0-4381-11ef-a494-5fd279eb97b6',
		name: 'Puerto Rico',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'VI',
		country: null,
		id: '082bc0e2-4381-11ef-a494-0bef9d446e6b',
		name: 'Virgin Islands',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: '2ff35b0e-5ff0-11ef-be40-4baeca616b54',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: '2ff3710c-5ff0-11ef-be40-5fe182db206a',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: '2ff3841c-5ff0-11ef-be40-63271a9720e2',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: '61856974-5bc1-11ef-92e0-c33ecb2bd7a8',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: '6506b4be-53ff-11ef-a119-37d0eaa90420',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: '6ac14630-5427-11ef-9b82-a3a69aab525b',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: '74056760-5ff0-11ef-be40-173d3a6977ff',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: '74057d54-5ff0-11ef-be40-c7872d403c85',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: '74059078-5ff0-11ef-be40-af39eae91689',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: '7f9831b4-5bbf-11ef-98e3-bf4a887c627a',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: '8a5038d6-5a34-11ef-bbc6-c79dd81d47d8',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: '9bed5e8e-53fe-11ef-a119-37dc2ca4d901',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'aa657fb2-5bd0-11ef-b937-1fe7e5515fd9',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'aa65a58c-5bd0-11ef-b937-3fd2c5660ebf',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'aa65b8ec-5bd0-11ef-b937-47170fc4c5cd',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: 'b25d67d6-53fe-11ef-a119-af7370a24ab8',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: 'c0ad920c-53fe-11ef-a119-6775d17ddeab',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: 'c3b7cd82-5336-11ef-933a-ab8b2cb35959',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: null,
		country: null,
		id: 'c41d7e5c-53fe-11ef-a119-7b85996654cd',
		name: 'HA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'e9fc2dd8-5fef-11ef-be40-e7b56a2d9f85',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'e9fc45fc-5fef-11ef-be40-2f4ae93c0222',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'e9fc5aa6-5fef-11ef-be40-af216adea3e0',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'ec30e95e-5fef-11ef-be40-c7553db17afc',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'ec30ffde-5fef-11ef-be40-2b71963733d8',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'ec3112ee-5fef-11ef-be40-9fbcdeabb845',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'f7386296-5fef-11ef-be40-d7cc0f917dea',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'f73879ac-5fef-11ef-be40-8be755ee549c',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'f7388d0c-5fef-11ef-be40-d7a4aab04005',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'f7b01b52-5fdf-11ef-b9ca-6f7ed0d43fea',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'f7b04262-5fdf-11ef-b9ca-3711de8a90de',
		name: 'CA',
	},
	{
		bulkLoadRecIdJfyApx: null,
		bulkLoadRunIdJfyApx: null,
		code: 'CA',
		country: null,
		id: 'f7b05630-5fdf-11ef-b9ca-1b40e1582f18',
		name: 'CA',
	},
];
export const SimpleFundingSource = (args) => {
	const rowData = createRows(13);

	return (
		<FundingSource
			rowData={rowData}
			registrationType="IRA-SEP"
			sourceTypes={sourceTypes}
			countryList={countryList}
			stateList={stateList}
			{...args}
		></FundingSource>
	);
};
SimpleFundingSource.args = {
	id: '1',
};

const fundingSourceMetaDataAxios = {
	ach: {
		on: true,
		enableRoutingNumberBasedBank: true,
		summary: [
			{
				icon: '\\e9c4',
				key: 'bankAccount.bankName',
				name: 'Bank',
			},
			{
				icon: '\\e9c5',
				key: 'bankAccount.bankAccountNumber',
				name: 'Account',
			},
		],

		fieldConfigs: {
			retainForFutureTransfers: {
				on: false,
			},
			isPersonalAccount: {
				on: false,
			},

			receivingAccountHolderIsOwner: {
				on: false,
			},

			deliveringAccountTitleSameAsReceiving: {
				on: false,
			},
			isForProductPurchase: {
				on: false,
			},
			usedForAccountFundingAmount: {
				on: false,
			},
		},
		accordianConfigs: {
			typeOfTransfer: {
				on: false,
			},
		},
	},
	check: {
		on: true,
		summary: [
			{
				icon: '\\e9c6',
				key: 'amount',
				name: 'Amount',
				type: 'currency',
			},
		],
		fieldConfigs: {
			usedForAccountFunding: {
				on: false,
			},
			checkDepositOption: {
				on: false,
			},
		},
	},
	wire_transfer: {
		on: true,
		summary: [
			{
				icon: '\\e9c6',
				key: 'amount',
				name: 'Amount',
				type: 'currency',
			},
		],
		fieldConfigs: {
			usedForAccountFunding: {
				on: false,
			},
		},
	},
	'external_transfer_-_acat': {
		on: true,
		firmInformation: [
			{
				name: 'Firm Name',
				value: 'Axos Clearing, LLC',
				icon: '\\e9c4',
			},
			{
				name: 'Clearing Number',
				value: 'DTCC - 0052   OCC - 0052',
				icon: '\\e9c7',
			},
			{
				name: 'Phone Number',
				value: '(*************',
				icon: '\\e9c8',
			},
			{
				name: 'Address',
				value: '15950 West Dodge Road, Suite 300, Omaha, NE  68118',
				icon: '\\e9c9',
			},
		],
		summary: [
			{
				icon: '\\e9c7',
				key: 'otherAccount.clearingNumber',
				name: 'Clearing Number',
			},
		],
		topLevel: [
			{
				id: 'acat-1',
				heading: 'Select a Transfer Instruction',
				options: 'SecuritiesTransferTypes',
				optionsIRA: 'SecuritiesTransferTypesIRA',
				key: 'transferType',
			},
		],
		fieldConfigs: {
			brokerageOrTrustCompany: {
				on: false,
			},
			liquidateAndTransferOption: {
				on: false,
			},
			'otherAccount.lastNameChanged': {
				on: false,
			},
			'otherAccount.firstMiddleNameChanged': {
				on: false,
			},
		},
	},
	'external_transfer_-_non-acat': {
		on: true,
		firmInformation: [
			{
				name: 'Firm Name',
				value: 'Axos Clearing, LLC',
				icon: '\\e9c4',
			},
			{
				name: 'Clearing Number',
				value: 'DTCC - 0052   OCC - 0052',
				icon: '\\e9c7',
			},
			{
				name: 'Phone Number',
				value: '(*************',
				icon: '\\e9c8',
			},
			{
				name: 'Address',
				value: '15950 West Dodge Road, Suite 300, Omaha, NE  68118',
				icon: '\\e9c9',
			},
		],
		summary: [
			{
				icon: '\\e9c7',
				key: 'otherAccount.clearingNumber',
				name: 'Clearing Number',
			},
		],
		topLevel: [
			{
				id: 'acat-2',
				heading: 'Select a Transfer Instruction',
				options: 'BankTransferTypeOptions',
				optionsIRA: 'BankTransferTypeOptionsIRA',
				key: 'transferType',
			},
		],
		fieldConfigs: {},
	},
	internal_transfer: {
		on: true,
		text: 'Internal',
		summary: [
			{
				icon: '\\e9c5',
				key: 'deliveringAccountAccountNumber',
				name: 'Account',
			},
		],
		fieldConfigs: {
			usedForAccountFunding: {
				on: false,
			},
			transferUnits: {
				on: false,
			},
			'securities.amount': {
				on: false,
			},
			notes: {
				on: false,
			},
		},
	},
};
const fundingSourceMetaDataTraid = {
	ach: {
		on: true,
		enableRoutingNumberBasedBank: true,
		summary: [
			{
				icon: '\\e9c4',
				key: 'bankAccount.bankName',
				name: 'Bank',
			},
			{
				icon: '\\e9c5',
				key: 'bankAccount.bankAccountNumber',
				name: 'Account',
			},
		],
		fieldConfigs: {
			detailsToBeProvided: {
				on: false,
			},
			'bankAccount.bankAccountHasOtherOwners': {
				on: false,
			},
			retainForFutureTransfers: {
				on: false,
			},
			isForProductPurchase: {
				on: false,
			},
			usedForAccountFundingAmount: {
				on: false,
			},
		},
	},
	check: {
		on: true,
		summary: [
			{
				icon: '\\e9c6',
				key: 'amount',
				name: 'Amount',
				type: 'currency',
			},
		],
		fieldConfigs: {
			usedForAccountFunding: {
				on: false,
			},
			checkDepositOption: {
				on: false,
			},
		},
	},
	wire_transfer: {
		on: true,
		summary: [
			{
				icon: '\\e9c6',
				key: 'amount',
				name: 'Amount',
				type: 'currency',
			},
		],
		fieldConfigs: {
			usedForAccountFunding: {
				on: false,
			},
		},
	},
	'external_transfer_-_acat': {
		on: true,
		text: 'External Transfer',
		firmInformation: [
			{
				name: 'Firm Name',
				value: '',
				icon: '\\e9c4',
			},
			{
				name: 'Clearing Number',
				value: '',
				icon: '\\e9c7',
			},
			{
				name: 'Phone Number',
				value: '',
				icon: '\\e9c8',
			},
			{
				name: 'Address',
				value: '',
				icon: '\\e9c9',
			},
		],
		summary: [
			{
				icon: '\\e9c7',
				key: 'otherAccount.clearingNumber',
				name: 'Clearing Number',
			},
		],
		topLevel: [
			{
				id: 'acat-1',
				heading: 'Select a Transfer Instruction',
				options: 'SecuritiesTransferTypes',
				optionsIRA: 'SecuritiesTransferTypes',
				key: 'transferType',
			},
		],
		fieldConfigs: {
			detailsToBeProvided: {
				on: false,
			},
			'otherAccount.ssn': {
				on: false,
			},
			'otherAccount.clearingNumber': {
				on: false,
			},
			description: {
				text: 'Fund Name',
			},
			'otherAccount.firmAddress': {
				on: false,
			},
			transferType: {
				on: false,
			},
			cusip: {
				text: 'Symbol',
			},
			symbol: {
				text: 'CUSIP or Symbol',
			},
			'otherAccount.accountTitle': {
				text: 'Name and title of account',
			},
			'otherAccount.accountType': {
				text: 'Account type/registration',
			},
			'otherAccount.firmPhoneNumber': {
				text: 'Phone number',
			},
		},
		accordianConfigs: {
			oneAndTheSamePersonCertification: {
				on: false,
			},
			accountRegistrationDifferences: {
				on: false,
			},
		},
	},
	'external_transfer_-_non-acat': {
		on: false,
		firmInformation: [
			{
				name: 'Firm Name',
				value: 'Axos Clearing, LLC',
				icon: '\\e9c4',
			},
			{
				name: 'Clearing Number',
				value: 'DTCC - 0052   OCC - 0052',
				icon: '\\e9c7',
			},
			{
				name: 'Phone Number',
				value: '(*************',
				icon: '\\e9c8',
			},
			{
				name: 'Address',
				value: '15950 West Dodge Road, Suite 300, Omaha, NE  68118',
				icon: '\\e9c9',
			},
		],
		summary: [
			{
				icon: '\\e9c7',
				key: 'otherAccount.clearingNumber',
				name: 'Clearing Number',
			},
		],
		topLevel: [
			{
				id: 'acat-2',
				heading: 'Select a Transfer Instruction',
				options: 'SecuritiesTransferTypesNONACAT',
				optionsIRA: 'SecuritiesTransferTypesNONACAT',
				key: 'transferType',
			},
		],
		fieldConfigs: {},
	},
	internal_transfer: {
		on: true,
		text: 'Internal Transfer',
		summary: [
			{
				icon: '\\e9c5',
				key: 'deliveringAccountAccountNumber',
				name: 'Account',
			},
		],
		accordianConfigs: {
			recievingAccountInformation: {
				on: false,
			},
			transferInstructions: {
				on: false,
			},
			partialAccountTransfer: {
				on: false,
			},
		},
		fieldConfigs: {
			usedForAccountFunding: {
				on: false,
			},
			transferUnits: {
				on: false,
			},
			'securities.amount': {
				on: false,
			},
			notes: {
				on: false,
			},
			'otherAccount.accountTitle': {
				on: false,
			},
		},
	},
};
const fundingSourceMetaDataCitizen = {
	version: '2.0',
	ach: {
		on: true,
		fieldProps: {
			phoneNumber: {
				country: '*',
				showISDCode: true,
			},
		},
		summary: [
			{
				icon: '\\e9c4',
				key: 'bankAccount.bankName',
				name: 'Bank',
			},
			{
				icon: '\\e9c5',
				key: 'bankAccount.bankAccountNumber',
				name: 'Account',
			},
		],
		fieldConfigs: {
			isPersonalAccount: {
				on: false,
			},

			receivingAccountHolderIsOwner: {
				on: false,
			},

			deliveringAccountTitleSameAsReceiving: {
				on: false,
			},
			'bankAccount.bankAccountHasOtherOwners': {
				on: false,
			},
			'bankAccount.otherOwnerEmail': {
				on: false,
			},
			'bankAccount.otherOwnerName': {
				on: false,
			},
			detailsToBeProvided: {
				on: false,
			},
			amount: {
				on: false,
			},
		},
		accordianConfigs: {
			typeOfTransfer: {
				on: false,
			},
		},
	},
	check: {
		on: true,
		fieldProps: {
			phoneNumber: {
				country: '*',
				showISDCode: true,
			},
		},
		summary: [
			{
				icon: '\\e9c6',
				key: 'amount',
				name: 'Amount',
				type: 'currency',
			},
		],
		fieldConfigs: {
			amount: {
				on: false,
			},
		},
	},
	wire_transfer: {
		on: true,
		text: 'Bank Wire',
		fieldProps: {
			phoneNumber: {
				country: '*',
				showISDCode: true,
			},
		},
		summary: [
			{
				icon: '\\e9c6',
				key: 'amount',
				name: 'Amount',
				type: 'currency',
			},
		],
		fieldConfigs: {
			amount: {
				on: false,
			},
		},
	},
	'external_transfer_-_acat': {
		on: true,
		text: 'External Transfer',
		fieldProps: {
			phoneNumber: {
				country: '*',
				showISDCode: true,
			},
		},
		topLevel: [
			{
				id: 'acat-1',
				heading: 'Select a Transfer Instruction',
				options: 'ExternalTransferTopLevel',
				optionsIRA: 'ExternalTransferTopLevel',
				key: 'transferType',
			},
		],
		firmInformation: [
			{
				name: 'Receiving Firm Clearing Number',
				value: '0226',
				icon: 'icon_-Tb_input_check',
			},
			{
				name: 'Firm Name',
				value: 'National Financial Services',
				icon: 'icon_-Tb_building_bank',
			},
			{
				name: 'Account Type',
				type: 'accountType',
				icon: 'icon_-Tb_building_bank',
			},
		],
		summary: [
			{
				icon: '\\e9c7',
				key: 'otherAccount.clearingNumber',
				name: 'Clearing Number',
			},
		],
		fieldConfigs: {
			detailsToBeProvided: {
				on: false,
			},
			'otherAccount.deliveringAccountTitleSameAsReceiving': {
				on: false,
			},
			'otherAccount.ssn': {
				on: false,
			},
			cusip: {
				text: 'Symbol',
			},
			bankAccountTransferType: {
				enum: 'BankTransferTypeCitizen',
			},
			dividendOption: {
				on: false,
			},
			capitalGainsOption: {
				on: false,
			},
			'otherAccount.accountTitle': {
				on: false,
			},
			'otherAccount.clearingNumber': {
				text: 'DTC number',
			},
			fullName: {
				on: false,
			},
			primaryEmail: {
				on: false,
			},
			'otherAccount.payeeName': {
				on: false,
			},
			'otherAccount.accountHolderAlternateName': {
				on: false,
			},
		},
	},
	'external_transfer_-_non-acat': {
		on: false,
	},
	internal_transfer: {
		on: true,
		fieldProps: {
			phoneNumber: {
				country: '*',
				showISDCode: true,
			},
		},
		topLevel: [
			{
				id: 'internal-transfer-1',
				heading: 'Select a Transfer Instruction',
				options: 'InternalTransferTopLevel',
				optionsIRA: 'InternalTransferTopLevel',
				key: 'internalTransferType',
			},
		],
		summary: [
			{
				icon: '\\e9c5',
				key: 'deliveringAccountAccountNumber',
				name: 'Account',
			},
		],
		fieldConfigs: {
			transferInstructionsAccountNumber: {
				count: 6,
			},
			detailsToBeProvided: {
				on: false,
			},
			'otherAccount.accountTitle': {
				on: false,
			},
			quantity: {
				text: 'Amount of shares',
			},
		},
	},
};

const enums = {
	RolloverDetails: [
		{ optionLabel: 'Solicited', optionValue: 'Solicited' },
		{ optionLabel: 'Unsolicited', optionValue: 'Unsolicited' },
	],

	FederalIncomeWithholding: [
		{
			optionLabel: 'Withhold 10% federal income tax',
			optionValue: 'Withhold 10% federal income tax',
		},
		{
			optionLabel: 'Withhold federal income tax %',
			optionValue: 'Withhold federal income tax %',
		},
	],

	BankAccountType: [
		{ optionLabel: 'Checking', optionValue: 'Checking' },
		{ optionLabel: 'Savings', optionValue: 'Savings' },
	],
	WithholdingType: [
		{ optionLabel: 'Do not withhold', optionValue: 'Do not withhold' },
		{ optionLabel: 'Withhold $', optionValue: 'Withhold $' },
		{ optionLabel: 'Withhold %', optionValue: 'Withhold %' },
		{
			optionLabel: 'Withhold % of federal withholding',
			optionValue: 'Withhold % of federal withholding',
		},
	],
	SpacielDelivaryOptions: [
		{ optionLabel: 'Overnight deliver', optionValue: 'Overnight deliver' },
		{ optionLabel: 'Saturday overnight', optionValue: 'Saturday overnight' },
		{ optionLabel: 'Alternate Payee', optionValue: 'Alternate Payee' },
	],
	WithholdingTypeWithoutStateCA: [
		{ optionLabel: 'Do not withhold', optionValue: 'Do not withhold' },
		{ optionLabel: 'Withhold $', optionValue: 'Withhold $' },
		{ optionLabel: 'Withhold %', optionValue: 'Withhold %' },
	],
	IRADistributionReasons: [
		{ optionLabel: 'Normal - age 59 1/2 and older', optionValue: 'Normal' },
		{ optionLabel: 'Early', optionValue: 'Early' },
		{ optionLabel: 'Roth', optionValue: 'Roth' },
		{ optionLabel: 'SIMPLE IRA', optionValue: 'SimpleIRA' },
		{
			optionLabel: 'Substantially Equal Series',
			optionValue: 'SubstantiallyEqualSeries',
		},
		{ optionLabel: 'Permanent Disability', optionValue: 'PermanentDisability' },
		{ optionLabel: 'Roth Conversion', optionValue: 'RothConversion' },
		{
			optionLabel: 'Due to Death from Inherited Beneficiary Account',
			optionValue: 'InheritedBeneficiary',
		},
		{
			optionLabel:
				'Direct Transfer to an Eligible Employer-Sponsored Retirement Plan',
			optionValue: 'EmployerPlanTransfer',
		},
		{
			optionLabel: 'Return of Excess Contribution',
			optionValue: 'ExcessContributionReturn',
		},
		{ optionLabel: 'Recharacterization', optionValue: 'Recharacterization' },
	],
	MeetsAgeRequirementOptions: [
		{ optionLabel: 'Checking', optionValue: true },
		{ optionLabel: 'Savings', optionValue: false },
	],
	IraDistributionAmountOptions: [
		{ optionLabel: 'Distribute Amount', optionValue: 'Distribute Amount' },
		{ optionLabel: 'Distribute RMD', optionValue: 'Distribute RMD' },
		{
			optionLabel: 'Distribute earnings (dividends & interest)',
			optionValue: 'Distribute earnings (dividends & interest)',
		},
	],
	BrokerageOrTrustCompanyOptions: [
		{
			optionLabel: 'Full Account Transfer,In Kind',
			optionValue: 'Full Account Transfer,In Kind',
		},
		{
			optionLabel: 'Partial Account Transfer,In Kind',
			optionValue: 'Partial Account Transfer,In Kind',
		},
	],
	// BankTransferTypeCitizen: [
	// 	{
	// 		optionLabel: 'Transfer All Cash',
	// 		optionValue: 'Transfer All Cash',
	// 	},
	// 	{
	// 		optionLabel: 'Liquidate CD Immediately and Transfer Cash',
	// 		optionValue: 'Liquidate CD Immediately and Transfer Cash',
	// 	},
	// 	{
	// 		optionLabel: 'Liquidate CD at Maturity and Transfer Cash',
	// 		optionValue: 'Liquidate CD at Maturity and Transfer Cash',
	// 	},
	// ],
	TransferAgentTransfer: [
		{
			optionLabel: 'Transfer all whole shares and sell fractions',
			optionValue: 'TransferAllSharesSellFractions',
		},
		{
			optionLabel: 'Transfer all whole shares only',
			optionValue: 'TransferAllSharesOnly',
		},
		{
			optionLabel:
				'Transfer all whole shares, sell fractions and close account.',
			optionValue: 'TransferAllSharesSellFractionsClose',
		},
		{
			optionLabel: 'Partial transfer of whole shares',
			optionValue: 'PartialTransferWholeShares',
		},
	],
	AmountTypeCitizen: [
		{ optionLabel: 'Shares', optionValue: 'Shares' },
		{ optionLabel: '$', optionValue: 'Dollar' },
	],

	YesOrNo: [
		{ optionLabel: 'Yes', optionValue: true },
		{ optionLabel: 'No', optionValue: false },
	],
	Frequency: [
		{ optionLabel: 'Weekly', optionValue: 'Weekly' },
		{ optionLabel: 'Semi-Monthly', optionValue: 'SemiMonthly' },
		{ optionLabel: 'Monthly', optionValue: 'Monthly' },
		{ optionLabel: 'Quarterly', optionValue: 'Quarterly' },
		{ optionLabel: 'Semi-Annually', optionValue: 'SemiAnnually' },
		{ optionLabel: 'Annually', optionValue: 'Annually' },
	],

	SecuritiesTransferTypes: [
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '4c1f4700-fa52-11ef-9447-13e39efcaf74',
			isDefault: false,
			optionLabel: 'Bank IRA CD/Savings Transfer',
			optionOrder: 1,
			optionValue: 'Bank IRA CD/Savings Transfer',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '709a31e6-fa5a-11ef-b398-9bd913d5db88',
			isDefault: false,
			optionLabel: 'Insurance Transfer/1035 exchange',
			optionOrder: 5,
			optionValue: 'Insurance Transfer/1035 exchange',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '74e11a96-fa53-11ef-8035-6fae4b055767',
			isDefault: false,
			optionLabel: 'Annuity Company Transfer',
			optionOrder: 4,
			optionValue: 'Annuity Company Transfer',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '82afa280-fa5a-11ef-b398-7f99a7bf03ad',
			isDefault: false,
			optionLabel: 'Transfer Agent Transfer',
			optionOrder: 6,
			optionValue: 'Transfer Agent Transfer',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '975abbac-fa5a-11ef-b398-33ea865a39d6',
			isDefault: false,
			optionLabel: 'Employer Sponsored Plan Rollover',
			optionOrder: 7,
			optionValue: 'Employer Sponsored Plan Rollover',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: 'b59342ea-fa52-11ef-8035-9f3516a96796',
			isDefault: false,
			optionLabel: 'Brokerage/Trust Company Transfer',
			optionOrder: 2,
			optionValue: 'Brokerage/Trust Company Transfer',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: 'dd02997a-fa52-11ef-8035-abb8f7bec4e7',
			isDefault: false,
			optionLabel: 'Mutual Fund Company Transfer',
			optionOrder: 3,
			optionValue: 'Mutual Fund Company Transfer',
			orgUnitCode: null,
		},
	],
	SecuritiesTransferTypesIRA: [
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '4c1f4700-fa52-11ef-9447-13e39efcaf74',
			isDefault: false,
			optionLabel: 'Bank IRA CD/Savings Transfer',
			optionOrder: 1,
			optionValue: 'Bank IRA CD/Savings Transfer',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '709a31e6-fa5a-11ef-b398-9bd913d5db88',
			isDefault: false,
			optionLabel: 'Insurance Transfer/1035 exchange',
			optionOrder: 5,
			optionValue: 'Insurance Transfer/1035 exchange',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '74e11a96-fa53-11ef-8035-6fae4b055767',
			isDefault: false,
			optionLabel: 'Annuity Company Transfer',
			optionOrder: 4,
			optionValue: 'Annuity Company Transfer',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '82afa280-fa5a-11ef-b398-7f99a7bf03ad',
			isDefault: false,
			optionLabel: 'Transfer Agent Transfer',
			optionOrder: 6,
			optionValue: 'Transfer Agent Transfer',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: '975abbac-fa5a-11ef-b398-33ea865a39d6',
			isDefault: false,
			optionLabel: 'Employer Sponsored Plan Rollover',
			optionOrder: 7,
			optionValue: 'Employer Sponsored Plan Rollover',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: 'b59342ea-fa52-11ef-8035-9f3516a96796',
			isDefault: false,
			optionLabel: 'Brokerage/Trust Company Transfer',
			optionOrder: 2,
			optionValue: 'Brokerage/Trust Company Transfer',
			orgUnitCode: null,
		},
		{
			code: null,
			color: null,
			description: null,
			disabled: false,
			domainName: null,
			enumerationName: 'SecuritiesTransferTypes',
			icon: null,
			id: 'dd02997a-fa52-11ef-8035-abb8f7bec4e7',
			isDefault: false,
			optionLabel: 'Mutual Fund Company Transfer',
			optionOrder: 3,
			optionValue: 'Mutual Fund Company Transfer',
			orgUnitCode: null,
		},
	],
	ExistingAccountTypeOptions: [
		{ optionLabel: 'Traditional IRA', optionValue: 'Traditional IRA' },
		{ optionLabel: 'Roth IRA', optionValue: 'Roth IRA' },
		{ optionLabel: 'SEP IRA', optionValue: 'SEP IRA' },
		{ optionLabel: 'Beneficiary IRA', optionValue: 'Beneficiary IRA' },
	],
	TransferInstructions: [
		{
			optionLabel: 'Transfer immediately',
			optionValue: 'Transfer immediately',
		},
		{
			optionLabel: 'Transfer at maturity date',
			optionValue: 'Transfer at maturity date',
		},
	],
	ExternalTransferTopLevel: [
		{
			optionLabel: 'Bank IRA CD/Savings Transfer',
			optionValue: 'Bank IRA CD/Savings Transfer',
		},
		{
			optionLabel: 'Brokerage/Trust Company Transfer',
			optionValue: 'Brokerage/Trust Company Transfer',
		},
		{
			optionLabel: 'Mutual Fund Company Transfer',
			optionValue: 'Mutual Fund Company Transfer',
		},
		{
			optionLabel: 'Annuity Company Transfer',
			optionValue: 'Annuity Company Transfer',
		},
		{
			optionLabel: 'Insurance Transfer/1035 exchange',
			optionValue: 'Insurance Transfer/1035 exchange',
		},
		{
			optionLabel: 'Transfer Agent Transfer',
			optionValue: 'Transfer Agent Transfer',
		},
		{
			optionLabel: 'Employer Sponsored Plan Rollover',
			optionValue: 'Employer Sponsored Plan Rollover',
		},
	],
	PayableToOptions: [
		{
			optionLabel: 'National Financial Services',
			optionValue: 'National Financial Services',
		},
		{ optionLabel: 'Insurance Company', optionValue: 'Insurance Company' },
	],
	TransferOptions: [
		{ optionLabel: 'Transfer in Kind', optionValue: 'Transfer in Kind' },
		{ optionLabel: 'Liquidate', optionValue: 'Liquidate' },
	],
	CheckDepositOptions: [
		{ optionLabel: 'Physical', optionValue: 'Physical' },
		{ optionLabel: 'Remote Deposit', optionValue: 'Remote Deposit' },
	],
	RecharacterizationYear: [
		{ optionLabel: 'Same year', optionValue: 'Same year' },
		{ optionLabel: 'Prior year', optionValue: 'Prior year' },
	],
	DelivaryMethodOptionsACH: [
		{ optionLabel: 'Journal entry', optionValue: 'Journal entry' },
		{
			optionLabel:
				'Deliver check or scurities in name of the account owner address of record',
			optionValue:
				'Deliver check or scurities in name of the account owner address of record',
		},

		{ optionLabel: 'ACH', optionValue: 'ACH' },
	],
	LiquidationInstructions: [
		{
			optionLabel: 'Liquidate and transfer 100% of my assets',
			optionValue: 'Liquidate and transfer 100% of my assets',
		},
		{
			optionLabel: 'Liquidate and transfer $ of my assets',
			optionValue: 'Liquidate and transfer $ of my assets',
		},
	],
	RMDOptions: [
		{
			optionLabel: 'RMD Satisfied (RMD prior to transfer)',
			optionValue: 'RMD Satisfied (RMD prior to transfer)',
		},
		{
			optionLabel: 'Transfer (RMD will transferred to Citizens Securities)',
			optionValue: 'Transfer (RMD will transferred to Citizens Securities)',
		},
	],
	TransferTypeOptionsInernalTrans: [
		{
			optionLabel: 'Full Account Transfer',
			optionValue: 'Full Account Transfer',
		},
		{
			optionLabel: 'Partial Account Transfer',
			optionValue: 'Partial Account Transfer',
		},
	],
	InternalTransferTypeOptions: [{ optionLabel: 'Cash', optionValue: 'Cash' }],
	InternalTransferTypeOptionsForNonIraContributions: [
		{ optionLabel: 'Cash', optionValue: 'Cash' },
		{ optionLabel: 'Securities', optionValue: 'Securities' },
	],
	InternalTransferTypeOptionsForCashTransfer: [
		{ optionLabel: 'All Cash', optionValue: 'All Cash' },
		{
			optionLabel: 'Specific Amount Of Cash',
			optionValue: 'Specific Amount Of Cash',
		},
	],
	IRAContributionYears: [
		{ optionLabel: 'Current Year', optionValue: 'Current Year' },
		{ optionLabel: 'Prior Year', optionValue: 'Prior Year' },
	],
	AmountTypeOptions: [
		{ optionLabel: 'Shares', optionValue: 'Shares' },
		{ optionLabel: '$', optionValue: '$' },
	],
	TransferTypeNonACAT: [
		{
			optionLabel: 'Brokerage/Trust Company Full Account Transfer',
			optionValue: 'Brokerage/Trust Company Full Account Transfer',
			icon: 'icon_-Tb_switch_horizontal',
		},
		{
			optionLabel: 'Brokerage/Trust Company Partial Account Transfer',
			optionValue: 'Brokerage/Trust Company Partial Account Transfer',
			icon: 'icon_-Tb_switch_horizontal',
		},
	],
	// BankTransferTypeOptions: [
	// 	{
	// 		optionLabel: 'Brokerage/Trust Company Full Account Transfer',
	// 		optionValue: 'Brokerage/Trust Company Full Account Transfer',
	// 		icon: 'icon_Tb_switch_horizontal',
	// 	},
	// 	{
	// 		optionLabel: 'Brokerage/Trust Company Partial Account Transfer',
	// 		optionValue: 'Brokerage/Trust Company Partial Account Transfer',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// 	{
	// 		optionLabel: 'Transfer All Cash',
	// 		optionValue: 'Transfer All Cash',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// 	{
	// 		optionLabel: 'Transfer Partial Cash',
	// 		optionValue: 'Transfer Partial Cash',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// 	{
	// 		optionLabel: 'Liquidate CD Immediately and Transfer Cash',
	// 		optionValue: 'Liquidate CD Immediately and Transfer Cash',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// 	{
	// 		optionLabel: 'Liquidate CD at Maturity and Transfer Cash',
	// 		optionValue: 'Liquidate CD at Maturity and Transfer Cash',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// ],
	// BankTransferTypeOptionsIRA: [
	// 	{
	// 		optionLabel: 'Transfer All Cash',
	// 		optionValue: 'Transfer All Cash',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// 	{
	// 		optionLabel: 'Transfer Partial Cash',
	// 		optionValue: 'Transfer Partial Cash',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// 	{
	// 		optionLabel: 'Liquidate CD Immediately and Transfer Cash',
	// 		optionValue: 'Liquidate CD Immediately and Transfer Cash',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// 	{
	// 		optionLabel: 'Liquidate CD at Maturity and Transfer Cash',
	// 		optionValue: 'Liquidate CD at Maturity and Transfer Cash',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// 	{
	// 		optionLabel: 'Bank or Credit Union Transfer',
	// 		optionValue: 'Bank or Credit Union Transfer',
	// 		icon: 'icon_-Tb_switch_horizontal',
	// 	},
	// ],
	TransferAllOptions: [
		{ optionLabel: 'All', optionValue: true },
		{ optionLabel: 'Share Amount', optionValue: false },
	],
	CapitalAndDvidentOptions: [
		{
			optionLabel: 'Transfer Immediately',
			optionValue: 'Transfer Immediately',
		},
		{
			optionLabel: 'Transfer at maturity date',
			optionValue: 'Transfer at maturity date',
		},
	],
	InternalTransferTopLevel: [
		{
			optionLabel: 'Bank IRA CD/Savings Transfer',
			optionValue: 'Bank IRA CD/Savings Transfer',
			icon: 'icon_-Tb_building_bank',
		},
		{
			optionLabel: 'Existing CSI Account Journal',
			optionValue: 'Existing CSI Account Journal',
			icon: 'icon_-Tb_switch_horizontal',
		},
	],
};

export const AxosWithForm: Story<FundingSourceProps & HTMLInputElement> = (
	args
) => {
	const onSubmit = (data) => {
		console.log(data);
	};

	const schema = Joi.object({
		UserAddress: Joi.any(),
	});

	return (
		<Form
			formId="form2"
			onSubmit={onSubmit}
			resolver={joiResolver(schema)}
			validationTrigger={IFormMode.onBlur}
			defaultValues={{
				fundingSource: [
					{
						key: 'd720377c-8c73-49a7-8ce9-b67bea15e281',
						bankAccount: {
							fundingMethod: {
								id: '09efbe60-4381-11ef-a494-33a21d88a864',
							},
							bankAccountType: 'Checking',
						},
						federalWithholdingPercent: null,
					},
				],
			}}
		>
			<FundingSource
				name="fundingSource"
				withForm={true}
				sourceTypes={sourceTypes}
				countryList={countryList}
				stateList={stateList}
				clientDob="1957-01-01"
				ssn="************"
				registrationType="IRA-SEP"
				enableACHIra={false}
				fundingSourceMetaData={fundingSourceMetaDataAxios}
				onFundingFeildChange={() => {}}
				enums={enums}
				error={[
					{
						transferType: {
							type: 'CustomWizardError',
							message: 'Required',
						},
						otherAccount: {
							clearingNumber: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							deliveringAccountTitleSameAsReceiving: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							registrationType: {
								id: {
									type: 'CustomWizardError',
									message: 'Required',
								},
							},
							firmName: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							firmAddress: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							firmPhoneNumber: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							accountNumber: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							accountTitle: {
								type: 'CustomWizardError',
								message: 'Required',
							},
						},
						securities: [
							{
								description: {
									type: 'CustomWizardError',
									message: 'Required',
									ref: {
										name: null,
									},
								},
							},
						],
					},
					{
						bankAccount: {
							bankAccountTitle: {
								type: 'custom',
								message: 'Required',
							},
							bankName: {
								type: 'custom',
								message: 'Required',
							},
						},
						startDate: 'ueeu',
						'otherAccount.payeeName': 'ueeu',
						'securities.1.fundAccountNumber': 'ueeu',
					},
				]}
				{...args}
			/>

			<Button type={HTMLButtonTypes.Submit} title="submit"></Button>

			<ButtonWrapper />
		</Form>
	);
};
export const TraidWithForm: Story<FundingSourceProps & HTMLInputElement> = (
	args
) => {
	const onSubmit = (data) => {
		console.log(data);
	};

	const schema = Joi.object({
		UserAddress: Joi.any(),
	});

	return (
		<Form
			formId="form2"
			onSubmit={onSubmit}
			resolver={joiResolver(schema)}
			validationTrigger={IFormMode.onBlur}
			defaultValues={{
				fundingSource: [
					{
						key: 'd720377c-8c73-49a7-8ce9-b67bea15e281',
						bankAccount: {
							fundingMethod: {
								id: '09efbe60-4381-11ef-a494-33a21d88a864',
							},
							bankAccountType: 'Checking',
						},
						federalWithholdingPercent: null,
					},
				],
			}}
		>
			<FundingSource
				name="fundingSource"
				withForm={true}
				sourceTypes={sourceTypes}
				countryList={countryList}
				stateList={stateList}
				clientDob="1957-01-01"
				ssn="************"
				registrationType="IRA-SEP"
				enableACHIra={false}
				fundingSourceMetaData={fundingSourceMetaDataTraid}
				enums={{
					...enums,
					AnnuityTransferOptions: [
						{
							optionLabel:
								'Full Immediate - Liquidate Annuity and transfer all cash',
							optionValue:
								'Full Immediate - Liquidate Annuity and transfer all cash',
						},
						{
							optionLabel:
								'Partial Immediate - Liquidate Annuity and transfer cash',
							optionValue:
								'Partial Immediate - Liquidate Annuity and transfer cash',
						},
						{
							optionLabel:
								'Full at Maturity - Liquidate Annuity and transfer all cash',
							optionValue:
								'Full at Maturity - Liquidate Annuity and transfer all cash',
						},
						{
							optionLabel:
								'Partial at Maturity - Liquidate Annuity and transfer cash',
							optionValue:
								'Partial at Maturity - Liquidate Annuity and transfer cash',
						},
					],
					SecuritiesTransferTypes: [
						{
							code: null,
							color: null,
							description: null,
							disabled: false,
							domainName: null,
							enumerationName: 'SecuritiesTransferTypes',
							icon: null,
							optionLabel: 'Certificate of Deposit (CD) Transfer',
							optionValue: 'Certificate of Deposit (CD) Transfer',
						},

						{
							code: null,
							color: null,
							description: null,
							disabled: false,
							domainName: null,
							enumerationName: 'SecuritiesTransferTypes',
							icon: null,
							id: '74e11a96-fa53-11ef-8035-6fae4b055767',
							isDefault: false,
							optionLabel: 'Annuity Company Transfer',
							optionOrder: 4,
							optionValue: 'Annuity Company Transfer',
							orgUnitCode: null,
						},

						{
							code: null,
							color: null,
							description: null,
							disabled: false,
							domainName: null,
							enumerationName: 'SecuritiesTransferTypes',
							icon: null,
							id: 'b59342ea-fa52-11ef-8035-9f3516a96796',
							isDefault: false,
							optionLabel: 'Brokerage/Trust Company Transfer',
							optionOrder: 2,
							optionValue: 'Brokerage/Trust Company Transfer',
							orgUnitCode: null,
						},
						{
							code: null,
							color: null,
							description: null,
							disabled: false,
							domainName: null,
							enumerationName: 'SecuritiesTransferTypes',
							icon: null,
							id: 'dd02997a-fa52-11ef-8035-abb8f7bec4e7',
							isDefault: false,
							optionLabel: 'Mutual Fund Company Transfer',
							optionOrder: 3,
							optionValue: 'Mutual Fund Company Transfer',
							orgUnitCode: null,
						},
					],
					CapitalAndDvidentOptions: [
						{
							optionLabel: 'Cash',
							optionValue: 'Cash',
						},
						{
							optionLabel: 'Reinvest',
							optionValue: 'Reinvest',
						},
					],
					CapitalGainsOptions: [
						{
							optionLabel: 'Cash',
							optionValue: 'Cash',
						},
						{
							optionLabel: 'Reinvest',
							optionValue: 'Reinvest',
						},
					],
				}}
				error={[
					{
						transferType: {
							type: 'CustomWizardError',
							message: 'Required',
						},
						otherAccount: {
							clearingNumber: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							deliveringAccountTitleSameAsReceiving: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							registrationType: {
								id: {
									type: 'CustomWizardError',
									message: 'Required',
								},
							},
							firmName: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							firmAddress: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							firmPhoneNumber: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							accountNumber: {
								type: 'CustomWizardError',
								message: 'Required',
							},
							accountTitle: {
								type: 'CustomWizardError',
								message: 'Required',
							},
						},
					},
					{
						bankAccount: {
							bankAccountTitle: {
								type: 'custom',
								message: 'Required',
							},
							bankName: {
								type: 'custom',
								message: 'Required',
							},
						},
						startDate: 'ueeu',
						'otherAccount.payeeName': 'ueeu',
						'securities.1.fundAccountNumber': 'ueeu',
					},
				]}
				{...args}
			/>

			<Button type={HTMLButtonTypes.Submit} title="submit"></Button>

			<ButtonWrapper />
		</Form>
	);
};
export const CitizensWithForm: Story<FundingSourceProps & HTMLInputElement> = (
	args
) => {
	const onSubmit = (data) => {};

	const schema = Joi.object({
		UserAddress: Joi.any(),
	});

	return (
		<Form
			formId="form2"
			onSubmit={onSubmit}
			resolver={joiResolver(schema)}
			validationTrigger={IFormMode.onBlur}
			defaultValues={{
				fundingSource: [
					{
						key: '762280b2-5c77-41b8-8f8a-1723074a262c',
						internalTransferType: 'Bank IRA CD/Savings Transfer',
						bankAccount: {
							fundingMethod: {
								id: '4dedc8c2-9102-11ef-9d10-3f4686ebe3c2',
							},
						},
						useForAccountFunding: true,
						bankAccountTransferType: 'Transfer at maturity date',
						cdMaturityDate: '2013-09-12',
					},
				],
			}}
		>
			<FundingSource
				name="fundingSource"
				withForm={true}
				sourceTypes={sourceTypes}
				countryList={countryList}
				stateList={stateList}
				ssn="************"
				enableACHIra={false}
				clientDob="1953-10-10"
				registrationType="IRA-INHERITED"
				deliveringAccount="qualified"
				receivingAccount="qualified"
				productType="Annuity"
				fundingSourceMetaData={fundingSourceMetaDataCitizen}
				iraContributionType="Direct Rollover from Employer Plan"
				enums={enums}
				// error={[
				// 	{
				// 		bankAccount: {
				// 			bankAccountTitle: {
				// 				type: 'custom',
				// 				message: 'Required',
				// 			},
				// 			bankName: {
				// 				type: 'custom',
				// 				message: 'Required',
				// 			},
				// 		},
				// 		startDate: 'ueeu',
				// 	},
				// ]}
				{...args}
			/>

			<Button type={HTMLButtonTypes.Submit} title="submit"></Button>

			<ButtonWrapper />
		</Form>
	);
};

const ButtonWrapper = () => {
	const context = useFormContext();
	return (
		<Button
			title="Reset"
			onClick={() => {
				context.reset();
			}}
		></Button>
	);
};

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

type CollapsedData = {
	icon: string;
	key: string;
	name: string;
	type?: string;
};

export type FormFieldConfig = {
	on: boolean;
	text: string;
	key: string;
	count?: number;
	enum?: string;
};
export type AccordianConfig = {
	on: boolean;
	text: string;
};

type TopLevelOption = {
	id: string;
	heading: string;
	options: any;
	optionsIRA?: any;
	key: string;
};

type ReceivingFirmInfo = {
	name: string;
	value: string;
	icon: string;
};

export type FundingSource = {
	on?: boolean;
	text?: string;
	topLevel?: TopLevelOption[];
	summary?: CollapsedData[];
	fieldConfigs?: Record<string, FormFieldConfig>;
	fieldProps?: Record<string, any>;
	accordianConfigs?: Record<string, AccordianConfig>;
	firmInformation?: ReceivingFirmInfo[];
	enableRoutingNumberBasedBank?: boolean;
};

export type FundingSourceMetaData = {
	version?: string;
	ach: FundingSource;
	check: FundingSource;
	wire_transfer: FundingSource;
	'external_transfer_-_acat': FundingSource;
	'external_transfer_-_non-acat': FundingSource;
	internal_transfer: FundingSource;
};
export interface IFundingSourceProps {
	id?: string;
	withForm?: boolean;
	value?: any;
	name?: string;
	onChange?: (data: any) => void;
	onChangeTrigger?: (data: any) => void;
	onFundingFeildChange?: (
		data: any,
		sourceIndex?: number,
		key?: string[]
	) => void;
	error?: any;
	isCreateUpdateData?: boolean;
	isComposition?: boolean;
	sourceTypes?: Array<SourceType>;
	productType?: string;
	clientDob?: string;
	containerStyles?: any;
	padding?: string;
	margin?: string;
	fundingSourceStyles?: any;
	showDelete?: boolean;
	enableAch: boolean;
	enableCheck: boolean;
	enableWireTransfer: boolean;
	enableIntTransfer: boolean;
	enableExtAcat: boolean;
	enableExtNonAcat: boolean;
	countryList?: any[];
	stateList?: any[];
	registrationType?: string;
	registrationTypes?: any[];
	ssn?: string;
	control?: any;
	setValue?: any;
	labelTransform?: LabelTransform;
	enableACHIra?: boolean;
	deliveringAccount?: string;
	receivingAccount?: string;
	apiService?: any;
	fundingSourceMetaData?: FundingSourceMetaData;
	iraContributionType?: string;
	verionOfMetadata?: string;
	showAlertPopup?: boolean;

	enums?: Record<
		string,
		Array<{
			optionLabel: string;
			optionValue: string | boolean;
			icon?: string;
		}>
	>;
}

export type SourceType = {
	id: string;
	fundingMethod?: string;
	icon?: string;
	selectedSourceId?: string;
};

export enum LabelTransform {
	Sentense_Case = 'unset',
	Capitalize = 'capitalize',
	Upper_Case = 'upperCase',
	Lower_Case = 'lowerCase',
}

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { IChoiceCardsProps } from './types';
import { CssProps, MetaData } from '@base/utils-hooks';

export const defaultProps: IChoiceCardsProps = {
	id: '1',
	data: [],
	multiSelect: false,
	fullWidth: false,
	hideIcon: false,
	hideDesc: false,
	width: '100px',
	numCols: 3,
};

export const configurationProps = {
	//any config params
	containerId: '', //carries default configuration if any
	targetMappingEnabled: true,
};

export const metadata: MetaData = {
	isPublishable: false,
	toolbarType: 'composite',
	description: 'This is metadata information for Choice cards component',
	configurationProps,
	cssProps: [
		{
			key: CssProps.COLOR,
			value: {
				props: 'textColor',
				propLabel: 'Text color',
				type: CssProps.COLOR,
				value: '',
			},
		},
		{
			key: CssProps.COLOR,
			value: {
				props: 'textColorSelected',
				propLabel: 'Text color when selected',
				type: CssProps.COLOR,
				value: '',
			},
		},
		{
			key: CssProps.BACKGROUND_COLOR,
			value: {
				props: 'cardBgColor',
				propLabel: 'Card background color',
				type: CssProps.COLOR,
				value: '',
			},
		},
		{
			key: CssProps.BACKGROUND_COLOR,
			value: {
				props: 'cardBgColorSelected',
				propLabel: 'Card background color when selected',
				type: CssProps.COLOR,
				value: '',
			},
		},
		{
			key: CssProps.BORDER,
			value: {
				props: 'cardBorder',
				propLabel: 'Card border',
				type: CssProps.BORDER,
				hasChildren: true,
				value: {
					cardBorderColor: '',
					cardBorderStyle: '',
				},
			},
		},
		{
			key: CssProps.BORDER,
			value: {
				props: 'cardBorderSelected',
				propLabel: 'Card border when selected',
				type: CssProps.BORDER,
				hasChildren: true,
				value: {
					cardBorderSelectedColor: '',
					cardBorderSelectedStyle: '',
				},
			},
		},
		{
			key: CssProps.COLOR,
			value: {
				props: 'fullwidthIconColor',
				propLabel: 'Icon color in full width mode',
				type: CssProps.COLOR,
				value: '',
			},
		},
		{
			key: CssProps.BACKGROUND_COLOR,
			value: {
				props: 'fullwidthIconBgColor',
				propLabel: 'Icon background color in full width mode',
				type: CssProps.COLOR,
				value: '',
			},
		},
		{
			key: CssProps.COLOR,
			value: {
				props: 'fullwidthCheckmarkColor',
				propLabel: 'Checkmark color in full width mode',
				type: CssProps.COLOR,
				value: '',
			},
		},
		{
			key: CssProps.BORDER_RADIUS,
			value: {
				props: 'borderRadius',
				propLabel: 'Border radius',
				type: CssProps.BORDER_RADIUS,
				value: '12px',
			},
		},
	],
	editableProps: {
		component: 'Choice cards',
		props: [
			{
				props: 'data',
				propLabel: 'Value',
				help: 'Set data source',
				allowedValues: [
					{
						show: true,
						default: 'Select value',
						dynamic: true,
						type: 'string',
					},
				],
			},
			{
				props: 'lookupValue',
				propLabel: 'Selected key',
				help: 'Sets lookupvalue',
				propertyType: 'attributeSelect',
				allowedValues: [
					{
						show: true,
						default: 'Select value',
						type: 'object',
					},
				],
			},
			{
				props: 'lookupLabel',
				propLabel: 'Title',
				help: 'Sets title',
				propertyType: 'attributeSelect',
				allowedValues: [
					{
						show: true,
						default: 'Select value',
						type: 'object',
					},
				],
			},
			{
				props: 'selectedValues',
				propLabel: 'Selected Values',
				help: 'Set selected value',
				allowedValues: [
					{
						show: true,
						default: '',
						dynamic: true,
						type: 'string',
					},
				],
			},
			{
				props: 'icon',
				propLabel: 'Icon',
				help: 'Sets icon',
				propertyType: 'attributeSelect',
				allowedValues: [
					{
						show: true,
						default: 'Select value',
						type: 'object',
					},
				],
			},
			{
				props: 'description',
				propLabel: 'Description (full width mode only)',
				help: 'Sets description',
				propertyType: 'attributeSelect',
				allowedValues: [
					{
						show: true,
						default: 'Select value',
						type: 'object',
					},
				],
			},
			{
				props: 'fullWidth',
				propLabel: 'Enable full width',
				help: 'Toggles enable if full width',
				allowedValues: [
					{
						show: true,
						default: defaultProps.fullWidth,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'hideIcon',
				propLabel: 'Hide icon',
				help: 'Toggles enable if Hide icon',
				allowedValues: [
					{
						show: true,
						default: defaultProps.hideIcon,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'hideDesc',
				propLabel: 'Hide description',
				help: 'Toggles enable if Hide description',
				allowedValues: [
					{
						show: false,
						default: defaultProps.hideDesc,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'multiSelect',
				propLabel: 'Enable multi selection',
				help: 'Toggles if multiple cards can be selected',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'numCols',
				propLabel: 'No of cards in a row',
				help: 'Sets number of cards per row',
				allowedValues: [
					{
						show: true,
						default: 3,
						type: 'number',
					},
				],
			},
			{
				props: 'width',
				propLabel: 'Card width',
				help: 'Sets card width',
				allowedValues: [
					{
						show: true,
						default: defaultProps.width,
						type: 'dimension',
						contstraint: {
							units: ['px'],
						},
					},
				],
			},
		],
		subComponents: [],
	},
	viewProps: [
		{
			name: 'dropdown',
			label: 'Dropdown',
			componentName: 'Dropdown',
			reference: 'atomic-dropdown',
			refArgs: {},
			fieldBindingKeyMap: {},
		},
		{
			name: 'checkbox',
			label: 'Checkbox',
			componentName: 'Checkboxgroup',
			reference: 'atomic-checkboxgroup',
			refArgs: {
				isMulti: true,
			},
			fieldBindingKeyMap: {
				data: 'options',
			},
			condition: {
				multiSelect: 'true',
			},
		},
		{
			name: 'radio',
			label: 'Radio',
			componentName: 'Radiogroup',
			reference: 'atomic-radiogroup',
			refArgs: {},
			fieldBindingKeyMap: {
				data: 'options',
			},
			condition: {
				multiSelect: 'false',
			},
		},
		{
			name: 'tabSwitcher',
			label: 'Tab switcher',
			componentName: 'Radiogroup',
			reference: 'atomic-radiogroup',
			refArgs: {
				isSegmentedTabs: true,
				isMulti: false,
			},
			fieldBindingKeyMap: {
				data: 'options',
			},
			condition: {
				multiSelect: 'false',
			},
		},
		{
			name: 'autocomplete',
			label: 'Autocomplete',
			componentName: 'Autocomplete',
			reference: 'atomic-autocomplete',
			fieldBindingKeyMap: {},
			refArgs: {
				acceptCustomValue: false,
				enableRemoteSearch: false,
				isMulti: false,
			},
			condition: {
				multiSelect: 'false',
			},
		},
	],
};

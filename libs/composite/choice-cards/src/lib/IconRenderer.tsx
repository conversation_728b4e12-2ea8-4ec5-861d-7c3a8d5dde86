import { FC, useEffect, useState } from 'react';
import Image from '@atomic/image';
import Icon, { IIconProps, IconCodes } from '@atomic/icon';
import { JiffyDriveService } from '@base/utils-hooks';
import { IChoiceCardsProps } from './types';
interface IconRendereProps extends IChoiceCardsProps {
	value: string;
	iconProps?: IIconProps;
}
export const IconRenderer: FC<IconRendereProps> = (props) => {
	const {
		iconDatatype,
		value,
		imageSize = 24,
		iconProps,
		baseUrl,
		tenantId,
		location,
	} = props;
	const [previewUrl, setPreviewUrl] = useState(null);
	const JiffyDrive = new JiffyDriveService(baseUrl, tenantId, location);

	useEffect(() => {
		if (iconDatatype?.toLowerCase() === 'file') {
			try {
				JiffyDrive.downloadFile(value)
					.then((response: any) => {
						const file = new Blob([response.data]);
						if (file) {
							// Revoke the previous URL to avoid memory leaks
							if (previewUrl) {
								URL.revokeObjectURL(previewUrl);
							}
							// Create a preview URL
							setPreviewUrl(URL.createObjectURL(file));
						}
					})
					.catch((Exception) => {
						console.log('Exception', Exception);
					});
			} catch (error) {
				console.log('Error while fetch file', error);
			}
		} else {
			setPreviewUrl(null);
		}
	}, [value, iconDatatype]);
	return iconDatatype?.toLowerCase() === 'file' ? (
		previewUrl && (
			<Image
				src={previewUrl}
				width={imageSize}
				height={imageSize}
				objectFit="cover"
				alt="loading"
			/>
		)
	) : (
		<Icon icon={IconCodes[value] ?? value} {...iconProps} />
	);
};

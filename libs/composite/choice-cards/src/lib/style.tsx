/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { ApexTheme } from '@base/theme';
import { createUseStyles } from 'react-jss';
import { ChoiceCardsProps } from './choice-cards';

export const styles = createUseStyles((theme: ApexTheme) => {
	const borderWidth = 2;
	const card = {
		display: 'flex',
		flexDirection: 'column',
		alignItems: 'center',
		gap: 8,
		paddingTop: 24,
		paddingBottom: 24,
		textAlign: 'center',
		borderRadius: 12,
		lineHeight: theme.lineHeights.desktop.bSmall,
		overflow: 'hidden',
		cursor: 'pointer',
	};
	const fullWidthCard = {
		...card,
		flexDirection: 'row',
		alignItems: '""',
		justifyContent: 'center',
		gap: 24,
		padding: 24,
		borderRadius: 8,
	};

	return {
		wrapper: (props: ChoiceCardsProps) => ({
			display: 'flex',
			flexDirection: 'column',
			gap: props.fullWidth ? 24 : 16,
			width: props?.data?.length
				? props.fullWidth
					? '100%'
					: 'fit-content'
				: '300px',
			fontFamily: theme.fontFamily,
			...(props.error
				? {
						border: `2px solid ${theme.colors.danger.default}`,
						borderRadius: props.fullWidth ? 10 : 14,
				  }
				: {}),
		}),
		row: {
			display: 'flex',
			flexDirection: 'row',
			justifyContent: 'center',
			gap: 16,
		},
		card: (props: ChoiceCardsProps) => ({
			...card,
			width: `calc(${props.width || '100px'} - ${2 * borderWidth}px)`,
			fontWeight: theme.fontWeights.regular,
			fontSize: theme.fontSizes.desktop.bSmall,
			color: props.textColor ? props.textColor : theme.colors.monochrome.ash,
			backgroundColor: props.cardBgColor
				? props.cardBgColor
				: theme.colors.monochrome.bg,
			borderStyle: props.cardBorderStyle ? props.cardBorderStyle : 'solid',
			borderWidth,
			borderColor: props.cardBorderColor
				? props.cardBorderColor
				: theme.colors.monochrome.bg,
			borderRadius: props.borderRadius ? props.borderRadius : 12,
		}),
		highlightedCard: (props: ChoiceCardsProps) => ({
			...card,
			width: `calc(${props.width || '100px'} - ${2 * borderWidth}px)`,
			fontWeight: theme.fontWeights.bold,
			fontSize: theme.fontSizes.desktop.bSmall,
			color: props.textColorSelected
				? props.textColorSelected
				: theme.colors.monochrome.ash,
			backgroundColor: props.cardBgColorSelected
				? props.cardBgColorSelected
				: theme.colors.primary.bg,
			borderStyle: props.cardBorderSelectedStyle
				? props.cardBorderSelectedStyle
				: 'solid',
			borderWidth,
			borderColor: props.cardBorderSelectedColor
				? props.cardBorderSelectedColor
				: theme.colors.primary.default,
			borderRadius: props.borderRadius ? props.borderRadius : 12,
		}),
		innerCard: {
			display: 'grid',
			alignContent: 'center',
			justifyContent: 'center',
			minHeight: theme.lineHeights.desktop.bSmall,
			height: '100%',
		},
		fullWidthCard: (props: ChoiceCardsProps) => ({
			...fullWidthCard,
			color: props.textColor ? props.textColor : theme.colors.monochrome.ash,
			backgroundColor: props.cardBgColor
				? props.cardBgColor
				: theme.colors.monochrome.white,
			borderStyle: props.cardBorderStyle ? props.cardBorderStyle : 'solid',
			borderWidth: 1,
			borderColor: props.cardBorderColor
				? props.cardBorderColor
				: theme.colors.monochrome.input,
			borderRadius: props.borderRadius ? props.borderRadius : 8,
		}),
		fullWidthHighlightedCard: (props: ChoiceCardsProps) => ({
			...fullWidthCard,
			color: props.textColorSelected
				? props.textColorSelected
				: theme.colors.monochrome.ash,
			backgroundColor: props.cardBgColorSelected
				? props.cardBgColorSelected
				: '#F2F0EC',
			borderStyle: props.cardBorderSelectedStyle
				? props.cardBorderSelectedStyle
				: 'solid',
			borderWidth: 1,
			borderColor: props.cardBorderSelectedColor
				? props.cardBorderSelectedColor
				: '#F2F0EC',
			borderRadius: props.borderRadius ? props.borderRadius : 8,
		}),
		iconContainer: {
			height: 22,
		},
		iconBg: (props: ChoiceCardsProps) => ({
			display: 'grid',
			alignContent: 'center',
			justifyContent: 'center',
			flex: '0 0 40px',
			height: 40,
			borderRadius: 10,
			backgroundColor: props.fullwidthIconBgColor
				? props.fullwidthIconBgColor
				: theme.colors.primary.light,
		}),
		info: {
			display: 'flex',
			flexDirection: 'column',
			gap: 16,
			width: '100%',
			minHeight: 32,
			justifyContent: 'center',
			textAlign: 'left',
		},
		title: {
			fontWeight: theme.fontWeights.semiBold,
			fontSize: theme.fontSizes.desktop.bSmall,
			lineHeight: theme.lineHeights.desktop.bSmall,
			minHeight: '15px',
		},
		description: {
			fontWeight: theme.fontWeights.regular,
			fontSize: theme.fontSizes.desktop.bSmall,
			lineHeight: theme.lineHeights.desktop.bMedium,
		},
	};
});

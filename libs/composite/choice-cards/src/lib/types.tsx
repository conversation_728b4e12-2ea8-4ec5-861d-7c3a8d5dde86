/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

export interface IChoiceCardsProps {
	id?: string;

	// list of enum objects
	data?: object[];

	// values already selected
	selectedValues?: string | string[];

	// key in data that contains value to be return
	lookupValue?: string;

	// key in data that contains title
	lookupLabel?: string;

	// key in data that contains icon
	icon?: string;

	// key in data that contains description
	description?: string;

	// width to be passed to each card (in px)
	width?: string;

	// fill row with one card
	fullWidth?: boolean;

	// toggles if icon should be displayed
	hideIcon?: boolean;

	// toggles if desc should be displayed
	hideDesc?: boolean;

	// number of columns to place in row
	numCols?: number;

	// whether multiple cards can be selected
	multiSelect?: boolean;

	// used with form
	withForm?: boolean;
	name?: string;
	onChange?: (any) => void;
	onChangeTrigger?: (any) => void;
	onItemClick?: any;
	error?: any;
	returnArrayObject?: boolean;
	iconDatatype?: string;
	imageSize?: number | string;
	baseUrl?: string;
	tenantId?: string;
	location?: string;

	// background color of the card
	cardBgColor?: string;

	// background color of the card when selected
	cardBgColorSelected?: string;

	// border color of the card
	cardBorderColor?: string;

	// border style of the card
	cardBorderStyle?: string;

	// border color of the card when selected
	cardBorderSelectedColor?: string;

	// border style of the card when selected
	cardBorderSelectedStyle?: string;

	// text color of the card
	textColor?: string;

	// text color of the card when selected
	textColorSelected?: string;

	// color of the icon in fullwidth mode
	fullwidthIconColor?: string;

	// background color of the icon in fullwidth mode
	fullwidthIconBgColor?: string;

	// color of the checkmark in fullwidth mode
	fullwidthCheckmarkColor?: string;

	// border radius of the card
	borderRadius?: string;
}

import <PERSON><PERSON> from 'joi';
import { joiResolver } from '@hookform/resolvers/joi';
import { Story, Meta } from '@storybook/react';
import Button, { HTMLButtonTypes } from '@atomic/button';
import Form, { IFormMode } from '@composite/form';
import ChoiceCards, { ChoiceCardsProps } from './choice-cards';
import { defaultProps } from './metadata';
import {
	genderData,
	descriptionData,
	manyOptionsData,
	iconOptionalData,
	occasionallyEmptyData,
} from './constants';
import { ContextmenuType } from '@composite/contextmenu';

export default {
	component: ChoiceCards,
	title: 'Composite/ChoiceCards',
	argTypes: {
		id: { type: 'number' },
	},
} as Meta;

const Template: Story<ChoiceCardsProps> = (args) => (
	<ChoiceCards
		{...args}
		onItemClick={(value) => {
			console.log('Selected', value);
		}}
	></ChoiceCards>
);

export const ChoiceCardsStory = Template.bind({});
ChoiceCardsStory.args = {
	...defaultProps,
	data: genderData,
	lookupLabel: 'optionLabel',
	icon: 'icon',
};

export const MultiSelectStory = Template.bind({});
MultiSelectStory.args = {
	...defaultProps,
	data: manyOptionsData,
	lookupLabel: 'optionLabel',
	icon: 'icon',
	numCols: 2,
	multiSelect: ContextmenuType.CHECKBOX,
	returnArrayObject: true,
};

export const FullWidthStory = Template.bind({});
FullWidthStory.args = {
	...defaultProps,
	data: descriptionData,
	lookupLabel: 'optionLabel',
	icon: 'icon',
	description: 'description',
	fullWidth: true,
};

export const EmptyStory = Template.bind({});
EmptyStory.args = {
	...defaultProps,
	data: [],
};

export const HiddenDescStory = Template.bind({});
HiddenDescStory.args = {
	...defaultProps,
	data: descriptionData,
	lookupLabel: 'optionLabel',
	icon: 'icon',
	description: 'description',
	fullWidth: true,
	hideDesc: true,
};

export const HiddenIconStory = Template.bind({});
HiddenIconStory.args = {
	...defaultProps,
	data: manyOptionsData,
	lookupLabel: 'optionLabel',
	icon: 'icon',
	hideIcon: true,
	multiSelect: true,
};

export const IconsOptionalStory = Template.bind({});
IconsOptionalStory.args = {
	...defaultProps,
	data: iconOptionalData,
	lookupLabel: 'optionLabel',
	icon: 'icon',
	numCols: 4,
};

export const WithSomeEmptyCardsStory = Template.bind({});
WithSomeEmptyCardsStory.args = {
	...defaultProps,
	data: occasionallyEmptyData,
	lookupLabel: 'optionLabel',
	icon: 'icon',
	hideIcon: true,
};

export const WithCustomStylesStory = Template.bind({});
WithCustomStylesStory.args = {
	...defaultProps,
	data: genderData,
	fullWidth: true,
	lookupLabel: 'optionLabel',
	icon: 'icon',
	cardBgColor: 'lightsalmon',
	cardBorderColor: 'red',
	cardBgColorSelected: 'lightgreen',
	cardBorderSelectedColor: 'blue',
	textColor: 'red',
	textColorSelected: 'blue',
	borderRadius: '24px',
};

export const WithFormSingleSelectStory = () => {
	const onSubmit = (data: unknown) => alert(JSON.stringify(data));

	const schema = Joi.object({
		choiceCards: Joi.string().required(),
	});
	return (
		<Form
			formId="form3"
			onSubmit={onSubmit}
			resolver={joiResolver(schema)}
			validationTrigger={IFormMode.onBlur}
			defaultValues={{ choiceCards: '' }}
		>
			<ChoiceCards
				data={genderData}
				lookupLabel={'optionLabel'}
				lookupValue={'id'}
				icon={'icon'}
				withForm
				name="choiceCards"
			/>
			<br />
			<Button type={HTMLButtonTypes.Submit} title="submit"></Button>
		</Form>
	);
};

export const WithFormMultiSelectStory = () => {
	const onSubmit = (data: unknown) => alert(JSON.stringify(data));

	const schema = Joi.object({
		choiceCards: Joi.array(),
	});
	return (
		<Form
			formId="form3"
			onSubmit={onSubmit}
			resolver={joiResolver(schema)}
			validationTrigger={IFormMode.onBlur}
			defaultValues={{ choiceCards: [{ optionLabel: 'Male' }] }}
		>
			<ChoiceCards
				data={genderData}
				lookupLabel={'optionLabel'}
				icon={'icon'}
				withForm
				name="choiceCards"
				multiSelect
				selectedValues={['Male']}
				returnArrayObject={true}
			/>
			<br />
			<Button type={HTMLButtonTypes.Submit} title="submit"></Button>
		</Form>
	);
};

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { IconCodes } from '@base/icon';
import {
	INavigationbarProps,
	NavigationbarType,
	NavigationStyle,
	VariationTypes,
	NavigationbarNameType,
	CustomFontWeights,
} from './types';

import { CssProps, MetaData } from '@base/utils-hooks';
import { jiffy as theme } from '@base/theme';

export const defaultProps: INavigationbarProps = {
	id: '1',
	navigationbarType: NavigationbarType.Primary,
	navigationStyle: NavigationStyle.trimmed,
	variation: VariationTypes.DEFAULT,
	quickpanelOptionIcon: IconCodes.icon_Tb_arrow_autofit_up,
	quickpanelOptionText: 'Quick Panel',
};

export const metadata: MetaData = {
	description: 'This is a help text',
	defaultBehavior: {
		onClick: 'when navigationbar is Clicked\nDo something\n',
		onLoad: 'when navigationbar is Loaded\nDo something\n',
		onUnload: 'when navigationbar is Refreshed\nDo something\n',
		onHover: 'when navigationbar is Hovered\nDo something\n',
		onValueChange: 'when navigationbar Value changes\nDo something\n',
	},
	thumbnail: 'component-thumbnails/nav-bar.png',
	configurationProps: {
		containerId: '',
	},
	cssProps: [
		{
			key: CssProps.BACKGROUND_COLOR,
		},
		{
			key: CssProps.MENU_COLOR,
		},
		{
			key: CssProps.SELECTED_MENU_BACKGROUND,
		},
		{
			key: CssProps.SELECTED_MENU_COLOR,
		},
		{
			key: CssProps.COLOR,
			value: {
				props: 'quickpanelOptionColor',
				propLabel: 'Quick Panel Option Color',
				value: '',
				type: 'color',
			},
		},
		{
			key: CssProps.BACKGROUND_COLOR,
			value: {
				props: 'quickpanelOptionBackground',
				propLabel: 'Quick Panel Option Background',
				value: theme.colors.primary.dark,
				type: 'color',
			},
		},
		{
			key: CssProps.BACKGROUND_COLOR,
			value: {
				props: 'quickpanelOptionHoverBackground',
				propLabel: 'Quick Panel Option Hover Background',
				value: theme.colors.primary.default,
				type: 'color',
			},
		},
	],
	editableProps: {
		component: 'Navigationbar',
		allowedChildrenComponents: ['none'],
		props: [
			{
				props: 'title',
				propLabel: 'Title',
				help: 'Helps to define title',
				allowedValues: [
					{
						show: true,
						default: '',
						type: 'string',
					},
				],
			},
			{
				props: 'navigationbarType',
				propLabel: 'Type',
				help: 'Helps to define navigationbar Type',
				allowedValues: [
					{
						show: true,
						default: NavigationbarNameType.Overlay_Sidebar,
						values: NavigationbarNameType,
						type: typeof NavigationbarNameType,
					},
				],
			},
			{
				props: 'isCollapsed',
				propLabel: 'Is collapsed',
				help: 'Helps to define isCollapsed',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'iconSize',
				propLabel: 'Icon size',
				help: 'Helps to define menu icon size',
				allowedValues: [
					{
						show: true,
						default: '20px',
						type: 'dimension',
						contstraint: {
							units: ['px'],
							min: 14,
							max: 30,
						},
					},
				],
			},
			{
				props: 'selectedMenuFontSize',
				propLabel: 'Selected menu font size',
				help: 'Helps to define selected menu font size',
				allowedValues: [
					{
						show: true,
						default: '16px',
						type: 'dimension',
						contstraint: {
							units: ['px'],
							min: 14,
							max: 30,
						},
					},
				],
			},
			{
				props: 'subMenuFontSize',
				propLabel: 'Sub menu font size',
				help: 'Helps to define sub menu font size',
				allowedValues: [
					{
						show: true,
						default: '14px',
						type: 'dimension',
						contstraint: {
							units: ['px'],
							min: 14,
							max: 30,
						},
					},
				],
			},
			{
				props: 'selectedMenuFontWeight',
				propLabel: 'Selected menu font weight',
				help: 'Helps to define selected menu font weight',
				allowedValues: [
					{
						show: true,
						default: CustomFontWeights.regular,
						values: CustomFontWeights,
						type: typeof CustomFontWeights,
					},
				],
			},
			{
				props: 'enableQuickPanel',
				propLabel: 'Enable Quick Panel',
				help: 'Helps to define enableQuickPanel',
				allowedValues: [
					{
						show: true,
						default: false,
						type: 'boolean',
					},
				],
			},
			{
				props: 'quickpanelOptionText',
				propLabel: 'Quick Panel Option Text',
				help: 'Helps to define quickpanelOptionText',
				allowedValues: [
					{
						show: true,
						default: 'Quick Panel',
						type: 'string',
					},
				],
			},
			{
				props: 'quickpanelOptionIcon',
				propLabel: 'Quick Panel Option Icon',
				help: 'Helps to define quickpanelOptionIcon',
				allowedValues: [
					{
						show: true,
						default: IconCodes.icon_Tb_arrow_autofit_up,
						type: 'icon',
					},
				],
			},
		],
	},
	toolbarType: '',
};

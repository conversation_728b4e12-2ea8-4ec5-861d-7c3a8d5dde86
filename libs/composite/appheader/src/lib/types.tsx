/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */
import { ReactNode } from 'react';
import { BoxProps } from '@atomic/box';
import { OverflowProps, OverflowItemProps } from '@atomic/overflow';

export enum Types {
	DEFAULT = 'Default',
	MODERN = 'Modern',
}
export enum NavigationMenuStyle {
	OnlyIcon = 'onlyicon',
	OnlyText = 'onlytext',
	TextWithIcon = 'textwithicon',
}

export interface IAppheaderProps {
	id?: string;
	children?: ReactNode;
	headerBackgroundColor?: string;
	headerWidth?: string;
	headerHeight?: string;
	headerContainerProps?: BoxProps;
	brandingBarProps?: BoxProps;
	buttonBarProps?: BoxProps;
	menuBarProps?: BoxProps;
	searchBarProps?: BoxProps;
	userInfoBarProps?: BoxProps;
	menuItemProps?: BoxProps;
	overflowLabel?: string;
	menuBarOverflowProps?: OverflowProps;
	menuBarOverflowItemProps?: OverflowItemProps;
	buttonBarOverflowProps?: OverflowProps;
	buttonBarOverflowItemProps?: OverflowItemProps;
	type?: Types;
	logoUrl?: string;
	logoTitle?: string;
	logoHeight?: string | number;
	logoWidth?: string | number;
	username?: string;
	firstName?: string;
	lastName?: string;
	lastLogin?: string;
	className?: string;
	logoEnabled?: boolean;
	appNameEnabled?: boolean;
	menuEnabled?: boolean;
	userProfileEnabled?: boolean;
	logoutButtonEnabled?: boolean;
	history?: Record<string, any>; // DEPRECATED:- Used for supporting old ui.
	backgroundColor?: string;
	menuColor?: string;
	selectedMenuColor?: string;
	selectedMenuBackground?: string;
	menuItemMargin?: string;
	alignMenuItem?: string;
	appNameLineHeight?: string;
	appNameLetterSpacing?: string;
	appNameFontSize?: string;
	appNameFontWeight?: string;
	appNameFontFamily?: string;
	appNameColor?: string;
	appNameVerticalAlignment?: string;
	userNameLineHeight?: string;
	userNameLetterSpacing?: string;
	userNameFontSize?: string;
	userNameFontWeight?: string;
	userNameFontFamily?: string;
	userNameColor?: string;
	userNameVerticalAlignment?: string;
	onItemClick?: (item: string) => void;
	enableExpandMode?: boolean;
	expandModeHeight?: string;
	menuStyle?: NavigationMenuStyle;
	orghiearchyEnabled?: boolean;
	serviceInstance?: any;
	onHiearchySelect?: (data: any) => void;
	orgHiearchyIcon?: string;
	profileBoxBackgroundColor?: string;
	userProfileMenuEnabled?: boolean;
	displayNameEnable?: boolean;
	userName: string;
	passwordExpireEnabled?: boolean;
	lastLoginEnabled?: boolean;
	defaultOrgValue?: any;
	token?: string;
	notificationEnabled?: boolean;
	accountSettingsNavigation?: string;
	enableAccountSettingsButton?: boolean;
	isNavigateToNewTab?: boolean;
	accountSettingsLinkName?: string;
	navigateCallback: (path: any, navigationVariables: any) => void;
	pageList: any;
	userId?: string;
	applicationId?: string;
	borderWidth?: string;
	borderColor?: string;
	borderStyle?: string;
	navigate?: any;
	dateFormat?: string;
}

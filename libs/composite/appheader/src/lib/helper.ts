export const getParsedJwt = (token) => {
	try {
		const base64Url = token.split('.')[1] || '';
		const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
		const jsonPayload =
			decodeURIComponent(
				atob(base64)
					.split('')
					.map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
					.join('')
			) || '{}';

		return JSON.parse(jsonPayload);
	} catch (e) {
		return {};
	}
};

export const calculateRemainingTime = (tocken = '') => {
	const parsedToken = getParsedJwt(tocken);
	const expiryTimestamp = parsedToken?.exp;
	const currentTimestamp = Math.floor(Date.now() / 1000);
	const remainingMilliseconds = expiryTimestamp - currentTimestamp;

	if (remainingMilliseconds < 0) {
		return `${0} seconds`;
	}

	const secondsInADay = 24 * 60 * 60;
	const secondsInAnHour = 60 * 60;
	const minituesAHour = 60;

	const days = Math.floor(remainingMilliseconds / secondsInADay);
	const hours = Math.floor(
		(remainingMilliseconds % secondsInADay) / secondsInAnHour
	);
	const minitues = Math.floor(
		(remainingMilliseconds % secondsInAnHour) / minituesAHour
	);

	let expiryTime = '';
	if (!isNaN(days) && days > 0) {
		expiryTime = `${expiryTime === '' ? days : ', ' + days} ${
			days === 1 ? 'Day' : 'Days'
		}`;
	}
	if (!isNaN(hours) && hours > 0) {
		expiryTime =
			expiryTime +
			`${expiryTime === '' ? hours : ', ' + hours} ${
				hours === 1 ? 'Hour' : 'Hours'
			}`;
	}
	if (!isNaN(minitues)) {
		expiryTime =
			expiryTime +
			`${expiryTime === '' ? minitues : ', ' + minitues} ${
				minitues === 1 ? 'Minute' : 'Minutes'
			}`;
	}
	return expiryTime;
};
export const calculateRemainLoginTime = (timestamp: any, dateFormat?: any) => {
	// Convert timestamp to a number if it is a string
	const numericTimestamp =
		typeof timestamp === 'string' ? Number(timestamp) : timestamp;

	// Create a Date object
	const date = new Date(numericTimestamp);

	// Check if the date is valid
	if (isNaN(date.getTime())) {
		return undefined;
	}
	// Format date part based on dateFormat prop
	let datePart = '';

	if (dateFormat) {
		// Simple replacements for common tokens
		datePart = dateFormat
			.replace(/yyyy|YYYY|yy|YY/, date.getFullYear().toString())
			.replace(/mm|MM/, String(date.getMonth() + 1).padStart(2, '0'))
			.replace(/dd|DD/, String(date.getDate()).padStart(2, '0'));
	} else {
		// Default: MM/DD/YYYY
		const formatter = new Intl.DateTimeFormat('en-US', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
		});
		const [month, day, year] = formatter
			.formatToParts(date)
			.reduce((acc, part) => {
				if (part.type === 'month') acc[0] = part.value;
				if (part.type === 'day') acc[1] = part.value;
				if (part.type === 'year') acc[2] = part.value;
				return acc;
			}, [] as string[]);
		datePart = `${month}/${day}/${year}`;
	}

	// Format time part as HH:mm:ss (24-hour) in browser's local time
	const timePart = date.toLocaleTimeString('en-GB', {
		hour12: false,
	});

	// Get timezone abbreviation in browser's local time
	const tzFormatter = new Intl.DateTimeFormat('en-US', {
		timeZoneName: 'short',
	});
	const tz =
		tzFormatter.formatToParts(date).find((part) => part.type === 'timeZoneName')
			?.value || '';

	return `${datePart} ${timePart} ${tz}`;
};

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import React, {
	FC,
	memo,
	ReactNode,
	useCallback,
	useEffect,
	useState,
} from 'react';
import { useTheme } from 'react-jss';
import { ApexTheme } from '@base/theme';
import { configurable } from '@base/config-provider';
import { setMetaData } from '@base/utils-hooks';
import { defaultProps, metadata } from './metadata';
import { IAppheaderProps, Types } from './types';
import Box from '@atomic/box';
import Branding from './component/Branding';
import MenuItem from './component/MenuItem';
import Text, { TextSizes } from '@atomic/text';
import Image from '@atomic/image';
import UserInfoBar from './component/UserInfoBar';
import Avatar, { AvatarTypes, LabelPositions } from '@atomic/avatar';
import Icon, { IconCodes } from '@atomic/icon';
import { AppheaderExpanded } from './appheaderexpanded';
import { AppHeaderExpandViewComponent } from './constants';
import Orghiearchy, { OrghiearchyType } from '@composite/orghiearchy';
import BellNotification from '@composite/bell-notification';

import UserProfileCard from './component/UserProfileCard';
import { calculateRemainingTime, calculateRemainLoginTime } from './helper';

export const sanitizeObject = (source: Record<string, any>) => {
	const response = {};
	for (const key in source) {
		if (source[key] !== undefined && source[key] !== null) {
			response[key] = source[key];
		}
	}
	return response;
};

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface AppheaderProps extends IAppheaderProps {}

const Appheader: FC<AppheaderProps> = memo((props: AppheaderProps) => {
	setMetaData('Appheader', metadata);
	const theme = useTheme<ApexTheme>();

	const {
		id,
		children,
		headerContainerProps,
		headerBackgroundColor,
		headerWidth,
		headerHeight,
		type,
		logoUrl,
		logoTitle = 'App Name',
		username,
		firstName,
		lastName,
		logoHeight,
		logoWidth,
		className,
		logoEnabled,
		appNameEnabled,
		menuEnabled,
		userProfileEnabled,
		logoutButtonEnabled,
		brandingBarProps,
		menuBarProps,
		userInfoBarProps,
		history, // DEPRECATED:- Will be removed when old UI support is removed
		backgroundColor,
		appNameLineHeight,
		appNameLetterSpacing,
		appNameFontSize,
		appNameFontWeight,
		appNameFontFamily,
		appNameColor,
		appNameVerticalAlignment,
		userNameLineHeight,
		userNameLetterSpacing,
		userNameFontSize,
		userNameFontWeight,
		userNameFontFamily,
		userNameColor,
		userNameVerticalAlignment,
		onItemClick,
		enableExpandMode,
		menuStyle,
		orghiearchyEnabled,
		serviceInstance,
		onHiearchySelect,
		defaultOrgValue,
		profileBoxBackgroundColor,
		lastLogin,
		userProfileMenuEnabled,
		displayNameEnable,
		userName,
		lastLoginEnabled,
		passwordExpireEnabled,
		accountSettingsNavigation,
		enableAccountSettingsButton,
		isNavigateToNewTab,
		accountSettingsLinkName,
		orgHiearchyIcon,
		token,
		notificationEnabled,
		navigateCallback,
		pageList,
		userId,
		applicationId,
		borderWidth,
		borderColor,
		borderStyle,
		navigate,
		dateFormat,
		...rest
	} = props;
	const [isProfileOpen, setProfileOpen] = useState(false);
	const [passwordExpires, setPasswordExpires] = useState('');
	const [lastLoginTime, setLastLoginTime] = useState('');
	const childrenArray = React.Children.toArray(children).filter(
		(child) =>
			child.props.componentDetails?.reference !==
				AppHeaderExpandViewComponent ||
			child.type.displayName !== AppHeaderExpandViewComponent
	);
	useEffect(() => {
		if (isProfileOpen) {
			const expiresTime = calculateRemainingTime(token);
			setPasswordExpires(expiresTime);
			const lastLoginTime = calculateRemainLoginTime(lastLogin, dateFormat);
			setLastLoginTime(lastLoginTime);
		}
	}, [token, isProfileOpen, lastLogin, dateFormat]);

	const onLogoutClick = () => {
		if (onItemClick) {
			onItemClick('/logout');
		}
		// Remove the below when old UI support is removed
		history?.push('/logout');
	};

	const onAccountSettingsClick = (nav: string) => {
		if (isNavigateToNewTab === true) {
			// Open in a new tab
			window.open(nav, '_blank');
		} else if (isNavigateToNewTab === false) {
			if (onItemClick) {
				onItemClick(nav);
			}
			// Remove the below when old UI support is removed
			history?.push(nav);
		}
	};

	const appNameProperties = sanitizeObject({
		lineHeight: appNameLineHeight,
		letterSpacing: appNameLetterSpacing,
		fontSize: appNameFontSize,
		fontWeight: appNameFontWeight,
		fontFamily: appNameFontFamily,
		color: appNameColor,
		alignItems: appNameVerticalAlignment,
	});

	const userNameProperties = sanitizeObject({
		lineHeight: userNameLineHeight,
		letterSpacing: userNameLetterSpacing,
		fontSize: userNameFontSize,
		fontWeight: userNameFontWeight,
		fontFamily: userNameFontFamily,
		color: userNameColor,
		alignItems: userNameVerticalAlignment,
	});
	const borderProps = {
		...(borderWidth && {
			borderWidth: borderWidth,
			borderStyle: borderStyle || 'solid',
		}),
		...(borderColor && { borderColor: borderColor }),
	};

	const _navigateCallback = useCallback(
		(path: string) => {
			// trim /page/ or /pages/ to find page name
			let page;
			const _path = path.replace(/\/?pages?\//, '');
			const _arr = _path.split('/');
			const pageName = _arr?.[0];
			for (const item of pageList) {
				if (item?.name === pageName) {
					page = item;
				}
			}
			if (page) {
				navigateCallback(page, _arr?.[1]);
			}
		},
		[navigateCallback, pageList]
	);

	if (type && enableExpandMode) {
		return <AppheaderExpanded {...props} />;
	}

	if (type === Types.MODERN) {
		// const menuItems = React.Children.toArray(children);
		const navMenuItems = React.Children.map(children, (item) => {
			const child: any = item;
			return React.cloneElement(child, {
				...rest,
				backgroundColor,
				menuStyle,
			});
		}).filter(
			(child: any) =>
				child.props.componentDetails?.reference !==
					AppHeaderExpandViewComponent &&
				child.type.displayName !== AppHeaderExpandViewComponent
		);

		return (
			<Box
				data-component="composite/appheader"
				id={id}
				justifyContent={
					logoEnabled || appNameEnabled || menuEnabled
						? 'space-between'
						: 'flex-end'
				}
				gap={10}
				zIndex={100}
				width={headerWidth || '100%'}
				height={headerHeight || 'auto'}
				backgroundColor={
					backgroundColor ||
					headerBackgroundColor ||
					theme.colors.monochrome.offWhite
				}
				padding="8px 24px"
				position="sticky"
				top={0}
				left={0}
				{...borderProps}
				{...headerContainerProps}
				className={className}
			>
				{(logoEnabled || appNameEnabled) && (
					<Branding brandingBarProps={brandingBarProps}>
						{logoEnabled ? (
							<MenuItem>
								<Image src={logoUrl} width={logoWidth} height={logoHeight} />
							</MenuItem>
						) : (
							<></>
						)}
						{appNameEnabled ? (
							<MenuItem menuItemProps={{ height: '100%' }}>
								<Text
									size={TextSizes.Medium}
									fontWeight={700}
									color={theme.colors.primary.default}
									whiteSpace="nowrap"
									overflow="hidden"
									textOverflow="ellipsis"
									display="flex"
									height="100%"
									alignItems="center"
									{...appNameProperties}
								>
									{logoTitle === 'CNTX_sys?.xJiffyAppDisplayName'
										? ''
										: logoTitle}
								</Text>
							</MenuItem>
						) : (
							<></>
						)}
					</Branding>
				)}
				{menuEnabled && <Box {...menuBarProps}>{navMenuItems}</Box>}
				{(userProfileEnabled || logoutButtonEnabled) && (
					<UserInfoBar userInfoBarProps={userInfoBarProps}>
						{notificationEnabled ? (
							<MenuItem>
								<BellNotification
									apiService={serviceInstance}
									navigateCallback={_navigateCallback}
									applicationId={applicationId}
									navigate={navigate}
								/>
							</MenuItem>
						) : (
							<></>
						)}
						{orghiearchyEnabled ? (
							<MenuItem>
								<Orghiearchy
									serviceInstance={serviceInstance}
									type={OrghiearchyType.BREADCRUMB}
									triggerType="icon"
									onHiearchySelect={onHiearchySelect}
									defaultValue={defaultOrgValue}
									orgHiearchyIcon={orgHiearchyIcon}
									userId={userId}
								/>
							</MenuItem>
						) : (
							<></>
						)}
						{logoutButtonEnabled && !userProfileMenuEnabled ? (
							<MenuItem>
								<Box margin="auto" cursor="pointer" onClick={onLogoutClick}>
									<Icon
										icon={IconCodes.icon_Bd_Log_Out_Circle}
										color={props.menuColor}
									/>
								</Box>
							</MenuItem>
						) : (
							<></>
						)}
						{userProfileEnabled && (
							<MenuItem>
								{userProfileMenuEnabled ? (
									<UserProfileCard
										triggerElement={
											<Box
												onClick={() => setProfileOpen(!isProfileOpen)}
												cursor="pointer"
											>
												<Avatar
													firstName={firstName}
													lastName={lastName}
													labelPosition={LabelPositions.Left}
													type={
														displayNameEnable
															? AvatarTypes.AvatarWithLabel
															: AvatarTypes.Avatar
													}
													enableStatus={false}
													imageUrl={''}
													size={'32px'}
													enableStory={false}
													avatarContainerProps={{
														margin: 0,
														onClick: () => setProfileOpen(!isProfileOpen),
													}}
													labelProps={{
														size: TextSizes.Small,
														lineHeight: 0,
														textAlign: 'center',
														textTransform: 'capitalize',
														alignItems: 'center',
														...userNameProperties,
													}}
												/>
											</Box>
										}
										isProfileOpen={isProfileOpen}
										setProfileOpen={setProfileOpen}
										handleLogout={onLogoutClick}
										handleAccountSettings={onAccountSettingsClick}
										firstName={firstName}
										lastName={lastName}
										hideFirstName={false}
										hideLastName={false}
										accountSettingsNavigation={accountSettingsNavigation}
										enableAccountSettingsButton={enableAccountSettingsButton}
										isNavigateToNewTab={isNavigateToNewTab}
										accountSettingsLinkName={accountSettingsLinkName}
										logoutButtonEnabled={logoutButtonEnabled}
										profileBoxBackgroundColor={profileBoxBackgroundColor}
										lastLogin={lastLoginTime}
										passwordExpires={passwordExpires}
										userName={userName}
										lastLoginEnabled={lastLoginEnabled}
										passwordExpireEnabled={passwordExpireEnabled}
									/>
								) : (
									<Avatar
										type={
											displayNameEnable
												? AvatarTypes.AvatarWithLabel
												: AvatarTypes.Avatar
										}
										labelPosition={LabelPositions.Left}
										meta=""
										enableStatus={false}
										enableStory={false}
										size="32px"
										avatarContainerProps={{ margin: 0 }}
										firstName={firstName}
										lastName={lastName}
										imageUrl=""
										labelProps={{
											size: TextSizes.Small,
											lineHeight: 0,
											textAlign: 'center',
											textTransform: 'capitalize',
											alignItems: 'center',
											...userNameProperties,
										}}
									/>
								)}
							</MenuItem>
						)}
					</UserInfoBar>
				)}
			</Box>
		);
	}

	return (
		<Box
			data-component="composite/appheader"
			id={id}
			justifyContent="flex-end"
			gap={10}
			zIndex={100}
			width={headerWidth || '100%'}
			height={headerHeight || 'auto'}
			backgroundColor={
				headerBackgroundColor || theme.colors.monochrome.offWhite
			}
			padding="8px 24px"
			position="sticky"
			top={0}
			left={0}
			{...borderProps}
			{...headerContainerProps}
			className={className}
		>
			{React.Children.map(children, (step, index) => {
				const isLastChild = childrenArray.length - 1 === index;
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				const child: any = step;
				if (childrenArray.length > 4) {
					return;
				} else {
					return React.cloneElement(child, {
						childIndex: index + 1,
						childrenLength: childrenArray.length,
						isLastChild,
						...rest,
					});
				}
			}).filter((child) => child.type.name !== AppHeaderExpandViewComponent)}
		</Box>
	);
});

Appheader.defaultProps = defaultProps;
Appheader.displayName = 'Appheader';

export default configurable(Appheader, 'Appheader');

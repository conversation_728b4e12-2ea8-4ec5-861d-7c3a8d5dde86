/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { ReactElement, useEffect, useState } from 'react';
import { useTheme } from 'react-jss';
import { ApexTheme } from '@base/theme';
import { IAppheaderProps } from './types';
import Box from '@atomic/box';
import Branding from './component/Branding';
import MenuItem from './component/MenuItem';
import Text, { TextSizes } from '@atomic/text';
import Image from '@atomic/image';
import UserInfoBar from './component/UserInfoBar';
import Avatar, { AvatarTypes, LabelPositions } from '@atomic/avatar';
import Icon, { IconCodes } from '@atomic/icon';
import { sanitizeObject } from './appheader';
import { AppHeaderExpandViewComponent } from './constants';
import Orghiearchy, { OrghiearchyType } from '@composite/orghiearchy';
import UserProfileCard from './component/UserProfileCard';
import { calculateRemainingTime, calculateRemainLoginTime } from './helper';
import BellNotification from '@composite/bell-notification';

interface AppheaderExpandedProps extends IAppheaderProps {}

export const AppheaderExpanded = (props: AppheaderExpandedProps) => {
	const theme = useTheme<ApexTheme>();

	const {
		id,
		children,
		headerContainerProps,
		headerBackgroundColor,
		headerWidth,
		headerHeight,
		type,
		logoUrl,
		logoTitle = 'App Name',
		username,
		firstName,
		lastName,
		logoHeight,
		logoWidth,
		className,
		logoEnabled,
		appNameEnabled,
		menuEnabled,
		userProfileEnabled,
		logoutButtonEnabled,
		brandingBarProps,
		menuBarProps,
		userInfoBarProps,
		history, // DEPRECATED:- Will be removed when old UI support is removed
		backgroundColor,
		appNameLineHeight,
		appNameLetterSpacing,
		appNameFontSize,
		appNameFontWeight,
		appNameFontFamily,
		appNameColor,
		appNameVerticalAlignment,
		userNameLineHeight,
		userNameLetterSpacing,
		userNameFontSize,
		userNameFontWeight,
		userNameFontFamily,
		userNameColor,
		userNameVerticalAlignment,
		onItemClick,
		expandModeHeight,
		menuStyle,
		defaultOrgValue,
		orghiearchyEnabled,
		serviceInstance,
		onHiearchySelect,
		orgHiearchyIcon,
		profileBoxBackgroundColor,
		lastLogin,
		token,
		userProfileMenuEnabled,
		notificationEnabled,
		accountSettingsNavigation,
		enableAccountSettingsButton,
		accountSettingsLinkName,
		isNavigateToNewTab,
		userName,
		displayNameEnable,
		lastLoginEnabled,
		passwordExpireEnabled,
		userId,
		dateFormat,
		...rest
	} = props;
	const childrenArray = React.Children.toArray(children);
	const [isProfileOpen, setProfileOpen] = useState(false);
	const [passwordExpires, setPasswordExpires] = useState('');
	const [lastLoginTime, setLastLoginTime] = useState('');
	const onLogoutClick = () => {
		if (onItemClick) {
			onItemClick('/logout');
		}
		// Remove the below when old UI support is removed
		history?.push('/logout');
	};
	const onAccountSettingsClick = (nav: string) => {
		if (isNavigateToNewTab === true) {
			// Open in a new tab
			window.open(nav, '_blank');
		} else if (isNavigateToNewTab === false) {
			if (onItemClick) {
				onItemClick(nav);
			}
			// Remove the below when old UI support is removed
			history?.push(nav);
		}
	};

	const appNameProperties = sanitizeObject({
		lineHeight: appNameLineHeight,
		letterSpacing: appNameLetterSpacing,
		fontSize: appNameFontSize,
		fontWeight: appNameFontWeight,
		fontFamily: appNameFontFamily,
		color: appNameColor,
		alignItems: appNameVerticalAlignment,
	});

	const userNameProperties = sanitizeObject({
		lineHeight: userNameLineHeight,
		letterSpacing: userNameLetterSpacing,
		fontSize: userNameFontSize,
		fontWeight: userNameFontWeight,
		fontFamily: userNameFontFamily,
		color: userNameColor,
		alignItems: userNameVerticalAlignment,
	});

	const ExpandedBox = childrenArray.find(
		(child: any) =>
			child.props.componentDetails?.reference ===
				AppHeaderExpandViewComponent ||
			child.type.displayName === AppHeaderExpandViewComponent
	);
	useEffect(() => {
		if (isProfileOpen) {
			const expiresTime = calculateRemainingTime(token);
			setPasswordExpires(expiresTime);
			const lastLoginTime = calculateRemainLoginTime(lastLogin, dateFormat);
			setLastLoginTime(lastLoginTime);
		}
	}, [token, isProfileOpen, lastLogin, dateFormat]);

	// const menuItems = React.Children.toArray(children);
	let navMenuItems = childrenArray.find(
		(child: any) =>
			child.props.componentDetails?.reference !==
				AppHeaderExpandViewComponent &&
			child.type.displayName !== AppHeaderExpandViewComponent
	) as ReactElement;
	navMenuItems = React.cloneElement(navMenuItems, {
		...rest,
		backgroundColor,
		type: 'tabview',
		menuStyle,
	});
	return (
		<Box
			data-component="composite/appheader"
			id={id}
			//gap={10}
			zIndex={100}
			backgroundColor={
				backgroundColor ||
				headerBackgroundColor ||
				theme.colors.monochrome.offWhite
			}
			padding="0px 24px 8px 24px"
			position="sticky"
			top={0}
			left={0}
			height={expandModeHeight}
			className={className}
			flexDirection="column"
			//justifyContent="space-between"
		>
			<Box
				justifyContent={
					logoEnabled || appNameEnabled || menuEnabled
						? 'space-between'
						: 'flex-end'
				}
				width={headerWidth || '100%'}
				height={headerHeight || '60px'}
				{...headerContainerProps}
			>
				{(logoEnabled || appNameEnabled) && (
					<Branding brandingBarProps={brandingBarProps}>
						{logoEnabled ? (
							<MenuItem>
								<Image src={logoUrl} width={logoWidth} height={logoHeight} />
							</MenuItem>
						) : (
							<></>
						)}
						{appNameEnabled ? (
							<MenuItem menuItemProps={{ height: '100%' }}>
								<Text
									size={TextSizes.Medium}
									fontWeight={700}
									color={theme.colors.primary.default}
									whiteSpace="nowrap"
									overflow="hidden"
									textOverflow="ellipsis"
									display="flex"
									height="100%"
									alignItems="center"
									{...appNameProperties}
								>
									{logoTitle === 'CNTX_sys?.xJiffyAppDisplayName'
										? ''
										: logoTitle}
								</Text>
							</MenuItem>
						) : (
							<></>
						)}
					</Branding>
				)}
				{(userProfileEnabled || logoutButtonEnabled || orghiearchyEnabled) && (
					<UserInfoBar userInfoBarProps={userInfoBarProps}>
						{notificationEnabled ? (
							<MenuItem>
								<BellNotification apiService={serviceInstance} />
							</MenuItem>
						) : (
							<></>
						)}
						{orghiearchyEnabled ? (
							<MenuItem>
								<Orghiearchy
									serviceInstance={serviceInstance}
									type={OrghiearchyType.BREADCRUMB}
									triggerType="icon"
									onHiearchySelect={onHiearchySelect}
									defaultValue={defaultOrgValue}
									orgHiearchyIcon={orgHiearchyIcon}
									userId={userId}
								/>
							</MenuItem>
						) : (
							<></>
						)}
						{logoutButtonEnabled && !userProfileMenuEnabled ? (
							<MenuItem>
								<Box margin="auto" cursor="pointer" onClick={onLogoutClick}>
									<Icon
										icon={IconCodes.icon_Bd_Log_Out_Circle}
										color={props.menuColor}
									/>
								</Box>
							</MenuItem>
						) : (
							<></>
						)}
						{userProfileEnabled && (
							<MenuItem>
								{userProfileMenuEnabled ? (
									<UserProfileCard
										triggerElement={
											<Box
												onClick={() => setProfileOpen(!isProfileOpen)}
												cursor="pointer"
											>
												<Avatar
													firstName={firstName}
													lastName={lastName}
													labelPosition={LabelPositions.Left}
													type={
														displayNameEnable
															? AvatarTypes.AvatarWithLabel
															: AvatarTypes.Avatar
													}
													enableStatus={false}
													imageUrl={''}
													size={'32px'}
													enableStory={false}
													avatarContainerProps={{
														margin: 0,
														onClick: () => setProfileOpen(!isProfileOpen),
													}}
													//imageProps={{ border: '2px solid white' }}
													labelProps={{
														size: TextSizes.Small,
														lineHeight: 0,
														textAlign: 'center',
														textTransform: 'capitalize',
														alignItems: 'center',
														...userNameProperties,
													}}
												/>
											</Box>
										}
										isProfileOpen={isProfileOpen}
										setProfileOpen={setProfileOpen}
										handleLogout={onLogoutClick}
										handleAccountSettings={onAccountSettingsClick}
										firstName={firstName}
										lastName={lastName}
										hideFirstName={false}
										hideLastName={false}
										logoutButtonEnabled={logoutButtonEnabled}
										profileBoxBackgroundColor={profileBoxBackgroundColor}
										lastLogin={lastLoginTime}
										accountSettingsNavigation={accountSettingsNavigation}
										enableAccountSettingsButton={enableAccountSettingsButton}
										isNavigateToNewTab={isNavigateToNewTab}
										accountSettingsLinkName={accountSettingsLinkName}
										passwordExpires={passwordExpires}
										userName={userName}
										lastLoginEnabled={lastLoginEnabled}
										passwordExpireEnabled={passwordExpireEnabled}
									/>
								) : (
									<Avatar
										type={AvatarTypes.AvatarWithLabel}
										labelPosition={LabelPositions.Left}
										meta=""
										enableStatus={false}
										enableStory={false}
										size="32px"
										avatarContainerProps={{ margin: 0 }}
										firstName={firstName}
										lastName={lastName}
										imageUrl=""
										labelProps={{
											size: TextSizes.Small,
											lineHeight: 0,
											textAlign: 'center',
											textTransform: 'capitalize',
											alignItems: 'center',
											...userNameProperties,
										}}
									/>
								)}
							</MenuItem>
						)}
					</UserInfoBar>
				)}
			</Box>
			{ExpandedBox}
			{menuEnabled && (
				<Box {...menuBarProps} height={50}>
					{navMenuItems}
				</Box>
			)}
		</Box>
	);
};

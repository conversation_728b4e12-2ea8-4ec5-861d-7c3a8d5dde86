/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosInstance, AxiosResponse, Method } from 'axios';
import GenAppAuthService from './services/genAppAuthService';
import {
	AuthType,
	IAPIHandlerOptions,
	TargetAuth,
	FileUploadPayload,
} from './types';
import { v4 as uuidv4 } from 'uuid';
// eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
import constructFieldsParam from './constructFieldsParam';
import {
	GenerateRequestBodyfield,
	WorkFlowInstanceRequestURLPart,
} from './constants';
import { generate32LowerHexCharacters, toBase64 } from './services/AuthUtils';
/*
Service definition to group API Calls, all the api calls that belongs to a 
module or needed to call with same header configurations can be called using
the same Service. One instance of API service can have multiple service definitions
and is identified using the key. For each service an axios instance is also created 
*/

type Service = {
	baseUrl: string;
	headers: Record<string, string>;
	instance: AxiosInstance;
	token: string;
	target?: TargetAuth;
	enableTraceId?: boolean;
	auth?: AuthType;
};

/*
API Service is the abstract class with which all the api calls of the Apex systems are funelled through
This can be api calls happening through React Query library or directly through the api service instance
Apex platform and all the editors and the generated applications API calls are routed through this instance.
*/

export class ApiService {
	_services: Record<string, Service>;
	_token: string;
	_genAppAuthService: any;
	_instanceId: string;

	constructor() {
		this._services = {};
		this._token = '';
		this._instanceId = uuidv4();
		this._genAppAuthService = GenAppAuthService;
	}

	/*
  Call method is the one channel through which all the API calls are finally triggered
  Let it be get, post, patch, delete or any of any HTTP methods   
  */
	/**
	 *
	 * To identify the service
	 * @param key
	 * @param method
	 * @param endpoint
	 * @param body
	 * @param header
	 * @param authEnabled
	 * @returns
	 */
	async call(
		key: string,
		method: Method,
		endpoint: string,
		body: Record<string, string> | FormData | FileUploadPayload = {},
		header: Record<string, string> = {},
		options: IAPIHandlerOptions = {}
	): Promise<AxiosResponse | any> {
		const service: Service = this._services[key];
		if (!service) {
			console.log('Service not configured ', key);
			return;
		}
		const target =
			service && service.target ? service.target : TargetAuth.PLATFORM;
		const auth = service?.auth;
		const enableTraceId = service?.enableTraceId;
		let headers: any = {
			...service?.headers,
			...header,
			target,
		};
		if (enableTraceId) {
			headers['X-B3-TraceId'] = generate32LowerHexCharacters();
		}
		if (auth === AuthType.AUTH) {
			const activeAppId = this._genAppAuthService.getActiveAppId();
			if (target === TargetAuth.APP && activeAppId) {
				headers = {
					...headers,
					'X-Jiffy-Target-App-Id': activeAppId,
				};
			}
		}

		if (this._genAppAuthService.getIsCustomLogin()) {
			// Custom login is enabled, don't use the token and bypass interceptor
			headers = { ...headers, isExternalToken: 'true' };
		} else {
			let serviceToken = await this._genAppAuthService.getAccessToken();
			serviceToken = serviceToken || (window as any)?._env_?.token;
			if (serviceToken) {
				// Override any tokens that are passed as part of the service configuration with the instance token
				// Ignoring any service token at this point and attaching the token that is valid from instance
				headers = { ...headers, Authorization: 'Bearer ' + serviceToken };
				if (this._genAppAuthService.getIsExternalToken()) {
					headers = { ...headers, isExternalToken: 'true' };
				}
			}
		}
		let updatedEndpoint = endpoint;
		if (
			method?.toLowerCase() === 'post' ||
			method?.toLowerCase() === 'patch' ||
			method?.toLowerCase() === 'put'
		) {
			let constructFields = true;
			if ('patched' in body) {
				constructFields = false;
			}
			if (constructFields) {
				if (endpoint?.includes(GenerateRequestBodyfield)) {
					const generatedParamValue = constructFieldsParam(body)?.join(',');
					updatedEndpoint = endpoint?.replace(
						GenerateRequestBodyfield,
						`_fields=${generatedParamValue}`
					);
				} else if (
					endpoint?.includes(WorkFlowInstanceRequestURLPart) &&
					(body as Record<string, any>)?.arguments
				) {
					const firstArgumentValue =
						Object.values((body as Record<string, any>)?.arguments || {})[0] ||
						{};
					if (typeof firstArgumentValue === 'object') {
						const generatedBodyParams =
							constructFieldsParam(firstArgumentValue)?.join(',');
						(body as Record<string, any>).arguments['_fields'] =
							generatedBodyParams;
					}
				}
			}
		}

		return service.instance.request({
			url: updatedEndpoint,
			method: method,
			headers,
			data: body,
			...options,
		});
	}

	/**
	 *
	 * @param key
	 * @param endpoint
	 * @param body
	 * @returns
	 */
	post(
		key: string,
		endpoint: string,
		body: Record<string, string>
	): Promise<AxiosResponse> {
		return this.call(key, 'POST', endpoint, body);
	}
	/**
	 *
	 * @param key
	 * @param endpoint
	 * @param body
	 * @returns
	 */
	get(
		key: string,
		endpoint: string,
		options: IAPIHandlerOptions = {}
	): Promise<AxiosResponse> {
		return this.call(key, 'GET', endpoint, {}, {}, options);
	}
	/**
	 *
	 * @param key
	 * @param endpoint
	 * @param body
	 * @returns
	 */
	put(
		key: string,
		endpoint: string,
		body: Record<string, string>
	): Promise<AxiosResponse> {
		return this.call(key, 'PUT', endpoint, body);
	}
	/**
	 *
	 * @param key
	 * @param endpoint
	 * @param body
	 * @returns
	 */
	delete(
		key: string,
		endpoint: string,
		body: Record<string, string> = {}
	): Promise<AxiosResponse> {
		return this.call(key, 'DELETE', endpoint, body);
	}
	/**
	 *
	 * @param key
	 * @param endpoint
	 * @param formData
	 * @returns
	 */
	upload(
		key: string,
		endpoint: string,
		formData: FormData | FileUploadPayload,
		options?: IAPIHandlerOptions
	): Promise<AxiosResponse> {
		return this.call(key, 'POST', endpoint, formData, {}, options);
	}

	/**
	 *
	 * @param key
	 * @param token
	 */
	setToken(key: string, token: string) {
		this._services[key].token = token;
		const instance = this._services[key].instance;
		instance.defaults.headers.common['Authorization'] = token;
	}

	/**
	 *
	 * @param key
	 * @returns
	 */
	getToken(key?: string) {
		return key ? this._services[key]['token'] : this._token;
	}

	/**
	 *
	 * @param key
	 * @returns
	 */
	getHeaders(key: string) {
		return this._services[key].headers;
	}

	/* 
  Parse the JWT token and return base64 decoded JSON result
  */
	parseJwt(token: any) {
		const base64Url = token.split('.')[1];
		const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
		const jsonPayload = decodeURIComponent(
			window
				.atob(base64)
				.split('')
				.map(function (c) {
					return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
				})
				.join('')
		);
		return JSON.parse(jsonPayload);
	}

	/* 
  Utility method to checks whether the token is expired, the expiry time is the time that is derived from the 
  token and the buffer is to add buffer to see if the token expires in next x milliseconds
   */
	isTokenExpired(expiryTime: number, buffer: number) {
		const expiry = expiryTime;
		const currentTime = Date.now() / 1000;
		return expiry - (buffer + currentTime) <= 0;
	}

	/* 
  Method to figure out if the token is expired, and if expired check for the refresh token expiry
  and to refresh the applicable token between platform and generated app.
  If the refresh token is also expired, logout and redirect the user
  */
	async checkTokenExpiry(expiryTime: number, token: any, target: TargetAuth) {
		if (
			this.isTokenExpired(expiryTime, 30) &&
			this._genAppAuthService.getAuthType() === 'TOKEN'
		) {
			// sessionStorage.removeItem("persist:hash")
			// sessionStorage.removeItem("jiffy_auth_token")
			window.location.replace(
				`${window.location.protocol}//${window.location.host}/session-expired`
			);
			return { expired: true, token };
		}
		if (
			this.isTokenExpired(expiryTime, 120) &&
			this._genAppAuthService.getAuthType() !== 'TOKEN'
		) {
			const refreshToken = await this._genAppAuthService.getRefreshToken();

			const refreshExpiry = this.parseJwt(refreshToken)?.exp;
			if (!this.isTokenExpired(refreshExpiry, 10)) {
				if (target === TargetAuth.APP) {
					token = await this._genAppAuthService.refreshApp();
					return { expired: true, token };
				} else {
					token = await this._genAppAuthService.refresh();
					sessionStorage.setItem('jiffy_auth_token', token);
					return { expired: true, token };
				}
			} else {
				// instead of doing this call the this.logout function
				this._genAppAuthService.logoutFn();
				// window.location.replace(
				// 	`${window.location.protocol}//${window.location.host}/logout`
				// );
				return { expired: false, token };
			}
		} else {
			return { expired: false, token };
		}
	}
	/*   
  Interceptor to intercept all the calls made by the AXIOS instance
  Once intercepted, will access the token from the header and checks for the expiry

  The response interceptor listen for the errors, all the errors that doesn't return a valid
  status are considered as invalid token and logouts the user and return to login page
  */
	addInterceptors(axios: AxiosInstance, auth: AuthType) {
		if (auth === AuthType.NO_AUTH) return axios;

		axios.interceptors.request.use(async (req: any) => {
			// Add SkyWalking header
			const isPlatform = req?.headers?.target === TargetAuth.PLATFORM;
			let traceId = generate32LowerHexCharacters();
			let segmentId = generate32LowerHexCharacters();
			const spanId = 0; // 0 as base64
			const parentService = toBase64(isPlatform ? 'platform' : 'app');
			const parentInstance = toBase64(this._instanceId);
			const parentEndpoint = toBase64(req.url || '');
			const targetHost = req.baseURL
				? new URL(req.baseURL).host
				: window.location.host;
			const targetAddress = toBase64(targetHost);
			traceId = toBase64(traceId);
			segmentId = toBase64(segmentId);
			const sw8Value = `1-${traceId}-${segmentId}-${spanId}-${parentService}-${parentInstance}-${parentEndpoint}-${targetAddress}`;
			req.headers['sw8'] = sw8Value;
			req.headers['X-Jiffy-Ts'] = new Date().toISOString();

			// Existing token interceptor logic
			if (req?.headers?.isExternalToken === 'true') {
				return req;
			}
			let tokenIntercepted = req?.headers?.Authorization;
			if (!tokenIntercepted) {
				// Incase the request is missing the Authorization header,
				// check if the authorization is set in the common headers
				tokenIntercepted = req?.headers?.common?.Authorization;
			}
			// Extracted token from the request
			if (tokenIntercepted) {
				const target = req?.headers?.target;
				delete req?.headers?.target;
				tokenIntercepted = tokenIntercepted.substring(7);
				const parsedToken = this.parseJwt(tokenIntercepted);
				const expiryTime = parsedToken?.exp;

				const { expired, token } = await this.checkTokenExpiry(
					Number(expiryTime),
					tokenIntercepted,
					target
				);
				if (expired) {
					// Setting the header back to the current request header
					req.headers = { ...req.headers, Authorization: 'Bearer ' + token };
					// Setting the header back to the common - not for this request but as a
					// blanket to all future requests on the same instance
					req.headers.common['Authorization'] = 'Bearer ' + token;

					return req;
				}
				return req;
			} else {
				console.log('No token found, logging out the session');
				window.location.replace(
					`${window.location.protocol}//${window.location.host}/logout`
				);
			}
			return req;
		});

		axios.interceptors.response.use(
			(response) => {
				return response;
			},
			async (error) => {
				const providerProperties =
					this._genAppAuthService?.authConfig?.providerProperties;
				const sandbox: any = localStorage.getItem('sandbox');
				const isSandbox = JSON.parse(sandbox || 'false');
				if (!error?.response?.status && error?.message !== 'canceled') {
					try {
						this._genAppAuthService.logoutWithMessage(
							`logging out since the API responded with status ${error?.response?.status}`
						);
					} catch (error) {
						console.log('Error while logging out from interceptor');
					}
					if (
						providerProperties?.tokenScheme === 'SCookie' &&
						providerProperties?.logoutUrl
					) {
						window.location.replace(providerProperties.logoutUrl);
					} else {
						window.location.replace(
							`${window.location.protocol}//${window.location.host}/logout`
						);
					}
				} else if (error?.response?.status == '403' && !isSandbox) {
					try {
						this._genAppAuthService.logoutWithMessage(
							`logging out since the API responded with status ${error?.response?.status}`
						);
					} catch (error) {
						console.log('Error while logging out from interceptor');
					}
					if (
						providerProperties?.tokenScheme === 'SCookie' &&
						providerProperties?.logoutUrl
					) {
						window.location.replace(providerProperties.logoutUrl);
					} else {
						window.location.replace(
							`${window.location.protocol}//${window.location.host}/logout`
						);
					}
				}
				return Promise.reject(error);
			}
		);

		return axios;
	}
	/**
	 * Configures the custom services with the provided key.
	 * If the target is set as platform, uses the platform token.
	 * If the target is set to app, uses the token of the active app that is configured.
	 *
	 * @param key
	 * @param baseUrl
	 * @param headers
	 */
	configure(
		key: string,
		baseUrl: string,
		headers: Record<string, string>,
		target = TargetAuth.PLATFORM,
		auth = AuthType.AUTH,
		enableTraceId = true
	) {
		let instance = axios.create({
			baseURL: baseUrl,
		});
		instance = this.addInterceptors(instance, auth);
		this._services[key] = {
			...this._services[key],
			baseUrl,
			headers,
			instance,
			target,
			auth,
			enableTraceId,
		};
		return instance;
	}
	/**
	 *
	 * @param key
	 * @param headers
	 */
	setHeaders(key: string, headers = {}) {
		if (key) {
			const currentHeaders = this._services?.[key]?.headers;
			this._services[key] = {
				...this._services?.[key],
				headers: { ...currentHeaders, ...headers },
			};
		}
	}
	/**
	 *
	 * @param key
	 * @returns
	 */
	getService(key: string) {
		return this._services?.[key];
	}

	/* 
  Configures the instance with the acitve APP. 
  This method takes in the client id which is the appinstance ID and 
  calls the methods to generate the exchange token that is required for the generated app.
  */
	async configureApp(clientId: string) {
		let token = await this._genAppAuthService.getAccessToken();
		const parsedToken = this.parseJwt(token);
		const expiryTime = parsedToken?.exp;
		token = await this.checkTokenExpiry(
			Number(expiryTime),
			token,
			TargetAuth.PLATFORM
		);
		return this._genAppAuthService.configureApp(clientId);
	}
}

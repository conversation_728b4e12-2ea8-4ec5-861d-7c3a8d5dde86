/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */
import NoAuthService from './noAuthService';
import JiffyAuthService from './jiffyAuthService';
import StoryAuthService from './storyAuthService';
import { BookType } from 'xlsx';

const authProviders = {
	OKTA: 'OKTA',
	AWS_COGNITO: 'AWS_COGNITO',
	NO_AUTH: 'NO_AUTH',
	JIFFY_AUTH: 'JIFFY_AUTH',
	STORY_AUTH: 'STORY_AUTH',
};

class GenAppAuthService {
	authConfig: any;
	auth: any;
	authService: any;
	idleTimeout: any;
	logoutPage: any;
	logoutFn: any;
	isExternalToken: boolean;
	isCustomLogin: boolean;

	constructor() {
		this.authConfig = {};
		this.auth = '';
		this.authService = {};
		this.idleTimeout = 0;
		this.logoutPage = {};
		this.logoutFn = () => {};
		this.isExternalToken = false;
		this.isCustomLogin = false;
	}

	get IdleTimeout() {
		return this.idleTimeout;
	}

	set IdleTimeout(timeout) {
		this.idleTimeout = timeout;
	}

	get LogoutPage() {
		return this.logoutPage;
	}

	set LogoutPage(logout) {
		this.logoutPage = logout;
	}

	init(
		config: any,
		initialAuthInfo: any,
		logoutFn: any,
		isExternalToken?: boolean,
		isCustomLogin?: boolean
	) {
		this.authConfig = config.authConfig;
		if (this.authConfig) {
			this.authConfig.providerProperties = config?.providerProperties;
		}
		this.isExternalToken = isExternalToken || false;
		this.isCustomLogin = isCustomLogin || false;
		if (config.appConfig && config.appConfig.IdleTimeout)
			this.idleTimeout = config.appConfig.IdleTimeout;
		if (config.appConfig && config.appConfig.logoutPage)
			this.logoutPage = config.appConfig.logoutPage;
		this.auth = config.auth;
		this.logoutFn = logoutFn;

		switch (config.auth) {
			case authProviders.JIFFY_AUTH:
				this.authService = new JiffyAuthService(
					this.authConfig,
					initialAuthInfo
				);
				break;
			case authProviders.STORY_AUTH:
				this.authService = new StoryAuthService();
				break;
			default: {
				this.authService = new NoAuthService();
			}
		}
	}

	authenticate() {
		return this.authService.authenticate();
	}
	apiAuthenticate() {
		return this.authService.apiAuthenticate();
	}
	getAccessToken() {
		return this.authService.AccessToken();
	}

	getIdToken() {
		return this.authService.IdToken();
	}
	getRefreshToken() {
		return this.authService.RefreshToken();
	}
	getAppRefreshToken() {
		return this.authService.AppRefreshToken();
	}
	getIsExternalToken() {
		return this.isExternalToken;
	}
	getIsCustomLogin() {
		return this.isCustomLogin;
	}
	getAuthToken(url: string) {
		return this.authService.AuthToken(url);
	}
	getAppAccessToken() {
		return this.authService.AppAccessToken();
	}

	getAuthType() {
		return this.authService.AuthType();
	}
	getTokenInfo() {
		return this.authService.getTokenInfo();
	}

	handleRedirect(authCode?: string) {
		return this.authService.handleRedirect(authCode);
	}
	refreshApp() {
		return this.authService.refreshApp && this.authService.refreshApp();
	}

	refresh() {
		return this.authService.refresh && this.authService.refresh();
	}

	logout() {
		return this.authService.logout();
	}

	getUserInfo() {
		return this.authService.getUserInfo();
	}

	getActiveAppId() {
		return this.authService.ActiveAppId?.();
	}

	configureApp(clientId: string) {
		return this.authService.setClientId(clientId);
	}
	refreshAppToken() {
		return (
			this.authService.refreshAppToken && this.authService.refreshAppToken()
		);
	}

	logoutWithMessage(message: string) {
		return this.authService.logoutWithMessage(message);
	}
}

export default new GenAppAuthService();

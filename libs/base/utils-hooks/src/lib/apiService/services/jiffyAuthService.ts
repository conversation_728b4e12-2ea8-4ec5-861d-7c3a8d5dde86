/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */
import BaseAuthImpl, { AUTH_TYPE } from './baseAuthImpl';
import {
	generateRandomString,
	generateCodeChallenge,
	parseJwt,
} from './AuthUtils';
import { ToastType, showToast } from '@base/toast';
import { v4 as uuidv4 } from 'uuid';
import { generateSw8Header } from './AuthUtils';

// Need to implement:- Prevent Redirect loops

export default class JiffyAuthService extends BaseAuthImpl {
	authConfig: any;
	private _instanceId: string;

	constructor(authConfig: any, initialAuthInfo: any) {
		super(initialAuthInfo);
		this.authConfig = authConfig;
		this._instanceId = uuidv4();
	}

	set init(authConfig: any) {
		this.authConfig = authConfig;
	}

	async authenticate() {
		const randomString = generateRandomString();
		const hashed = await generateCodeChallenge(randomString);
		const providerProperties = this.authConfig?.providerProperties;
		localStorage.setItem(
			'CODE_CHALLENGE',
			JSON.stringify({ string: randomString, hashed })
		);

		let params = `tenantId=${this.authConfig.tenantId}&client_id=${
			this.authConfig.clientId
		}&response_type=${this.authConfig.responseType}&scope=${encodeURIComponent(
			this.authConfig.scope.join(' ')
		)}&redirect_uri=${encodeURIComponent(this.authConfig.redirectUri)}&state=${
			this.authConfig.state
		}&code_challenge=${hashed}&code_challenge_method=${
			this.authConfig.codeChallengeMethod
		}`;
		if (providerProperties?.isExternalIDP && providerProperties?.idpHint) {
			const idpHintKey = providerProperties?.idpHintKey || 'idp_hint';
			params = `${params}&${idpHintKey}=${providerProperties?.idpHint}`;
		}
		if (
			providerProperties?.tokenScheme == 'SCookie' &&
			providerProperties?.logoutUrl
		) {
			window.location.href = providerProperties?.logoutUrl;
		} else {
			window.location.href = `${this.authConfig.baseUrl}login?${params}`;
		}
	}

	async handleRedirect(authCode?: string) {
		// Need to implement:- Error handling in case of missing params or code challenge

		const params = new URLSearchParams(window.location.search);
		const code = params.get('code');
		const codeChallenge = JSON.parse(
			localStorage.getItem('CODE_CHALLENGE') as unknown as string
		);

		if (authCode) {
			return fetch(
				`${this.authConfig.baseUrl}magic-token?magicLinkCode=${authCode}`,
				{
					method: 'GET',
					headers: {
						'X-Jiffy-Ts': new Date().toISOString(),
						sw8: generateSw8Header(this._instanceId),
					},
				}
			)
				.then((response) => response.json())
				.then((data) => {
					const { access_token, id_token, refresh_token, expires_in, page } =
						data;
					this.accessToken = access_token;
					this.idToken = id_token;
					this.refreshToken = refresh_token;
					this.expires_in = expires_in;
					if (page) {
						const pagePath = page.replaceAll(' ', '_');
						localStorage.setItem('initiatingRoute', `/page/${pagePath}`);
						localStorage.setItem('initiatingPath', `/page/${pagePath}`);
					}
				})
				.catch((error) => {
					return Promise.reject(error);
				});
		}

		return fetch(
			`${this.authConfig.baseUrl}token?tenantId=${this.authConfig.tenantId}`,
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
					'X-Jiffy-Ts': new Date().toISOString(),
					sw8: generateSw8Header(this._instanceId),
				},
				body: new URLSearchParams({
					code: code as unknown as string,
					code_verifier: codeChallenge?.string,
					redirect_uri: this.authConfig?.redirectUri,
					client_id: this.authConfig?.clientId,
					grant_type: this.authConfig?.grantType,
				}),
			}
		)
			.then((response) => response.json())
			.then((data) => {
				const { access_token, id_token, refresh_token, expires_in } = data;
				this.accessToken = access_token;
				this.idToken = id_token;
				this.refreshToken = refresh_token;
				this.expires_in = expires_in;
			})
			.catch((error) => {
				this.authenticate();
			});
	}

	async getTokenInfo() {
		return new Promise((resolve, reject) => {
			fetch(
				`${this.authConfig.baseUrl}/tokenInfo?tenantId=${this.authConfig.tenantId}`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
						'X-Jiffy-Ts': new Date().toISOString(),
						sw8: generateSw8Header(this._instanceId),
					},
					body: new URLSearchParams({
						appId: this.ActiveAppId() || '',
						accessToken: this.accessToken || '',
					}).toString(),
				}
			)
				.then((response) => {
					if (!response.ok) {
						throw new Error(`HTTP error! Status: ${response.status}`);
					}
					return response.json();
				})
				.then((data) => {
					resolve(data);
				})
				.catch((error) => {
					reject(error);
				});
		});
	}

	async refresh() {
		return new Promise((resolve, reject) => {
			fetch(
				`${this.authConfig.baseUrl}token?tenantId=${this.authConfig.tenantId}`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
						'X-Jiffy-Ts': new Date().toISOString(),
						sw8: generateSw8Header(this._instanceId),
					},
					body: new URLSearchParams({
						refresh_token: this.refreshToken,
						client_id: this.authConfig.clientId,
						grant_type: this.authConfig.refreshGrantType || 'refresh_token',
					}),
				}
			)
				.then((response) => response.json())
				.then((data) => {
					const { access_token, id_token, refresh_token, expires_in } = data;
					this.accessToken = access_token;
					this.idToken = id_token;
					this.refreshToken = refresh_token;
					this.expires_in = expires_in;
					resolve(access_token);
				})
				.catch((error) => {
					reject(this.accessToken);
				});
		});
	}
	async refreshAppToken() {
		return new Promise((resolve, reject) => {
			fetch(
				`${this.authConfig.baseUrl}token?tenantId=${this.authConfig.tenantId}`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
						'X-Jiffy-Ts': new Date().toISOString(),
						sw8: generateSw8Header(this._instanceId),
					},
					body: new URLSearchParams({
						refresh_token: this.refreshToken,
						client_id: this.authConfig.clientId,
						grant_type: this.authConfig.refreshGrantType || 'refresh_token',
					}),
				}
			)
				.then((response) => response.json())
				.then((data) => {
					const { access_token, id_token, refresh_token, expires_in } = data;
					this.accessToken = access_token;
					this.idToken = id_token;
					this.refreshToken = refresh_token;
					this.expires_in = expires_in;
					resolve(data);
				})
				.catch((error) => {
					reject(this.accessToken);
				});
		});
	}

	async refreshApp() {
		return new Promise((resolve, reject) => {
			fetch(
				`${this.authConfig.baseUrl}token?tenantId=${this.authConfig.tenantId}`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
						'X-Jiffy-Ts': new Date().toISOString(),
						sw8: generateSw8Header(this._instanceId),
					},
					body: new URLSearchParams({
						refresh_token: this.appAuthInfo?.refreshToken || '',
						client_id: this.appAuthInfo?.activeAppId || '',
						grant_type: this.authConfig?.refreshGrantType || 'refresh_token',
					}),
				}
			)
				.then((response) => response.json())
				.then((data) => {
					const { access_token, id_token, refresh_token, expires_in } = data;
					if (this.appAuthInfo) {
						this.appAuthInfo.accessToken = access_token;
						this.appAuthInfo.idToken = id_token;
						this.appAuthInfo.refreshToken = refresh_token;
						this.appAuthInfo.expires_in = expires_in;
						resolve(access_token);
					}
				})
				.catch((error) => {
					reject(this.accessToken);
				});
		});
	}

	setClientId(clientId: string) {
		const appAuthInfo = {
			activeAppId: clientId,
		};

		this.updateAppAuthInfo(appAuthInfo);
	}

	async exchangeToken(clientId: string) {
		return (
			fetch(
				`${this.authConfig.baseUrl}token/exchange?tenantId=${this.authConfig.tenantId}`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${this.accessToken}`,
						'X-Jiffy-Ts': new Date().toISOString(),
						sw8: generateSw8Header(this._instanceId),
					},
					body: JSON.stringify({
						clientId: clientId,
					}),
				}
			)
				.then((response) => response.json())
				.then((data) => {
					const { access_token, id_token, refresh_token, expires_in } = data;
					const appAuthInfo = {
						activeAppId: clientId,
						expires_in: expires_in,
						refreshToken: refresh_token,
						idToken: id_token,
						accessToken: access_token,
						userInfo: access_token ? parseJwt(access_token) : null,
					};

					this.updateAppAuthInfo(appAuthInfo);
				})
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				.catch((error: any) => {
					showToast({
						title: 'Error',
						description: `Configuration for application failed!`,
						type: ToastType.Error,
						showIcon: true,
						isDismissible: true,
					});
				})
		);
	}

	async logout() {
		return new Promise((resolve, reject) => {
			fetch(`${this.authConfig.logoutUrl}?id_token_hint=${this.idToken}`, {
				method: 'GET',
			})
				.then((response) => {
					fetch(`${this.authConfig.baseUrl}logout`, {
						method: 'GET',
						headers: {
							Authorization: `Bearer ${this.accessToken}`,
							'X-Jiffy-Ts': new Date().toISOString(),
							sw8: generateSw8Header(this._instanceId),
						},
					}).then((res) => {
						resolve(response);
					});
				})
				.catch((error) => {
					reject(error);
				});
		});
	}

	IdToken() {
		const that = this;
		return {
			then: function (onFulfilled: any) {
				if (that.idToken) return onFulfilled(that.idToken);
				return that.authenticate();
			},
		};
	}

	RefreshToken() {
		const that = this;
		return {
			then: function (onFulfilled: any) {
				if (that.refreshToken) {
					return onFulfilled(that.refreshToken);
				}
				return that.authenticate();
			},
		};
	}

	AccessToken() {
		const that = this;
		return {
			then: function (onFulfilled: any) {
				if (that.accessToken) return onFulfilled(that.accessToken);
				return that.authenticate();
			},
		};
	}

	AuthToken() {
		const that = this;
		return {
			then: function (onFulfilled: any) {
				if (that.accessToken)
					return onFulfilled({
						accessToken: that.accessToken,
						idToken: that.idToken,
						expires_in: that.expires_in,
						refreshToken: that.refreshToken,
					});
				return that.authenticate();
			},
		};
	}

	AppIdToken() {
		const that = this;
		return {
			then: function (onFulfilled: any) {
				if (that?.appAuthInfo?.idToken)
					return onFulfilled(that?.appAuthInfo?.idToken);
				return that.refreshApp();
			},
		};
	}

	AppRefreshToken() {
		const that = this;
		return {
			then: function (onFulfilled: any) {
				if (that?.appAuthInfo?.refreshToken) {
					return onFulfilled(that?.appAuthInfo?.refreshToken);
				}
				return that.refreshApp();
			},
		};
	}

	AppAccessToken() {
		const that = this;
		return {
			then: function (onFulfilled: any) {
				if (that?.appAuthInfo?.accessToken)
					return onFulfilled(that?.appAuthInfo?.accessToken);
				return that.refreshApp();
			},
		};
	}

	AppAuthToken() {
		const that = this;
		return {
			then: function (onFulfilled: any) {
				if (that?.appAuthInfo?.accessToken && that?.appAuthInfo?.idToken)
					return onFulfilled({
						accessToken: that?.appAuthInfo?.accessToken,
						idToken: that?.appAuthInfo?.idToken,
						expires_in: that?.appAuthInfo?.expires_in,
						refreshToken: that?.appAuthInfo?.refreshToken,
					});
				return that.refreshApp();
			},
		};
	}

	ActiveAppId() {
		return this.appAuthInfo?.activeAppId;
	}

	AuthType() {
		return this.authType || AUTH_TYPE.LOGIN;
	}

	async getUserInfo() {
		// Need to implement- Return only user data instead of entire token data
		if (this.accessToken) return parseJwt(this.accessToken);
		this.authenticate();
	}

	async logoutWithMessage(message: string) {
		const token = sessionStorage.getItem('jiffy_auth_token');
		return new Promise((resolve, reject) => {
			fetch(`${this.authConfig.baseUrl}logout?message=${message}`, {
				method: 'GET',
				headers: {
					Authorization: `Bearer ${token}`,
					'X-Jiffy-Ts': new Date().toISOString(),
					sw8: generateSw8Header(this._instanceId),
				},
			});
		});
	}
}

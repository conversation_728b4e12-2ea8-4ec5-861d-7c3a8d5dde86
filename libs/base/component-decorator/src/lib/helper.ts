/* eslint-disable @nrwl/nx/enforce-module-boundaries */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-case-declarations */
/*
 * Created on Tue Oct 19 2021
 *
 * Copyright (c) 2021. Jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of <PERSON><PERSON>.ai
 * No part of this file may be copied by any means without the express written permission of <PERSON><PERSON>.ai
 */
import { ToolbarType } from '@base/toolbar';
import _isEqual from 'lodash/isEqual';
import { IActiveElement, MetaData } from '@base/utils-hooks';
import { CursorPosition, EditorType } from './types';

const exceptionComponents = [
	'GridRow',
	'GridCol',
	'Capabilityblock',
	'Datatable',
];

/**
 *
 * @param componentName
 * @returns
 */
export const isFullWidthContainer = (componentName: string) => {
	return (
		componentName &&
		exceptionComponents.some(
			(item) => item.toLowerCase() === componentName.toLowerCase()
		)
	);
};

/**
 *
 * @param currentComponentId
 * @param currentComponentName
 * @returns
 */
export const isHoverStackValid = (
	currentComponentId: string | number,
	currentComponentName: string,
	activeEle?: IActiveElement
): boolean => {
	const hoverStack = activeEle?.hoverHierarchy
		? [...(activeEle?.hoverHierarchy || [])]
		: [];

	if (!hoverStack.length) {
		return false;
	}

	switch (currentComponentName) {
		case 'FunctionalBlock':
			const currentActiveElement = hoverStack?.pop();
			return (
				currentComponentId === currentActiveElement?.id &&
				currentComponentName === currentActiveElement?.componentName
			);
		case 'Capabilityblock':
			let lastHoveredElement = hoverStack?.pop();
			while (
				lastHoveredElement &&
				['GridRow', 'GridCol'].includes(lastHoveredElement.componentName)
			) {
				lastHoveredElement = hoverStack?.pop();
			}
			return (
				lastHoveredElement?.id === currentComponentId &&
				lastHoveredElement?.componentName === currentComponentName
			);
		default:
			return Boolean(
				hoverStack.find(
					(i) =>
						i.id === currentComponentId &&
						i.componentName === currentComponentName
				)
			);
	}
};
/**
 *
 * @param currentComponentId
 * @param currentComponentName
 * @returns
 */
export const isLastHoveredElement = (
	currentComponentId: string | number,
	currentComponentName: string,
	activeEle?: IActiveElement
): boolean => {
	const hoverStack = [...(activeEle?.hoverHierarchy || [])];
	let lastHoveredElement = hoverStack?.pop();
	if (!lastHoveredElement) {
		return false;
	}
	if (currentComponentName === 'Capabilityblock') {
		while (
			lastHoveredElement &&
			['GridRow', 'GridCol'].includes(lastHoveredElement.componentName)
		) {
			lastHoveredElement = hoverStack?.pop();
		}
	}
	return (
		lastHoveredElement?.componentName === currentComponentName &&
		currentComponentId === lastHoveredElement?.id
	);
};

export const getActiveToolbarType = (
	microEditable?: boolean,
	metadata?: MetaData
) => {
	if (!metadata?.toolbarType) return undefined;
	if (microEditable && metadata?.toolbarType === 'atomic') {
		return ToolbarType.INLINE;
	} else if (microEditable && metadata?.toolbarType === 'composite') {
		return ToolbarType.MICRO;
	}
	return undefined;
};

/**
 *  show/hide the box shadow with active editor
 * @param activeEditorType
 * @returns
 */
export const isWorkflowEditor = (activeEditorType?: EditorType): boolean => {
	return activeEditorType === EditorType.WORKFLOW;
};

export const isDataEditor = (activeEditorType?: EditorType): boolean => {
	return activeEditorType === EditorType.DATA;
};

export const isAutomationEditor = (activeEditorType?: EditorType): boolean => {
	return activeEditorType === EditorType.AUTOMATION;
};

/**
 *
 * @param e
 * @param ref
 * @returns
 */
export const getMousePosition = (e: any, ref: any) => {
	const width = ref?.current?.offsetWidth;
	const rect = e.currentTarget.getBoundingClientRect();
	const x = e.clientX - rect.left;
	if (x < width / 2) {
		return CursorPosition.LEFT;
	} else {
		return CursorPosition.RIGHT;
	}
};

const componentList = ['navigationbar', 'appheader', 'quickpanelbox'];

export const isBorderToolbarEnabled = (componentName: string) => {
	return componentList.some((item) => item === componentName?.toLowerCase());
};

export const isActiveElement = (
	id: string,
	componentName: string,
	activeElement: IActiveElement,
	editable: boolean
) => {
	return editable && isLastHoveredElement(id, componentName, activeElement);
};

export const areEqualChildProps = (
	prevProps: {
		microEditable: boolean;
		childProps: any;
	},
	nextProps: {
		microEditable: boolean;
		childProps: any;
	}
) => {
	return prevProps?.microEditable && true;
};

export const areEqualDecoratorProps = (prevProps: any, nextProps: any) => {
	return _isEqual(prevProps, nextProps);
};

export const FULL_WIDTH_COMPONENT_LIST = [
	'Form',
	'Datatable',
	'Agentviewtemplate',
	'Documentclassifier',
	'TabEditor',
	'Segmenttabs',
	'Uploadcard',
	'Driveuploader',
	'Pdfviewer',
	'Videoplayer',
	'Snap',
	'Content',
	'Barchart',
	'Columnchart',
	'Linechart',
	'Piechart',
	'Gaugechart',
	'InlineTextEditor',
	'Radarchart',
	'Slicedchart',
	'Progresschart',
	'GridRow',
	'GridCol',
	'Iframe',
	'Docpdfviewer',
	'Documentlistviewer',
	'Wizard',
	'Cardview',
	'Repeater',
	'Repeateritem',
	'Rejectioncard',
	'Reconmatchsummary',
	'Address',
	'FundingSource',
	'Listview',
	'ButtonsList',
	'Table',
	'Accordion',
	'Timeline',
	'Kanban',
	'ProgressTracker',
	'SimpleList',
	'TableCell',
	'TableRow',
	'TableColumn',
	'Login',
	'ReviewAccordionItem',
	'QuickPanelOptionsMenuBar',
	'QuickPanelChildWrapper',
	'QuickPanelOptionMenuBarItem',
	'QuickPanelHeadingText',
	'QuickPanelHeadingButton',
	'ReviewAccordion',
];

export const isGridComponent = (componentName: string) => {
	return FULL_WIDTH_COMPONENT_LIST.some(
		(component: string) =>
			component.toLowerCase() === componentName.toLowerCase()
	);
};

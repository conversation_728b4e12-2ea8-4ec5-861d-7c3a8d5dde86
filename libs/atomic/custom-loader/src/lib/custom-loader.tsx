/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the CustomLoader component
import React, { FC } from 'react';
import DefaultImage from '../assets/loader.svg';
import Axos from '../assets/axos.gif';

import { configurable } from '@base/config-provider';
import { setMetaData } from '@base/utils-hooks';
import { defaultProps, metadata } from './metadata';
import { IconMapProps, ICustomLoaderProps } from './types';
import { styles } from './style';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface CustomLoaderProps extends ICustomLoaderProps {}

const iconMap: Record<string, IconMapProps> = {
	default: {
		src: DefaultImage,
	},
	axos: {
		src: Axos,
		animate: false,
	},
	circularLoading: {
		src: 'circularLoading',
		cssBased: true,
	},
};

const CustomLoader: FC<CustomLoaderProps> = (props: CustomLoaderProps) => {
	// Framework generated code, below line registers the component
	// NOT TO BE DELETED
	setMetaData('CustomLoader', metadata);

	// Framework generated code, below line provides the contextProvider instance
	// const context = useEditContext();

	const classes = styles(props);
	let loaderIcon =
		localStorage.getItem('custom-loader-icon') ?? 'circularLoading';
	if (iconMap?.[loaderIcon]?.cssBased === true) {
		if (classes[iconMap?.[loaderIcon]?.src]) {
			return (
				<div
					data-component="atomic/custom-loader"
					className={`${classes[iconMap?.[loaderIcon]?.src]} ${
						classes.loaderStyles
					}`}
				></div>
			);
		}
		loaderIcon = 'default';
	}
	return (
		<img
			data-component="atomic/custom-loader"
			src={iconMap?.[loaderIcon]?.src ?? iconMap.default?.src}
			className={`${classes.loaderStyles} ${
				iconMap?.[loaderIcon]?.animate === false ? '' : classes.animate
			}`}
			alt="Loading..."
		/>
	);
};

CustomLoader.defaultProps = defaultProps;

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
export default configurable(CustomLoader, 'Customloader');
CustomLoader.displayName = 'Customloader';

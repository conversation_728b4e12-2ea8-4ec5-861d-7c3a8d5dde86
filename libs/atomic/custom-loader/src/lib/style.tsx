/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { ApexTheme } from '@base/theme';
import { createUseStyles } from 'react-jss';
import { ICustomLoaderProps } from './types';

export const styles = createUseStyles<string, ICustomLoaderProps>(
	(theme: ApexTheme) => ({
		'@keyframes spinAnimation': {
			'0%': {
				transform: 'rotate(0deg)',
			},
			'100%': {
				transform: 'rotate(360deg)',
			},
		},
		'@keyframes l2': {
			to: {
				transform: 'rotate(1turn)',
			},
		},
		circularLoading: () => ({
			aspectRatio: 1,
			borderRadius: '50%',
			border: `4px solid ${theme.colors.primary[200]}`,
			borderRightColor: theme.colors.primary[700],
			animation: '$l2 1s infinite linear',
		}),
		loaderStyles: (props) => ({
			display: 'inline-block',
			width: props.size ?? '32px',
			verticalAlign: 'middle',
			alignSelf: 'center',
			height: props.size ?? '32px',
		}),
		animate: {
			animation: '$spinAnimation 1.5s linear infinite',
		},
	})
);

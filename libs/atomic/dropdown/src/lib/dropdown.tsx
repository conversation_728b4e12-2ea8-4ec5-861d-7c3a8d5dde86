/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the Dropdown component
import { FC, useState, forwardRef, useEffect, useRef, memo } from 'react';
import { useTheme } from 'react-jss';
import { configurable } from '@base/config-provider';
import { setMetaData } from '@base/utils-hooks';
import { ApexTheme } from '@base/theme';
import { defaultProps, metadata } from './metadata';
import {
	IDropdownProps,
	DropdownType,
	DropdownSize,
	InputOrientation,
} from './types';
import classNames from 'classnames';
import Box from '@atomic/box';
import Text from '@atomic/text';
import Icon, { IconCodes } from '@atomic/icon';
import Contextmenu, {
	ContextmenuProps,
	ContextmenuType,
} from '@composite/contextmenu';
import { useDropdownStyles } from './styles/useDropdownStyles';
import { PlacementType } from '@atomic/listview';
import { CaptionMessage, FormControl } from '@composite/form';
import { Controller } from 'react-hook-form';
import {
	getDisplayValue,
	getEditedProps,
	getPlaceholderColor,
	getSelectItemStyle,
	isNotEmptyValue,
	validateSelectedValues,
} from './helper';
import {
	activeStyle,
	disableStyle,
	getContainerWidth,
	getTriggerOffset,
} from './helper/helper';
import Tooltip, { TooltipPosition, TooltipType } from '@atomic/tooltip';
// import { ITEM_CONTAINER_HEIGHT } from './constants';
// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface DropdownProps extends IDropdownProps, ContextmenuProps {}

const DropdownWrapper: FC<DropdownProps> = forwardRef(
	(props: DropdownProps) => {
		// Framework generated code, below line registers the component
		// NOT TO BE DELETED
		setMetaData('Dropdown', metadata);
		const [currentContainerWidth, setContainerWidth] = useState('0px');
		const [isHovered, setHovered] = useState(false);
		const ref = useRef(null);
		useEffect(() => {
			setContainerWidth(`${ref?.current?.offsetWidth - 16}px`);
		}, [ref?.current?.offsetWidth]);

		const theme: ApexTheme = useTheme();
		const classes = useDropdownStyles(props);
		const {
			id,
			className,
			label,
			placeholder,
			selectedValues,
			dropdownType,
			disabled,
			readonly,
			dropdownIconProps,
			dropdownLabelStyles,
			data,
			contextMenuProps,
			selectedItemStyles,
			iconHoverStyles,
			dropdownContainerStyle,
			onMouseDown,
			onItemClick,
			onChange,
			withForm,
			showLabel,
			multiSelect,
			showAsterisk,
			onChangeTrigger,
			onBlur,
			onBlurTrigger,
			menuType,
			showError,
			margin,
			padding,
			border,
			font,
			isOpen,
			fontSize,
			color,
			inpColor,
			backgroundColor,
			makeSelectionClearable,
			preventRenderLayer = false,
			isAggregate,
			enableOnChange = false,
			tooltipPossiblePlacements = [],
			inputOrientation,
		} = props;
		const [isDropDownOpen, setMenuOpen] = useState(isOpen || false);
		const menuOpenStatus = (currentStatus) => {
			setMenuOpen(currentStatus);
		};
		const onSelected = (selectedItem) => {
			setMenuOpen(selectedItem);
		};
		const showDropdownLabel = isNotEmptyValue(selectedValues);
		const [menu, setMenu] = useState<ContextmenuType>(menuType);
		const [selectedValueState, setSelectedValueState] = useState<
			string | string[]
		>(validateSelectedValues(selectedValues));
		useEffect(() => {
			const validValue = validateSelectedValues(selectedValues);
			setSelectedValueState(validValue);
			if (validValue !== undefined) {
				if (withForm) {
					// trigger onChange to update the selected value in the form
					onChange?.(validValue as any);
					enableOnChange &&
						onChangeTrigger?.({
							target: { value: validValue },
						});
				} else {
					enableOnChange && onItemClick && onItemClick(validValue);
				}
			}
		}, [JSON.stringify(selectedValues), enableOnChange, withForm]);

		useEffect(() => {
			if (menuType) {
				setMenu(menuType);
				return;
			}
			if (multiSelect) setMenu(ContextmenuType.CHECKBOX);
			else setMenu(ContextmenuType.CONTEXTMENU);
		}, [multiSelect, menuType]);

		const handleItemClick = (value) => {
			setHovered(false);
			onItemClick && onItemClick(value);
			if (withForm) {
				onChange?.(value);
				onChangeTrigger?.({
					target: { value },
				});
			}
		};
		const handleBlur = (event) => {
			setHovered(false);
			if (withForm) {
				onBlur?.(event);
				onBlurTrigger?.({
					target: { value: event },
				});
			}
		};
		const handleKeyDown = (event) => {
			setHovered(false);
			if (event.key === 'Enter') {
				onSelected(!isDropDownOpen);
			}
		};

		const MAX_CHAR_LIMIT = 24;
		const isTruncated =
			getDisplayValue(selectedValueState, data, menu, props)?.length >
			MAX_CHAR_LIMIT;
		// const actualContainerHeight =
		// 	(Array.isArray(data) &&
		// 		data?.length &&
		// 		`${data.length * ITEM_CONTAINER_HEIGHT}px`) ||
		// 	`${ITEM_CONTAINER_HEIGHT}px`;

		const dropdownTextComp = () => {
			return (
				<Box minWidth={0} flex={1}>
					<Text
						className={classes.text}
						opacity={disabled || readonly ? 0.5 : 1}
						color={
							((disabled || readonly) && disableStyle(props)?.color) ||
							getPlaceholderColor({
								showDropdownLabel,
								theme,
								disabled: disabled || readonly,
								inpColor,
							})
						}
						{...dropdownLabelStyles}
						{...getSelectItemStyle(showDropdownLabel, selectedItemStyles)}
						{...((disabled || readonly) && disableStyle(props))}
						disableInlineEdit={true}
					>
						{showDropdownLabel
							? getDisplayValue(selectedValueState, data, menu, props)
							: placeholder?.toString()}
						{/*converting to string to prevent component breaking when user bind object placeholder */}
					</Text>
				</Box>
			);
		};
		const clearSelectedOptions = (event) => {
			setHovered(false);
			setMenuOpen(false);
			event.stopPropagation();
			if (menu == ContextmenuType.CONTEXTMENU) {
				onChange?.({
					target: { value: withForm ? null : '' },
				} as unknown as any);
				onChangeTrigger?.({
					target: { value: '' },
				} as unknown as any);
				onItemClick?.('');
			} else if (menu == ContextmenuType.CHECKBOX) {
				onChange?.({
					target: { value: withForm ? null : [] },
				} as unknown as any);
				onChangeTrigger?.({
					target: { value: [] },
				} as unknown as any);
				onItemClick?.([]);
			}
		};
		return (
			<div id={id} className={className}>
				<Box
					data-component="atomic/dropdown"
					width={
						inputOrientation === InputOrientation.Horizontal
							? props.width || '304px'
							: '100%'
					}
					className={classes.dropdown}
					margin={margin}
					padding={padding}
					border={border}
					backgroundColor={backgroundColor}
					{...dropdownContainerStyle}
					flexDirection={
						inputOrientation === InputOrientation.Horizontal ? 'row' : 'column'
					}
					alignItems={
						inputOrientation === InputOrientation.Horizontal
							? 'center'
							: 'inherit'
					}
					onMouseEnter={() => setHovered(true)}
					onMouseLeave={() => setHovered(false)}
				>
					{showLabel && (
						// eslint-disable-next-line @typescript-eslint/no-explicit-any
						<Text
							className={classNames(classes.labelText, {
								[classes.required]: showAsterisk || props?.showAsMandatory,
							})}
							fontSize={fontSize || font}
							color={color || 'red'}
							opacity={disabled || readonly ? 0.5 : 1}
							inlineTargetId={props?.id}
						>
							{label}
						</Text>
					)}
					{dropdownType === DropdownType.Primary && (
						<Contextmenu
							id={
								inputOrientation === InputOrientation.Horizontal
									? 'contextMenuDropDown'
									: 'contextMenuDropDownVertical'
							}
							isHeightAutoCorrect
							scrollbarStyles={{ maxHeight: '350px' }}
							placement={PlacementType.BottomStart}
							selectedItemStyles={selectedItemStyles}
							containerWidth={getContainerWidth(
								props?.containerWidth,
								currentContainerWidth
							)}
							hideScrollBarStyle={{ padding: 0, overflow: 'hidden' }}
							// virtuosoStyles={{ height: actualContainerHeight }}
							onOutsideClickCallback={(isClickedOutside) =>
								setMenuOpen(isClickedOutside)
							}
							triggerOffset={getTriggerOffset(props?.triggerOffset)}
							triggerElement={
								<Box
									ref={ref}
									display="flex"
									flexDirection="column"
									onKeyDown={handleKeyDown}
									tabIndex={0}
									className={classNames({
										[classes.dropdownTriggerWrapper]: !isDropDownOpen,
										[classes.disableOutline]: isDropDownOpen,
									})}
								>
									<Box
										data-testid="dropdown-trigger-id"
										className={classNames(classes.box, {
											[classes.disabled]: disabled || readonly,
											[classes.active]: isDropDownOpen,
										})}
										onClick={() => onSelected(!isDropDownOpen)}
										onMouseDown={onMouseDown}
										{...((disabled || readonly) && disableStyle(props))}
									>
										{isTruncated ? (
											<Tooltip
												type={TooltipType.Tooltip}
												triggerOffset={10}
												containerStyles={{ width: '200px' }}
												placement={TooltipPosition.TopStart}
												possiblePlacements={tooltipPossiblePlacements}
												content={getDisplayValue(
													selectedValueState,
													data,
													menu,
													props
												)}
											>
												{dropdownTextComp()}
											</Tooltip>
										) : (
											dropdownTextComp()
										)}
										<Box
											gap={'8px'}
											alignItems="center"
											width={makeSelectionClearable ? '48px' : '24px'}
											justifyContent={'flex-end'}
										>
											{makeSelectionClearable &&
											showDropdownLabel &&
											(isDropDownOpen || isHovered) &&
											(menu == ContextmenuType.CONTEXTMENU ||
												menu == ContextmenuType.CHECKBOX) ? (
												<Icon
													icon={IconCodes.icon_Tb_x}
													height="16px"
													fontSize="16px"
													color={
														disabled || readonly
															? theme.colors.monochrome.line
															: theme.colors.monochrome.label
													}
													hoverStyle={iconHoverStyles}
													{...dropdownIconProps}
													{...activeStyle(isDropDownOpen, props)}
													{...((disabled || readonly) && disableStyle(props))}
													onClick={clearSelectedOptions}
												/>
											) : (
												<></>
											)}

											<Icon
												icon={IconCodes.icon_Tb_chevron_down}
												height="24px"
												fontSize="24px"
												color={
													disabled || readonly
														? theme.colors.monochrome.line
														: theme.colors.monochrome.label
												}
												hoverStyle={iconHoverStyles}
												{...dropdownIconProps}
												{...activeStyle(isDropDownOpen, props)}
												{...((disabled || readonly) && disableStyle(props))}
											/>
										</Box>
									</Box>
								</Box>
							}
							isMenuOpenCallback={(isMenuOpenValue) =>
								menuOpenStatus(isMenuOpenValue)
							}
							isMenuOpen={isDropDownOpen}
							showError={showError}
							textStyles={{
								fontWeight: theme.fontWeights.regular,
							}}
							textHoverStyles={{ fontWeight: theme.fontWeights.regular }}
							textWrapperBoxStyle={{
								...(multiSelect && {
									width: 'calc(100% - 30px)',
								}),
							}}
							{...getEditedProps(props, menu, selectedValueState)}
							{...contextMenuProps}
							onBlur={handleBlur}
							onItemClick={handleItemClick}
							preventRenderLayer={preventRenderLayer}
							isAggregate={isAggregate}
						/>
					)}
					{dropdownType === DropdownType.OnlyIcon && (
						<Contextmenu
							isHeightAutoCorrect
							placement={PlacementType.BottomStart}
							selectedItemStyles={selectedItemStyles}
							//virtuosoStyles={{ height: actualContainerHeight }}
							onOutsideClickCallback={(isClickedOutside) =>
								setMenuOpen(isClickedOutside)
							}
							hideScrollBar={{ padding: 0, overflow: 'hidden' }}
							triggerElement={
								<Box
									className={classNames(classes.boxwithIcon, {
										[classes.disabled]: disabled || readonly,
										[classes.active]: isDropDownOpen,
									})}
									onClick={() => onSelected(!isDropDownOpen)}
									onMouseDown={onMouseDown}
									onKeyDown={handleKeyDown}
									tabIndex={0}
								>
									<Icon
										icon={IconCodes.icon_Bd_Arrow_Chevron_Down}
										height="16px"
										{...dropdownIconProps}
										{...activeStyle(isDropDownOpen, props)}
									/>
								</Box>
							}
							isMenuOpenCallback={(isMenuOpenValue) =>
								menuOpenStatus(isMenuOpenValue)
							}
							isMenuOpen={isDropDownOpen}
							showError={showError}
							textStyles={{
								fontWeight: theme.fontWeights.regular,
							}}
							textHoverStyles={{ fontWeight: theme.fontWeights.regular }}
							{...getEditedProps(props, menu, selectedValueState)}
							{...contextMenuProps}
							onBlur={handleBlur}
							onItemClick={handleItemClick}
							isAggregate={isAggregate}
						/>
					)}
				</Box>
			</div>
		);
	}
);

const Dropdown: FC<DropdownProps> = memo(({ withForm, ...restProps }) => {
	const { name } = restProps;
	if (!withForm) {
		return (
			<CaptionMessage {...restProps}>
				<DropdownWrapper {...restProps} />
			</CaptionMessage>
		);
	}
	return (
		<FormControl name={name} {...restProps}>
			<CaptionMessage {...restProps}>
				<Controller
					name={name}
					defaultValue={restProps.selectedValues}
					render={({ field: { onChange, value }, fieldState: { error } }) => {
						return (
							<DropdownWrapper
								{...restProps}
								onChange={onChange}
								selectedValues={value}
								withForm={withForm}
								error={error}
							/>
						);
					}}
				/>
			</CaptionMessage>
		</FormControl>
	);
});

Dropdown.defaultProps = defaultProps;
Dropdown.displayName = 'Dropdown';
export { DropdownType, DropdownSize };

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
export default configurable(Dropdown);

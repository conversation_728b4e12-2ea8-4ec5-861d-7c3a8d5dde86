/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import {
	FC,
	CSSProperties,
	useState,
	forwardRef,
	useRef,
	memo,
	useEffect,
	ChangeEvent,
} from 'react';
import { useTheme } from 'react-jss';
import classNames from 'classnames';
import { Controller, useFormContext } from 'react-hook-form';

import Box from '@atomic/box';
import { configurable } from '@base/config-provider';
import { setMetaData } from '@base/utils-hooks';
import Button, { ButtonTypes } from '@atomic/button';
import { ApexTheme } from '@base/theme';
import Caption from '@atomic/caption';
import Text, { TextSizes, TextTypes } from '@atomic/text';
import { defaultProps, metadata } from './metadata';
import { TextareaProps, Type } from './types';
import { useTextareaStyle } from './styles';
import { getPropsToStyle } from './helper';
import { useScrollbarStyles } from '@atomic/scrollbar';
import { FormControl, CaptionMessage } from '@composite/form';
import { mergeRefs } from 'react-laag';

const defaultButtonStyles: CSSProperties = {
	width: 'fit-content',
	minWidth: 'fit-content',
	height: 'fit-content',
	padding: '0',
	marginLeft: '5px',
	alignItems: 'flex-start',
	background: 'transparent',
};

const TextareaWrapper: FC<TextareaProps> = memo(
	forwardRef<HTMLTextAreaElement, TextareaProps>((props, ref) => {
		const theme: ApexTheme = useTheme();
		const textareaRef = useRef(null);
		setMetaData('Textarea', metadata);
		const {
			name,
			placeholder,
			id,
			type,
			size,
			disabled,
			onChange,
			onClear,
			isClearable,
			iconProps,
			defaultValue,
			value,
			clearIcon,
			message,
			buttonProps,
			scrollbarProps,
			showValidation,
			onFocus,
			onBlur,
			validationProps,
			onSelect,
			onClick,
			formState,
			error,
			withLabel,
			label,
			labelTextProps,
			labelTextAreaContainerBoxProps,
			placeholderWrapperBoxProps,
			margin,
			padding,
			color,
			backgroundColor,
			borderRadius,
			border,
			font,
			readOnly,
			hasSecondaryIcon,
			secondaryIcon,
			secondaryButtonProps,
			onSecondaryIconClick,
			autoFocus,
			width,
			withForm,
			fontSize,
			showAsterisk,
			enableOnChange,
			maxCharLength,
			...rest
		} = props;
		const methods = useFormContext();
		const [focused, setFocused] = useState(false);
		const [textValue, setTextValue] = useState<string>(defaultValue || value);
		const propsToStyle = getPropsToStyle(theme, props).styleProps;
		const propsToIcon = getPropsToStyle(theme, props).iconStyle;
		const classes = useTextareaStyle({ ...propsToStyle, ...props });
		const scrollStyle = useScrollbarStyles(scrollbarProps);
		const secondaryIconProps = getPropsToStyle(theme, props).secondaryIconStyle;

		useEffect(() => {
			//Some times users force update values using editformstate, in that case onChange will be triggered unnecessarily.
			if (value != textValue) {
				setTextValue(value);
				//This onChange was inititally triggered from handleChange.
				//That would mean that onchange will be triggered when user types out the value and wont be triggered in the case of valuebinding where the user doesnt type the value.
				// Need to do a undefined check to prevent undefined values from triggering onChange
				enableOnChange && value !== undefined && onChange && onChange(value);
			}
		}, [value, enableOnChange]);

		const handleFocus = (e) => {
			setFocused(true);
			if (onFocus) {
				onFocus(e);
			}
		};

		const handleBlur = (e) => {
			setFocused(false);
			if (value != e.target.value) {
				onChange && onChange(e.target.value);
			}
			if (onBlur) {
				onBlur(e);
			}
		};
		const handleTextareaFocus = () => {
			textareaRef.current.focus();
		};
		const handleChange = (e) => {
			e?.stopPropagation();
			setTextValue(e.target.value);
		};
		const handleClear = () => {
			if (!withForm) {
				setTextValue('');
			} else {
				setTextValue('');
				methods.setValue(name, '');
			}
		};
		return (
			<Box
				flexDirection="column"
				margin={margin}
				padding={padding}
				backgroundColor={backgroundColor}
				border={border}
				width={width}
				className={classes.captionTextareaContainer}
				data-component="atomic/textarea"
			>
				<Box
					flexDirection="column"
					rowGap={8}
					{...labelTextAreaContainerBoxProps}
				>
					{withLabel && (
						<Text
							type={TextTypes.Body}
							size={TextSizes.Small}
							color={color || theme.colors.monochrome.ash}
							fontSize={fontSize || font}
							className={classNames(classes.labelText, {
								[classes.required]: showAsterisk || props?.showAsMandatory,
							})}
							inlineTargetId={props?.id}
							{...labelTextProps}
						>
							{label}
						</Text>
					)}
					<Box
						className={classes.textareaWrapper}
						onClick={handleTextareaFocus}
					>
						<textarea
							id={id}
							placeholder={placeholder}
							value={textValue}
							className={classNames(classes.textarea, scrollStyle.scrollbar)}
							disabled={disabled}
							onChange={handleChange}
							onFocus={handleFocus}
							onBlur={handleBlur}
							onSelect={onSelect}
							onClick={onClick}
							name={name}
							readOnly={readOnly}
							autoFocus={autoFocus}
							maxLength={maxCharLength}
							{...rest}
							ref={mergeRefs(ref, textareaRef)}
						></textarea>
						{hasSecondaryIcon && (
							<Button
								title=""
								onClick={onSecondaryIconClick}
								disabled={false}
								buttonType={ButtonTypes.Text}
								iconProps={{
									icon: secondaryIcon,

									color: theme.colors.monochrome.placeholder,

									...secondaryIconProps,
								}}
								buttonStyles={defaultButtonStyles}
								focusStyles={{
									boxShadow: 'none',
								}}
								{...secondaryButtonProps}
							></Button>
						)}
						{!disabled && isClearable && textValue?.length > 0 && (
							<Box width={24}>
								<Button
									title=""
									onClick={handleClear}
									disabled={false}
									buttonType={ButtonTypes.Text}
									iconProps={{
										icon: clearIcon,
										color: theme.colors.monochrome.placeholder,
										...propsToIcon,
									}}
									buttonStyles={defaultButtonStyles}
									focusStyles={{
										boxShadow: 'none',
									}}
									{...buttonProps}
									tabIndex="-1"
								></Button>
							</Box>
						)}
					</Box>
					{focused && showValidation && type !== Type.PRIMARY && (
						<Box className={classes.captionWrapper}>
							<Caption
								isIcon
								message={[error?.message]}
								type="error"
								{...validationProps}
							/>
						</Box>
					)}
				</Box>
			</Box>
		);
	})
);

const Textarea: FC<TextareaProps> = (props) => {
	if (!props.withForm) {
		return (
			<CaptionMessage {...props}>
				<div>
					<TextareaWrapper {...props} />
				</div>
			</CaptionMessage>
		);
	}
	const handleOnChange = (ev: ChangeEvent<HTMLTextAreaElement>, onChange) => {
		onChange.call(null, ev);
		if (props?.onChange) {
			props.onChange(ev);
		}
	};
	return (
		<FormControl name={props.name} {...props}>
			<CaptionMessage {...props}>
				<Controller
					name={props.name}
					defaultValue={props?.defaultValue || props?.value}
					render={({ field: { onChange, value }, fieldState: { error } }) => {
						return (
							<TextareaWrapper
								{...props}
								name={props.name}
								onChange={(ev: ChangeEvent<HTMLTextAreaElement>) =>
									handleOnChange(ev, onChange)
								}
								value={value || ''}
								error={error}
							/>
						);
					}}
				/>
			</CaptionMessage>
		</FormControl>
	);
};

Textarea.defaultProps = defaultProps;
Textarea.displayName = 'Textarea';
export default configurable(Textarea);

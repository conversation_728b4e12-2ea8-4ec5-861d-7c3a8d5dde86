/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { Size, TextareaProps, Type } from './types';
import { IconCodes } from '@atomic/icon';
import { MetaData } from '@base/utils-hooks';
import { TEXTAREA_CSS_PROPS } from './constants';

export const defaultProps: Partial<TextareaProps> = {
	id: '',
	width: '100%',
	clearIcon: IconCodes.icon_Bd_Close_X,
	placeholder: 'Type here',
	disabled: false,
	isClearable: true,
	name: 'textArea',
	readOnly: false,
	hasSecondaryIcon: false,
	withLabel: false,
	label: 'Textarea',
	size: Size.MEDIUM,
};

export const metadata: MetaData = {
	isInlineEditable: true,
	isPublishable: true,
	toolbarType: 'atomic',
	description: 'This is metadata information for textarea component',
	defaultBehavior: {
		onLoad: 'When textarea is Loaded\nDo something\n',
		onUnload: 'When textarea is Refreshed\nDo something\n',
		onMouseOver: 'When textarea is Hovered\nDo something\n',
		onFocus: 'When textarea is Focussed\nDo something\n',
		onBlur: 'When textarea is Focussed out\nDo something\n',
		onChange: 'When textarea is Changed\nDo something\n',
	},
	thumbnail: 'component-thumbnails/text-area.png',
	configurationProps: {
		containerId: '',
		targetMappingEnabled: true,
	},
	cssProps: TEXTAREA_CSS_PROPS,
	editableProps: {
		component: 'Textarea',
		allowedChildrenComponents: ['none'],
		props: [
			{
				props: 'label',
				propLabel: 'Label',
				help: 'Provide a text for the textarea label',
				allowedValues: [
					{
						show: true,
						default: 'Textarea',
						type: 'string',
					},
				],
			},
			{
				props: 'showLabel',
				propLabel: 'Show Label',
				help: 'Choose to show or hide the label',
				allowedValues: [
					{
						show: true,
						default: true,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'value',
				propLabel: 'Value',
				help: 'Provide a value for the input',
				allowedValues: [
					{
						show: true,
						default: '',
						type: 'string',
						dynamic: true,
					},
				],
			},
			{
				props: 'disabled',
				propLabel: 'This element is disabled',
				help: 'Disable the field on selection',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'width',
				propLabel: 'Width',
				help: 'Choose the width of the Text Field',
				allowedValues: [
					{
						show: true,
						default: '624px',
						type: 'string',
					},
				],
			},
			{
				props: 'type',
				propLabel: 'Type',
				help: 'Choose a type from below options',
				allowedValues: [
					{
						show: false,
						default: Type.CAPTION,
						values: Type,
						type: typeof Type,
					},
				],
			},
			{
				props: 'placeholder',
				propLabel: 'Placeholder',
				help: 'Enter a placeholder text',
				allowedValues: [
					{
						show: true,
						default: 'Type here',
						type: 'string',
					},
				],
			},
			{
				props: 'isClearable',
				propLabel: 'Show clear icon',
				help: 'Enable to have a clear option',
				allowedValues: [
					{
						show: true,
						default: true,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'size',
				propLabel: 'Size',
				help: 'Choose the size of the Text Area Field',
				allowedValues: [
					{
						show: true,
						default: Size.MEDIUM,
						values: Size,
						type: typeof Size,
					},
				],
			},
			{
				props: 'rows',
				propLabel: 'Number of rows',
				help: 'Provide the required number of rows for the Text Area Field',
				allowedValues: [
					{
						show: true,
						default: '',
						type: 'number',
					},
				],
			},
			{
				props: 'showAsterisk',
				propLabel: 'Mandatory',
				help: 'Mark this component as required',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'showAsMandatory',
				propLabel: 'Show as mandatory',
				help: 'soft mandatory',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'maxCharLength',
				propLabel: 'Text maximum length',
				help: 'Helps to define max length of the textarea',
				allowedValues: [
					{
						show: true,
						default: '',
						type: 'number',
					},
				],
			},
		],
	},
};

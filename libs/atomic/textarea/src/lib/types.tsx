/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-types */
/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */
import { CSSProperties, FocusEvent, SyntheticEvent } from 'react';
import { IIconProps } from '@atomic/icon';
import { TextProps } from '@atomic/text';
import { ICaptionProps } from '@atomic/caption';
import { IButtonProps } from '@atomic/button';
import { ScrollbarProps } from '@atomic/scrollbar';
import { FieldValues, UseFormReturn } from 'react-hook-form';
import { BoxProps } from '@atomic/box';
import { CssStyleProps } from '@base/global-events';
import { InputCSSProps } from '@atomic/input';

export type TextareaLabel = string;

export enum Type {
	PRIMARY = 'primary',
	CAPTION = 'caption',
	SUCCESS = 'success',
	ERROR = 'error',
	WARNING = 'warning',
}

export enum Size {
	SMALL = 'small',
	MEDIUM = 'medium',
	LARGE = 'large',
}
type MessageArr = string;

export interface TextareaProps
	extends Partial<UseFormReturn<FieldValues, object>>,
		CssStyleProps,
		InputCSSProps {
	name: string;
	withForm?: boolean;
	withLabel?: boolean;
	label?: string;
	labelTextProps?: TextProps;
	labelTextAreaContainerBoxProps?: BoxProps;
	placeholderWrapperBoxProps?: BoxProps;
	id?: string;
	type?: string;
	size?: Size;
	clearIcon?: string;
	placeholder?: string;
	defaultValue?: string;
	value?: string;
	width?: string;
	height?: string;
	disabled?: boolean;
	isClearable?: boolean;
	showValidation?: boolean;
	message?: Array<MessageArr>;
	iconProps?: IIconProps;
	validationProps?: ICaptionProps;
	textProps?: TextProps;
	buttonProps?: IButtonProps;
	disabledProps?: CSSProperties;
	textAreaFocusProps?: CSSProperties;
	captionTextareaContainerProps?: CSSProperties;
	textareaWrapperProps?: CSSProperties;
	textareaProps?: CSSProperties;
	scrollbarProps?: ScrollbarProps;
	error?: any;
	secondaryIcon?: string;
	hasSecondaryIcon?: boolean;
	secondaryIconProps?: IIconProps;
	secondaryButtonProps?: IButtonProps;
	readOnly?: boolean;
	autoFocus?: boolean;
	formStylesEnabled?: boolean;
	showAsterisk?: boolean;
	rows?: number;
	onSecondaryIconClick?: (event: React.MouseEvent<HTMLElement>) => void;
	onChange?: (event: React.ChangeEvent<HTMLInputElement> | string) => void;
	onClear?: (event: React.MouseEvent<HTMLElement>) => void;
	onFocus?: (event: FocusEvent<HTMLTextAreaElement>) => void;
	onBlur?: (event: FocusEvent<HTMLTextAreaElement>) => void;
	onSelect?: (event: SyntheticEvent<HTMLTextAreaElement>) => void;
	onClick?: (event: React.MouseEvent<HTMLElement>) => void;
	enableOnChange?: boolean;
	showAsMandatory?: boolean; // soft mandatory
	maxCharLength?: number;
}

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the Repeateritem component
import React, { FC, ReactElement, memo, useMemo } from 'react';
import { useTheme } from 'react-jss';
import { ApexTheme } from '@base/theme';
import { IRepeateritemProps, Row } from './types';
import { styles } from './style';
import Box, { BoxProps } from '@atomic/box';
import Icon, { IconCodes } from '@base/icon';
import _get from 'lodash/get';
import { useRepeaterItemStyles } from './style';

import classNames from 'classnames';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface RepeateritemProps extends IRepeateritemProps {
	isSelected?: boolean;
}

const CompositionItem: FC<RepeateritemProps> = (props: RepeateritemProps) => {
	const theme: ApexTheme = useTheme();
	const componentStyles = styles(theme, props);
	const classes = useRepeaterItemStyles({ ...props, isSelected: false });
	const {
		index,
		rowData,
		parentName,
		fieldErrors = [],
		showDelete,
		showItemName,
		itemName,
		onClickDelete,
		refreshKey,
	} = props;

	const error = useMemo(() => {
		return fieldErrors.length === 0
			? {}
			: {
					...fieldErrors[0],
					message:
						fieldErrors.length > 1
							? 'There are multiple errors'
							: fieldErrors[0].message,
			  };
	}, [JSON.stringify(fieldErrors)]);

	const recursiveFieldMapping = (
		template: ReactElement,
		rowData: Row,
		index: number,
		parent: string,
		fn: (
			element: ReactElement,
			parent: string
		) => React.ReactElement<any, string | React.JSXElementConstructor<any>>
	) => {
		return React.Children.map(template, (child: ReactElement) => {
			// if (!React.isValidElement(child)) return child;

			// Check if the child has children and recursively map
			if (child.props && child.props.children) {
				const parentName = child?.props?.name
					? `${parent}.${child.props.name}`
					: parent;

				child = React.cloneElement(child, {
					rowData,
					microEditable: index === 0,
					hideAddElement: index !== 0,
					repeaterIndex: index,
					children: recursiveFieldMapping(
						child.props.children,
						rowData,
						index,
						parentName,
						fn
					),
				});
			}
			return fn(child, parent);
		});
	};

	const setNameAttribute = (element: ReactElement, parent: string) => {
		// if (!React.isValidElement(element)) return element;

		if (element.props && element.props.name) {
			let val = _get(rowData, `${element.props.name}`);
			// This is done to prevent overriding of value binding
			if (element.props.value?.runtimeExpression) {
				val = element.props.value ?? val;
			}
			return React.cloneElement(element, {
				name: `${parent}.${element.props.name}`,
				value: val || null,
				rowData,
				microEditable: index === 0,
				hideAddElement: index !== 0,
				repeaterIndex: index,
			});
		} else {
			return React.cloneElement(element, {
				rowData,
				microEditable: index === 0,
				hideAddElement: index !== 0,
				repeaterIndex: index,
			});
		}
	};

	const RenderContent = useMemo(() => {
		const Template = props?.renderProps?.(null, null, null, true);
		return recursiveFieldMapping(
			Template,
			rowData,
			index,
			`${parentName}[${index}]`,
			setNameAttribute
		);
	}, [JSON.stringify(rowData), index, parentName, refreshKey]);

	return (
		<Box
			data-component="atomic/repeateritem"
			className={classNames(classes.card, classes.cardOnHover, {
				[classes.error]: error?.message,
			})}
		>
			<span>
				{showItemName && (
					<Box className={classes.cardTitle}>
						{`${itemName || ''} ${index + 1}`}{' '}
					</Box>
				)}
				<Box {...(componentStyles as BoxProps)}>{RenderContent}</Box>
				<Box
					className={classNames(classes.selection, {
						[classes.selectedFooter]: false,
					})}
				>
					{showDelete && (
						<Box className={classes.delete} onClick={() => onClickDelete?.()}>
							<Icon
								icon={IconCodes.icon_Bd_Delete}
								iconStyle={{
									width: '23px',
									height: '23px',
									fontSize: '23px',
								}}
							/>
						</Box>
					)}
				</Box>
			</span>
		</Box>
	);
};

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
export default memo(CompositionItem);

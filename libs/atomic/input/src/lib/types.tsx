/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { CSSProperties } from 'react';
import { IFormChildProps } from '@base/utils-hooks';
import {
	ChangeEvents,
	CssStyleProps,
	FocusEvents,
	KeyboardEvents,
	MouseEvents,
} from '@base/global-events';
import { IIconProps } from '@atomic/icon';
import { CaptionMessageType } from '@composite/form';

export enum InputSize {
	LARGE = 'Large',
	MEDIUM = 'Medium',
	SMALL = 'Small',
}

export enum InputWidth {
	LARGE = 'Large',
	MEDIUM = 'Medium',
	SMALL = 'Small',
}

export enum InputTextAlignment {
	LEFT = 'left',
	RIGHT = 'right',
}

export enum DateFormat {
	'MM/DD/YYYY' = 'MM/dd/yyyy',
	'MM-DD-YYYY' = 'MM-dd-yyyy',
	'YYYY/MM/DD' = 'yyyy/MM/dd',
	'YYYY-MM-DD' = 'yyyy-MM-dd',
	'DD MMM YYYY' = 'dd MMM yyyy',
}

export enum TimeFormat {
	'HH:MM:SS A' = 'HH:MM:SS A',
	'HH:MM A' = 'HH:MM A',
}

export enum DateFormatMask {
	'MM/DD/YYYY' = '99/99/9999',
	'MM-DD-YYYY' = '99-99-9999',
	'YYYY/MM/DD' = '9999/99/99',
	'YYYY-MM-DD' = '9999-99-99',
	'DD-Mon-YYYY' = '99 aaa 9999',
}

export enum TimeFormatMask {
	'HH:MM:SS XM' = '99:99:99 aa',
	'HH:MM XM' = '99:99 aa',
}

export enum CaptionTypes {
	SUCCESS = 'success',
	ERROR = 'error',
	WARNING = 'warning',
	CAPTION = 'caption',
}

type PatterRuleType = {
	value: RegExp;
	message: string;
};

export interface IInputProps
	extends ChangeEvents,
		InputCSSProps,
		FocusEvents,
		KeyboardEvents,
		MouseEvents,
		CssStyleProps,
		IFormChildProps {
	id?: string;
	name: string;
	placeholder?: string;
	value?: any;
	required?: boolean;
	initialValue?: string;
	disabled?: boolean;
	readonly?: boolean;
	type?: HTMLInputType | string;
	allowedCharacters?: AllowedCharacterType;
	customPattern?: string;
	textTransform?: TextTransform;
	inputSize?: InputSize;
	isValidInput?: (isValid: boolean) => boolean;
	maxCharLength?: number;
	minCharLength?: number;
	withForm?: boolean;
	error?: any;
	inputStyles?: CSSProperties;
	inputContainerProps?: CSSProperties;
	inputHoverStyles?: CSSProperties;
	inputFocusStyles?: CSSProperties;
	inputActiveStyles?: CSSProperties;
	inputPlaceholderStyles?: CSSProperties;
	validate?: any;
	readOnlyStyles?: CSSProperties;
	patternRule?: PatterRuleType;
	onChangeTrigger?: (any) => void;
	arrowPosition?: string;
	width?: InputWidth | string;
	rightIconOnFocus?: boolean;
	inputSelection?: any;

	autoComplete?: InputAutocomplete;

	//prefix and suffix props
	inputSuffix?: () => JSX.Element;
	inputPrefix?: () => JSX.Element;
	onLeftIconClick?: (event: React.MouseEvent) => void;
	onRightIconClick?: (event: React.MouseEvent) => void;
	inputPrefixProps?: CSSProperties;
	inputSuffixProps?: CSSProperties;

	//label props
	showLabel?: boolean;
	label?: string;
	labelProps?: CSSProperties;
	mandatory?: boolean;
	showAsterisk?: boolean;
	showAsMandatory?: boolean; // soft mandatory

	// caption props
	showCaption?: boolean;
	showDefaultSuffix?: boolean;
	captionMessage?: Array<string>;
	captionMessageType?: CaptionMessageType;
	captionProps?: CSSProperties;
	captionContainerStyles?: CSSProperties;
	captionTextStyles?: CSSProperties;
	captionIconStyles?: IIconProps;
	captionArrowStyles?: any;
	innerRef?: any;
	leftIconProps?: IIconProps;
	rightIconProps?: IIconProps;
	showCaptionMessage?: boolean;
	ref?: any;
	onInactiveEvt?: (event: React.ChangeEvent<HTMLInputElement>) => void;
	onActiveEvt?: (event: React.ChangeEvent<HTMLInputElement>) => void;
	formStylesEnabled?: boolean;

	labelEditable?: boolean;
	onLabelEdit?: (label: string) => void;

	enableOnChange?: boolean;
}

export type InputLabel =
	| 'input'
	| 'rightIcon'
	| 'leftIcon'
	| 'inputContainer'
	| 'label'
	| 'required'
	| 'formInputError';

export type InputAutocomplete = 'on' | 'off';

export interface InputIconProps extends IInputProps {
	textValue: string;
}

export interface InputCSSProps {
	inpFontFamily?: string;
	inpFontWeight?: string;
	inpFontSize?: string;
	inpLetterSpacing?: string;
	inpLineHeight?: string;
	inpColor?: string;
	inpTextAlign?: string;
	foregroundColor?: string;
}

export enum HTMLInputType {
	TEXT = 'text',
	NUMBER = 'number',
}

export enum AllowedCharacterType {
	TEXT_ONLY = 'text_only',
	NUMBER_ONLY = 'number_only',
	TEXT_AND_NUMBER = 'text_and_number',
	ALL = 'all',
	CUSTOM = 'custom',
}
export enum AllowedCharacterOptions {
	TEXT_ONLY = AllowedCharacterType.TEXT_ONLY,
	NUMBER_ONLY = AllowedCharacterType.NUMBER_ONLY,
	TEXT_AND_NUMBER = AllowedCharacterType.TEXT_AND_NUMBER,
	ALL = AllowedCharacterType.ALL,
}

export enum TextTransform {
	UPPERCASE = 'uppercase',
	LOWERCASE = 'lowercase',
	CAPITALIZE = 'capitalize',
	NONE = 'none',
}

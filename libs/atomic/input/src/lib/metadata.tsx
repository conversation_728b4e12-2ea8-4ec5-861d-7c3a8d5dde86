/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import {
	InputSize,
	InputWidth,
	InputTextAlignment,
	HTMLInputType,
	AllowedCharacterType,
	TextTransform,
	AllowedCharacterOptions,
} from './types';
import {
	mouseEventMetadataProps,
	focuseEventMetadataProps,
	changeEventMetadataProps,
	keyboardEventMetadataProps,
} from '@base/global-events';
import {
	MetaData,
	PropertyConfiguration,
	SubMetaData,
	SubMetaDataDefinition,
} from '@base/utils-hooks';
import { INPUT_CSS_PROPS } from './constant';

export const defaultInputProps = {
	inputSize: InputSize.SMALL,
	width: InputWidth.LARGE,
	withForm: false,
	showLabel: false,
	label: 'Inputbox',
	mandatory: false,
	disabled: false,
};

export const defaultMetadata: MetaData = {
	isInlineEditable: true,
	isPublishable: true,
	toolbarType: 'atomic',
	description: 'This is metadata information for input component',
	defaultBehavior: {
		onLoad: 'When input is Loaded\nDo something\n',
		onUnload: 'When input is Refreshed\nDo something\n',
		onHover: 'When input is Hovered\nDo something\n',
		onFocus: 'When input is Focussed\nDo something\n',
		onBlur: 'When input is Focussed out\nDo something\n',
		onChange: 'When input is Changed\nDo something\n',
		onKeyPress: 'When key is Pressed\nDo something\n',
	},
	thumbnail: 'component-thumbnails/input.png',
	configurationProps: {
		containerId: '',
		targetMappingEnabled: true,
	},
	cssProps: INPUT_CSS_PROPS,
	editableProps: {
		component: 'Input',
		allowedChildrenComponents: ['none'],
		props: [
			{
				props: 'label',
				propLabel: 'Label',
				help: 'Provide a text for the input label',
				allowedValues: [
					{
						show: true,
						default: 'Inputbox',
						type: 'string',
					},
				],
			},
			{
				props: 'showLabel',
				propLabel: 'Show Label',
				help: 'Choose to show or hide the label',
				allowedValues: [
					{
						show: true,
						default: true,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'value',
				propLabel: 'Value',
				help: 'Provide a value for the input',
				allowedValues: [
					{
						show: true,
						default: '',
						type: 'string',
						dynamic: true,
					},
				],
			},
			{
				props: 'disabled',
				propLabel: 'This element is disabled',
				help: 'Enable/Disable input',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'width',
				propLabel: 'Width',
				help: 'Choose the width of the Text Field',
				allowedValues: [
					{
						show: true,
						default: '304px',
						type: 'string',
					},
				],
			},
			{
				props: 'inputSize',
				propLabel: 'Size',
				help: 'Choose the size of the Text Field',
				allowedValues: [
					{
						show: true,
						default: InputSize.SMALL,
						values: InputSize,
						type: typeof InputSize,
					},
				],
			},
			{
				props: 'inputTextAlignment',
				propLabel: 'Alignment',
				help: 'Choose the alignment of the text',
				allowedValues: [
					{
						show: false,
						default: InputTextAlignment,
						values: InputTextAlignment.LEFT,
						type: typeof InputTextAlignment.LEFT,
					},
				],
			},
			{
				props: 'placeholder',
				propLabel: 'Placeholder',
				help: 'Enter the placeholder text for the input',
				allowedValues: [
					{
						show: true,
						default: 'Type here',
						type: 'string',
					},
				],
			},
			{
				props: 'showAsterisk',
				propLabel: 'Mandatory',
				help: 'Mark the component as required',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'showAsMandatory',
				propLabel: 'Show as mandatory',
				help: 'soft mandatory',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'rightIconOnFocus',
				propLabel: 'Right Icon Only on Focus',
				help: 'Set true if right icon should display only when focussed',
				allowedValues: [
					{
						show: false,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'type',
				propLabel: 'Type',
				help: 'Choose the type of the Text Field',
				allowedValues: [
					{
						show: true,
						default: HTMLInputType.TEXT,
						values: HTMLInputType,
						type: typeof HTMLInputType,
					},
				],
			},
		],
		...mouseEventMetadataProps,
		...focuseEventMetadataProps,
		...changeEventMetadataProps,
		...keyboardEventMetadataProps,
		subComponents: [],
	},
};

const customEditablePropsForTypeText = [
	{
		props: 'maxCharLength',
		propLabel: 'Text maximum length',
		help: 'Provide maximum length for the text input',
		allowedValues: [
			{
				show: true,
				default: '',
				type: 'number',
			},
		],
	},
	{
		props: 'allowedCharacters',
		propLabel: 'Allowed Characters',
		help: 'Choose allowed characters for the input',
		allowedValues: [
			{
				show: true,
				default: AllowedCharacterType.ALL,
				values: AllowedCharacterOptions,
				type: typeof AllowedCharacterType,
			},
		],
	},
	{
		props: 'textTransform',
		propLabel: 'Text Transform',
		help: 'Choose the text transform for the input',
		allowedValues: [
			{
				show: true,
				default: TextTransform.NONE,
				values: TextTransform,
				type: typeof TextTransform,
			},
		],
	},
];

export const customEditableProps: Record<string, PropertyConfiguration[]> = {
	['type' + HTMLInputType.TEXT]: customEditablePropsForTypeText,
	type: customEditablePropsForTypeText,
};

const filterProps = (keys: string[], append = true) => {
	const props = append ? [...defaultMetadata.editableProps.props] : [];
	keys.forEach((key) => {
		const customProps = customEditableProps[key];
		customProps.forEach((customProp) => {
			if (append) {
				const index = defaultMetadata.editableProps.props.findIndex(
					(prop) => prop.props == customProp.props
				);
				index >= 0
					? props.splice(index, 1, customProp)
					: props.splice(props.length, 0, customProp);
			} else {
				props.push(customProp);
			}
		});
	});
	return props;
};

const withInputTypeAsTextDefinition: SubMetaDataDefinition = {
	type: { type: HTMLInputType.TEXT },
	metaData: {
		...defaultMetadata,
		editableProps: {
			...defaultMetadata.editableProps,
			props: filterProps(['type' + HTMLInputType.TEXT]),
		},
	},
};

const withInputTypeAsEmptyDefinition: SubMetaDataDefinition = {
	type: { type: '' },
	metaData: {
		...defaultMetadata,
		editableProps: {
			...defaultMetadata.editableProps,
			props: filterProps(['type']),
		},
	},
};

const withTypeAsTextMeta: SubMetaData = {
	name: 'type',
	definition: withInputTypeAsTextDefinition,
	priority: 1,
};

const withTypeAsEmptyMeta: SubMetaData = {
	name: 'type',
	definition: withInputTypeAsEmptyDefinition,
	priority: 1,
};

export const metadata: MetaData = {
	...defaultMetadata,
	subMetaData: [withTypeAsTextMeta, withTypeAsEmptyMeta],
};

import Constants from './constant';
import { AllowedCharacterType } from './types';

export const getPlaceHolder = (placeholder) => {
	return placeholder || Constants.TYPE_HERE;
};

export const getPattern = (
	type: AllowedCharacterType,
	customPattern: string
): string | undefined => {
	switch (type) {
		case AllowedCharacterType.TEXT_ONLY:
			return '^[A-Za-z]*$';
		case AllowedCharacterType.NUMBER_ONLY:
			return '^[0-9]*$';
		case AllowedCharacterType.TEXT_AND_NUMBER:
			return '^[A-Za-z0-9]*$';
		case AllowedCharacterType.CUSTOM:
			return customPattern;
		case AllowedCharacterType.ALL:
		default:
			return undefined;
	}
};

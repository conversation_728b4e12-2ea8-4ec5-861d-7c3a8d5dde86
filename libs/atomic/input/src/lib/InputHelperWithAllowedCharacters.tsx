/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
	ChangeEvent,
	FC,
	FormEvent,
	forwardRef,
	memo,
	useEffect,
	useState,
} from 'react';
import Text, { TextSizes, TextTypes } from '@atomic/text';
import Box from '@atomic/box';
import { IInputProps } from './types';
import { useInputStyles } from './styles/useInputStyles';
import InputIcon from './InputIcon';
import { ApexTheme } from '@base/theme';
import Icon from '@atomic/icon';
import { useTheme } from 'react-jss';
import constant from './constant';
import classNames from 'classnames';
import { getPattern } from './helper';

const InputHelperWithAllowedCharacters: FC<IInputProps> = forwardRef<
	HTMLInputElement,
	IInputProps
>((props, ref) => {
	const {
		showLabel,
		label,
		labelProps,
		mandatory,
		showAsterisk,
		inputPrefix,
		onLeftIconClick,
		disabled,
		readonly,
		initialValue,
		placeholder,
		maxCharLength,
		allowedCharacters,
		type,
		minCharLength,
		inputSuffix,
		onRightIconClick,
		showDefaultSuffix,
		showCaption,
		captionMessageType,
		name,
		validationCriteria,
		onActiveEvt,
		onInactiveEvt,
		showCaptionMessage,
		error,
		onBlur,
		onFocus,
		autoComplete,
		onChange,
		onChangeTrigger,
		children,
		value,
		margin,
		padding,
		color,
		backgroundColor,
		border,
		font,
		width,
		rightIconOnFocus,
		rightIconProps,
		labelEditable,
		onLabelEdit,
		inputSelection,
		fontSize,
		customPattern,
		...rest
	} = props;
	const classes = useInputStyles(props);
	const theme: ApexTheme = useTheme();
	const [inputValue, setInputValue] = useState<string>(
		initialValue ? initialValue : value
	);
	const [textValue, setTextValue] = useState<string>(
		initialValue ? initialValue : value
	);
	const [isInputFocused, setIsInputFocused] = useState(false);
	const [isInputHovered, setIsInputHovered] = useState(false);
	const pattern = getPattern(allowedCharacters, customPattern);

	useEffect(() => {
		const regex = pattern ? new RegExp(pattern) : null;
		if (regex && regex.test(value)) {
			setInputValue((prev) => value);
		} else if (!regex) {
			setInputValue(value);
		}
	}, [value]);

	const handleInput = (e: FormEvent<HTMLInputElement>) => {
		const onChangeInputValue = (e.target as HTMLInputElement).value;
		const regex = pattern ? new RegExp(pattern) : null;
		if (regex && !regex.test(onChangeInputValue)) {
			setInputValue((prev) => prev || '');
			return;
		}
		setInputValue(onChangeInputValue);
	};

	const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
		setTextValue(e.target.value);
		if (!disabled || !readonly) {
			onChange && onChange(e);
			onChangeTrigger?.(e?.target?.value);
		}
	};

	// Check if there is any other input apart from keypress and invoke onChange
	useEffect(() => {
		if (inputSelection !== undefined) {
			onChange?.({ target: { value: inputSelection } });
			onChangeTrigger?.(inputSelection);
		}
	}, [inputSelection]);

	const handleOnFocus = (e: ChangeEvent<HTMLInputElement>) => {
		onActiveEvt(e);
		if (rightIconOnFocus) setIsInputFocused(true);
	};

	const handleOnBlur = (e: ChangeEvent<HTMLInputElement>) => {
		onInactiveEvt(e);
		if (rightIconOnFocus) setIsInputFocused(false);
	};

	const handleMouseEnter = () => {
		if (rightIconOnFocus) setIsInputHovered(true);
	};

	const handleMouseLeave = () => {
		if (rightIconOnFocus) setIsInputHovered(false);
	};

	const handleRightIconClick = (event: React.MouseEvent<HTMLDivElement>) => {
		onRightIconClick && onRightIconClick(event);
	};

	return (
		<Box
			className={classes.inputContainer}
			flexDirection="column"
			margin={margin}
			padding={padding}
			backgroundColor={backgroundColor}
			border={border}
			width={width}
		>
			{showLabel && (
				<Text
					size={TextSizes.Small}
					type={TextTypes.Body}
					className={classNames(classes.label, {
						[classes.required]: showAsterisk || props?.showAsMandatory,
					})}
					fontSize={fontSize || font}
					color={color || theme.colors.monochrome.ash}
					inlineTargetId={props?.id}
					onTextChange={onLabelEdit}
					{...labelProps}
				>
					{label}
				</Text>
			)}
			<Box
				alignItems="center"
				onMouseEnter={handleMouseEnter}
				onMouseLeave={handleMouseLeave}
			>
				{inputPrefix && inputPrefix() && (
					<Box className={classes.leftIcon} onClick={onLeftIconClick}>
						{inputPrefix && inputPrefix()}
					</Box>
				)}
				<input
					disabled={disabled}
					className={classes.input}
					defaultValue={initialValue}
					placeholder={placeholder}
					type={type}
					maxLength={maxCharLength}
					minLength={minCharLength}
					onFocus={handleOnFocus}
					readOnly={readonly}
					onBlur={handleOnBlur}
					name={name}
					autoComplete={autoComplete}
					value={inputValue}
					onChange={handleChange}
					onInput={handleInput}
					{...rest}
					ref={ref as unknown as any}
				/>
				{rightIconProps && (
					<Box className={classes.rightIcon} onClick={handleRightIconClick}>
						<Icon {...rightIconProps} />
					</Box>
				)}
				{inputSuffix && inputSuffix() ? (
					<Box className={classes.rightIcon} onClick={handleRightIconClick}>
						{inputSuffix && inputSuffix()}
					</Box>
				) : (
					showDefaultSuffix &&
					(rightIconOnFocus ? isInputFocused || isInputHovered : true) && (
						<Box className={classes.rightIcon} onClick={handleRightIconClick}>
							<InputIcon
								showCaptionMessage={showCaptionMessage}
								showCaption={showCaption}
								captionMessageType={captionMessageType}
								textValue={textValue}
								{...props}
							></InputIcon>
						</Box>
					)
				)}
			</Box>
		</Box>
	);
});

InputHelperWithAllowedCharacters.defaultProps = {
	autoComplete: 'off',
	placeholder: constant.TYPE_HERE,
};
export default memo(InputHelperWithAllowedCharacters);

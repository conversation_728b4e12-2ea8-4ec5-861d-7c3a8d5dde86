import { Story, Meta } from '@storybook/react';
import Repeater, { RepeaterProps } from './repeater';
import { defaultProps } from './metadata';
import faker from '@faker-js/faker';
import RepeaterItem from '@atomic/repeateritem';
import GridCol from '@atomic/grid-col';
import Text from '@atomic/text';
import Form, { IFormMode } from '@composite/form';
import Button, { HTMLButtonTypes } from '@atomic/button';
import Input from '@atomic/input';
import { joiResolver } from '@hookform/resolvers/joi';
import Joi from 'joi';

import {
	DisplayTypeTypes,
	LayoutType,
	SelectionModeType,
	VariantType,
} from './types';
import { useFormContext } from 'react-hook-form';
export default {
	component: Repeater,
	title: 'Atomic/Repeater',
	argTypes: {
		id: { type: 'number' },
	},
} as Meta;

const getRandomFrom = (array) =>
	array[Math.floor(Math.random() * array.length)];

const createRows = (range) => {
	const now = Date.now();
	const rows = [];
	for (let i = 0; i < range; i++) {
		rows.push({
			id: i?.toString(),
			name: faker.name.findName(),
			valid: getRandomFrom([true, false, null]),
			avatar: faker.image.avatar(),
			title: `Task #${i + 1}`,
			complete: Math.random() * 10,
			area: faker.name.jobArea(),
			country: faker.address.country(),
			client: faker.company.companyName(),
			contact: faker.internet.exampleEmail(),
			budget: 500 + Math.random() * 10500,
			assignee: faker.name.findName(),
			progress: Math.random() * 100,
			startTimestamp: now - Math.round(Math.random() * 1e10),
			endTimestamp: now + Math.round(Math.random() * 1e10),
			email: faker.internet.email(),
			phonenumber: faker.phone.phoneNumber(),
			rating: Math.floor(Math.random() * 5 + 1),
			percent: Math.floor(Math.random() * 91 + 10),
			status: getRandomFrom(['Success', 'Error', 'Pending', 'Rejected']),
			amount: faker.finance.amount(),
			ssn: getRandomFrom([
				'*********',
				'*********',
				'***********',
				'*********',
				'***********',
				'*********',
			]),
			url: faker.image.imageUrl(),
			duration: Math.floor(Math.random() * 10000000 + 100000),
			date: getRandomFrom(['1997-07-16', '1997-08-16', '1997-09-16']),
			datetime: faker.datatype.datetime().toString(),
		});
	}
	return rows;
};
const Template: Story<RepeaterProps & HTMLButtonElement> = (args) => (
	<Repeater {...args}></Repeater>
);

export const RepeaterStory = Template.bind({});
RepeaterStory.args = {
	...defaultProps,
};

export const SimpleRepeater = (args) => {
	const rowData = createRows(13);
	const expressionSample = (item) => item?.name;
	return (
		<Repeater rowData={rowData} {...args}>
			<RepeaterItem
				renderProps={(data) => (
					<>
						{/* <GridRow> */}
						<GridCol>
							<Text text={`Name: ${expressionSample(data)}`} />
						</GridCol>
						{/* </GridRow> */}
					</>
				)}
			></RepeaterItem>
		</Repeater>
	);
};
SimpleRepeater.args = {
	id: '1',
	layout: LayoutType.Vertical,
	selectionMode: SelectionModeType.Multiple,
	onSelectionChange: (value) => console.log('Selection: ', value),
	defaultSelected: 1,
	enableSearch: true,
};
const expressionSample = (item) => item?.name;
export const CarouselView = (args) => {
	const rowData = createRows(13);
	return (
		<Repeater rowData={rowData} {...args}>
			<RepeaterItem
				renderProps={(data) => (
					<>
						{/* <GridRow> */}
						<GridCol>
							<Text text={`Name: ${expressionSample(data)}`} />
						</GridCol>
						{/* </GridRow> */}
					</>
				)}
			></RepeaterItem>
		</Repeater>
	);
};
CarouselView.args = {
	id: '1',
	laout: LayoutType.Vertical,
	displayType: DisplayTypeTypes.Carousal,
	selectionMode: SelectionModeType.Single,
	onSelectionChange: (value) => console.log('Selection: ', value),
	defaultSelected: 1,
};

const ButtonWrapper = () => {
	const context = useFormContext();
	// console.log('context in Repeater >>>>>>', context.getValues());
	return (
		<Button
			title="Reset"
			onClick={() => {
				context.reset();
			}}
		></Button>
	);
};

export const RepeaterWithForm: Story<RepeaterProps & HTMLInputElement> = (
	args
) => {
	const schema = Joi.object({
		repeater: Joi.array().min(1),
	});
	const onSubmit = (data) => console.log('data of event>>>>>>>>', data);
	return (
		<Form
			onSubmit={onSubmit}
			defaultValues={{
				repeater: ['1'],
			}}
			resolver={joiResolver(schema)}
			validationTrigger={IFormMode.onBlur}
		>
			<Repeater {...args}>
				<RepeaterItem
					renderProps={(data) => (
						<>
							{/* <GridRow> */}
							<GridCol>
								<Text text={`Name: ${expressionSample(data)}`} />
							</GridCol>
							{/* </GridRow> */}
						</>
					)}
				></RepeaterItem>
			</Repeater>
			<br />
			<Button type={HTMLButtonTypes.Submit} title="submit"></Button>
			<ButtonWrapper />
			<br />
		</Form>
	);
};

RepeaterWithForm.args = {
	id: '1',
	name: 'repeater',
	withForm: true,
	rowData: createRows(5),
	displayType: DisplayTypeTypes.Card,
	selectionMode: SelectionModeType.Multiple,
};

export const RepeaterWithForm1NComposition: Story<
	RepeaterProps & HTMLInputElement
> = (args) => {
	const schema = Joi.object({
		dependent: Joi.array().min(1),
		ownerName: Joi.string(),
	});
	const onSubmit = (data) => console.log('data of event>>>>>>>>', data);
	return (
		<Form
			onSubmit={onSubmit}
			defaultValues={{
				ownerName: 'John Smith',
				dependent: [
					{
						id: '1',
						name: 'user 1',
						age: 10,
					},
					{
						id: '2',
						name: 'user 2',
						age: 20,
					},
				],
			}}
			resolver={joiResolver(schema)}
			validationTrigger={IFormMode.onBlur}
		>
			<Input name="ownerName" showLabel={true} label="Owner" withForm />
			<br />
			<Repeater {...args}>
				<RepeaterItem
					renderProps={(data) => (
						<GridCol>
							<Text text={`Name: ${expressionSample(data)}`} />
							<GridCol>
								<Input name="name" showLabel={true} label="" withForm />
							</GridCol>
						</GridCol>
					)}
				></RepeaterItem>
			</Repeater>
			<br />
			<Button type={HTMLButtonTypes.Submit} title="submit"></Button>
			<ButtonWrapper />
			<br />
		</Form>
	);
};

RepeaterWithForm1NComposition.args = {
	id: '1',
	name: 'dependent',
	withForm: true,
	isCreateUpdateData: true,
	displayType: DisplayTypeTypes.Card,
	layout: LayoutType.Vertical,
	itemName: 'Dependent',
	showItemName: true,
};

export const RepeaterWithHeader: Story<RepeaterProps & HTMLInputElement> = (
	args
) => {
	return <RepeaterWithForm1NComposition {...args} />;
};
RepeaterWithHeader.args = {
	...RepeaterWithForm1NComposition.args,
	variant: VariantType['With header'],
};

export const RepeaterWithErrors = (args) => {
	const rowData = createRows(3);
	const expressionSample = (item) => item?.name;
	return (
		<Repeater rowData={rowData} {...args}>
			<RepeaterItem
				renderProps={(data) => (
					<>
						{/* <GridRow> */}
						<GridCol>
							<Text text={`Name: ${expressionSample(data)}`} />
						</GridCol>
						{/* </GridRow> */}
					</>
				)}
			></RepeaterItem>
		</Repeater>
	);
};
RepeaterWithErrors.args = {
	id: '1',
	layout: LayoutType.Vertical,
	enableSearch: true,
	errors: [
		{
			code: 'ValidateRuleERROR',
			field: 'primaryOwner.owner',
			detail: 'Proof of identity is mandatory',
			message: 'Proof of identity  is mandatory',
			rejectedValue: null,
		},
		{
			code: 'ValidateRuleERROR',
			field: 'secondaryOwner.0.beneficiary.documentType',
			detail: 'Document Type is mandatory',
			message: 'Document Type is mandatory',
			rejectedValue: null,
		},
		{
			code: 'ValidateRuleERROR',
			field: 'secondaryOwner.1.beneficiary.relationship',
			detail: 'Relationship Type is mandatory',
			message: 'Relationship Type is mandatory',
			rejectedValue: null,
		},
		{
			code: 'ValidateRuleERROR',
			field: 'secondaryOwner.1.beneficiary.percentage',
			detail: 'Beneficiary percentage sum is not 100',
			message: 'Beneficiary percentage sum is not 100',
			rejectedValue: null,
		},
	],
	errorFieldName: 'secondaryOwner',
};

export const RepeaterWithFooter = (args) => {
	const rowData = createRows(5);
	const expressionSample = (item) => item?.name;
	return (
		<Repeater rowData={rowData} {...args}>
			<RepeaterItem
				renderProps={(data) => (
					<>
						{/* <GridRow> */}
						<GridCol>
							<Text text={`Name: ${expressionSample(data)}`} />
						</GridCol>
						{/* </GridRow> */}
					</>
				)}
			></RepeaterItem>
		</Repeater>
	);
};
RepeaterWithFooter.args = {
	id: '1',
	layout: LayoutType.Vertical,
	selectionMode: SelectionModeType.Single,
	onSelectionChange: (value) => console.log('Selection: ', value),
	defaultSelected: '1',
	enableSearch: true,
	selectionStyle: 'radio',
	variant: VariantType['With footer'],
};

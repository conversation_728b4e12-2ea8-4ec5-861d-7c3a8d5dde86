/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { BoxProps } from '@atomic/box';
import { ImageProps } from '@atomic/image';
import { CssStyleProps } from '@base/global-events';
import { CSSProperties } from 'react';
import {
	FieldValues,
	UseFieldArrayInsert,
	UseFieldArrayRemove,
} from 'react-hook-form';

export interface IRepeaterProps extends CssStyleProps {
	id?: string;
	name?: string;
	containerStyles?: BoxProps;
	noRecordsImageProps?: ImageProps & CSSProperties;
	errorMessage?: string;
	isLoading?: boolean;
	rowData?: Row[];
	children?: React.ReactElement;
	layout?: LayoutType;
	displayType?: DisplayTypeTypes;
	selectionMode?: SelectionModeType;
	maxCardsCount?: number;
	cardAlignment?: AlignmentType;
	gap?: string;
	onSelectionChange?: (value: string | { id: string }[]) => void;
	withForm?: boolean;
	onChange?: (event: { target: { value: string | { id: string }[] } }) => void;
	defaultSelected?: any;
	boName?: string;
	isComposition?: boolean;
	onInsert?: UseFieldArrayInsert<FieldValues, string>;
	onDelete?: UseFieldArrayRemove;
	itemName?: string;
	showItemName?: boolean;
	isCreateUpdateData?: boolean;
	reRenderRepeater?: () => void;
	enableSearch?: boolean;
	showDelete?: boolean;
	showAdd?: boolean;
	errors?: any[];
	errorFieldName?: string;
	errorAttributes?: any;
	sourceData?: any;
	onClickAdd?: (index) => void;
	onClickDelete?: (index) => void;
	variant?: VariantType;
	selectionStyle?: SelectionStyleType;
	refreshKey?: string;
	sharedRef?: any;
	headerText?: string;
}

export interface Row {
	[index: string]: string | number | boolean | object;
}

export enum LayoutType {
	Horizontal = 'horizontal',
	Vertical = 'vertical',
}
export enum AlignmentType {
	Left = 'flex-start',
	Right = 'flex-end',
	Center = 'center',
	Space_Between = 'space-between',
	Space_Around = 'space-around',
	Space_Evenly = 'space-evenly',
}

export enum DisplayTypeTypes {
	Card = 'card',
	Carousal = 'carousal',
}

export enum SelectionModeType {
	Single = 'single',
	Multiple = 'multiple',
	None = 'none',
}
export enum SelectionStyleType {
	Radio = 'radio',
	Checkbox = 'checkbox',
}
export enum VariantType {
	Default = 'default',
	'With header' = 'withHeader',
	'With footer' = 'withFooter',
}

export enum AassociationType {
	Aggregation = 'aggregation',
	Composition = 'composition',
}
export const NOT_FOUND_TEXT = 'No data found';

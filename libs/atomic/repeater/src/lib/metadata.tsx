/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import {
	CssProps,
	MetaData,
	PropertyConfiguration,
	SubMetaData,
	SubMetaDataDefinition,
} from '@base/utils-hooks';
import {
	AlignmentType,
	DisplayTypeTypes,
	IRepeaterProps,
	LayoutType,
	SelectionModeType,
	SelectionStyleType,
	VariantType,
} from './types';

export const defaultProps: IRepeaterProps = {
	id: '1',
	cardAlignment: AlignmentType.Left,
	layout: LayoutType.Horizontal,
	displayType: DisplayTypeTypes.Card,
	selectionMode: SelectionModeType.None,
	maxCardsCount: 20,
	itemName: '',
	enableSearch: false,
	showDelete: true,
	showAdd: true,
};

const defaultEditableProps: PropertyConfiguration[] = [
	{
		props: 'rowData',
		propLabel: 'Value',
		help: 'Set data source',
		allowedValues: [
			{
				show: true,
				default: 'Select value',
				dynamic: true,
				type: 'string',
			},
		],
	},
	{
		props: 'errors',
		propLabel: 'Error',
		help: 'Error data source',
		allowedValues: [
			{
				show: true,
				default: 'Select value',
				dynamic: true,
				type: 'string',
			},
		],
	},
	{
		props: 'errorFieldName',
		propLabel: 'Error field name',
		help: 'Set data source',
		allowedValues: [
			{
				show: true,
				default: '',
				dynamic: true,
				type: 'string',
			},
		],
	},

	{
		props: 'enableSearch',
		propLabel: 'Enable Search',
		help: 'Helps to enable search',
		allowedValues: [
			{
				show: true,
				default: false,
				values: [true, false],
				type: 'boolean',
			},
		],
	},
	{
		props: 'isCreateUpdateData',
		propLabel: 'Enable create update',
		help: 'Helps to enable create update',
		allowedValues: [
			{
				show: false,
				default: false,
				values: [true, false],
				type: 'boolean',
			},
		],
	},
	{
		props: 'showDelete',
		propLabel: 'Enable Delete',
		help: 'Helps to enable delete',
		allowedValues: [
			{
				show: false,
				default: defaultProps.showDelete,
				values: [true, false],
				type: 'boolean',
			},
		],
	},
	{
		props: 'showAdd',
		propLabel: 'Enable Add',
		help: 'Helps to enable Add',
		allowedValues: [
			{
				show: false,
				default: defaultProps.showAdd,
				values: [true, false],
				type: 'boolean',
			},
		],
	},
	{
		props: 'minimumRowCount',
		propLabel: 'Minimum row count',
		help: 'Defines the initial number of cards to display on create or update.',
		allowedValues: [
			{
				show: false,
				default: 0,
				type: 'number',
			},
		],
	},
	{
		props: 'itemName',
		propLabel: 'Item name',
		help: 'Set item name',
		allowedValues: [
			{
				show: true,
				default: '',
				type: 'string',
			},
		],
	},
	{
		props: 'showItemName',
		propLabel: 'Show item name',
		help: 'Helps to enable show item name',
		allowedValues: [
			{
				show: true,
				default: false,
				values: [true, false],
				type: 'boolean',
			},
		],
	},
	{
		props: 'headerText',
		propLabel: 'Header text',
		help: 'Set header text',
		allowedValues: [
			{
				show: true,
				default: defaultProps.itemName,
				type: 'string',
			},
		],
	},
	{
		props: 'layout',
		propLabel: 'Layout',
		help: 'Helps to difine layout',
		allowedValues: [
			{
				show: true,
				default: LayoutType.Horizontal,
				values: LayoutType,
				type: typeof LayoutType,
			},
		],
	},
	{
		props: 'displayType',
		propLabel: 'Display type',
		help: 'Helps to define Display type',
		allowedValues: [
			{
				show: true,
				default: DisplayTypeTypes.Card,
				values: DisplayTypeTypes,
				type: typeof DisplayTypeTypes,
			},
		],
	},
	{
		props: 'selectionMode',
		propLabel: 'Selection mode',
		help: 'Helps to define Selection mode',
		allowedValues: [
			{
				show: true,
				default: SelectionModeType.None,
				values: SelectionModeType,
				type: typeof SelectionModeType,
			},
		],
	},
	{
		props: 'selectionStyle',
		propLabel: 'Selection style',
		help: 'Helps to select selection style',
		allowedValues: [
			{
				show: false,
				default: SelectionStyleType.Radio,
				values: SelectionStyleType,
				type: typeof SelectionStyleType,
			},
		],
	},
	{
		props: 'isCarousal',
		propLabel: 'Display as carousal',
		help: 'Helps to enable carousal view',
		allowedValues: [
			{
				show: false,
				default: false,
				values: [true, false],
				type: 'boolean',
			},
		],
	},
	{
		props: 'cardAlignment',
		propLabel: 'Alignment',
		help: 'Helps to difine alignment',
		allowedValues: [
			{
				show: true,
				default: AlignmentType.Left,
				values: AlignmentType,
				type: typeof AlignmentType,
			},
		],
	},
	{
		props: 'gap',
		propLabel: 'Gap',
		help: 'Helps to determine row/column gap',
		allowedValues: [
			{
				show: true,
				default: '20px',
				type: 'dimension',
				contstraint: {
					units: ['px'],
				},
			},
		],
	},
	{
		props: 'maxCardsCount',
		propLabel: 'Number of cards to be displayed',
		help: 'Helps to define number of cards to be displayed',
		allowedValues: [
			{
				show: true,
				default: 20,
				type: 'number',
			},
		],
	},
];

const viewProps = [
	{
		name: 'repeaterDefault',
		label: 'Default',
		componentName: 'Repeater',
		reference: 'atomic-repeater',
		refArgs: {
			variant: VariantType.Default,
		},
		condition: {
			variant: VariantType['With footer'],
		},
	},
	{
		name: 'repeaterWithFooter',
		label: 'With footer',
		componentName: 'Repeater',
		reference: 'atomic-repeater',
		refArgs: {
			variant: VariantType['With footer'],
		},
		condition: {
			showByDefault: 'true',
			variant: VariantType.Default,
		},
	},
	{
		name: 'repeaterWithHeader',
		label: 'With header',
		componentName: 'Repeater',
		reference: 'atomic-repeater',
		refArgs: {
			variant: VariantType['With header'],
		},
		condition: {
			showByDefault: 'true',
			variant: VariantType.Default,
		},
	},
];
const defaultMetadata: MetaData = {
	isPublishable: true,
	toolbarType: 'atomic',
	description: 'This is metadata information for repeater component',
	defaultBehavior: {
		onLoad: 'When repeater is Loaded\nDo something\n',
	},
	configurationProps: {
		containerId: '',
		targetMappingEnabled: true,
	},
	cssProps: [
		{
			key: CssProps.PADDING,
		},
		{
			key: CssProps.BACKGROUND_COLOR,
		},
		{
			key: CssProps.BORDER,
		},
		{
			key: CssProps.BORDER_WIDTH,
		},
		{
			key: CssProps.BORDER_RADIUS,
			value: {
				props: 'borderRadius',
				propLabel: 'Border radius',
				value: '0px 0px 0px 0px',
				type: 'borderRadius',
			},
		},
	],
	viewProps: viewProps,
	editableProps: {
		component: 'Repeater',
		props: defaultEditableProps,
		subComponents: [],
	},
};
export const customEditableProps: Record<string, PropertyConfiguration[]> = {
	['withForm' + true]: [
		{
			props: 'isCreateUpdateData',
			propLabel: 'Enable create update',
			help: 'Helps to enable create update',
			allowedValues: [
				{
					show: true,
					default: false,
					values: [true, false],
					type: 'boolean',
				},
			],
		},
		{
			props: 'showDelete',
			propLabel: 'Enable Delete',
			help: 'Helps to enable delete',
			allowedValues: [
				{
					show: true,
					default: defaultProps.showDelete,
					values: [true, false],
					type: 'boolean',
				},
			],
		},
		{
			props: 'showAdd',
			propLabel: 'Enable Add',
			help: 'Helps to enable Add',
			allowedValues: [
				{
					show: true,
					default: defaultProps.showAdd,
					values: [true, false],
					type: 'boolean',
				},
			],
		},
		{
			props: 'minimumRowCount',
			propLabel: 'Minimum row count',
			help: 'Defines the initial number of cards to display on create or update.',
			allowedValues: [
				{
					show: true,
					default: 0,
					type: 'number',
				},
			],
		},
	],
	['displayType' + DisplayTypeTypes.Carousal]: [
		{
			props: 'enableSearch',
			propLabel: 'Enable Search',
			help: 'Helps to enable search',
			allowedValues: [
				{
					show: false,
					default: false,
					values: [true, false],
					type: 'boolean',
				},
			],
		},
	],
	['selectionMode' + SelectionModeType.Single]: [
		{
			props: 'selectionStyle',
			propLabel: 'Selection style',
			help: 'Helps to select selection style',
			allowedValues: [
				{
					show: true,
					default: SelectionStyleType.Radio,
					values: SelectionStyleType,
					type: typeof SelectionStyleType,
				},
			],
		},
	],
};

const filterProps = (keys: string[], append = true) => {
	const props = append ? [...defaultEditableProps] : [];
	keys.forEach((key) => {
		const customProps = customEditableProps[key];
		customProps.forEach((customProp) => {
			if (append) {
				const index = defaultEditableProps.findIndex(
					(prop) => prop.props == customProp.props
				);
				index >= 0
					? props.splice(index, 1, customProp)
					: props.splice(props.length, 0, customProp);
			} else {
				props.push(customProp);
			}
		});
	});
	return props;
};

const withFormDefinition: SubMetaDataDefinition = {
	type: { withForm: 'true' },
	metaData: {
		...defaultMetadata,
		editableProps: {
			...defaultMetadata.editableProps,
			props: filterProps(['withForm' + true]),
		},
	},
};
const displayTypeDefinition: SubMetaDataDefinition = {
	type: { displayType: DisplayTypeTypes.Carousal },
	metaData: {
		...defaultMetadata,
		editableProps: {
			...defaultMetadata.editableProps,
			props: filterProps(['displayType' + DisplayTypeTypes.Carousal]),
		},
	},
};
const singleSelectionModeDefinition: SubMetaDataDefinition = {
	type: { selectionMode: SelectionModeType.Single },
	metaData: {
		...defaultMetadata,
		editableProps: {
			...defaultMetadata.editableProps,
			props: filterProps(['selectionMode' + SelectionModeType.Single]),
		},
	},
};
const displayTypeMeta: SubMetaData = {
	name: 'displayType',
	definition: displayTypeDefinition,
	priority: 1,
};
const withFormMeta: SubMetaData = {
	name: 'withForm',
	definition: withFormDefinition,
	priority: 1,
};
const singleSelectionModeMeta: SubMetaData = {
	name: 'selectionMode',
	definition: singleSelectionModeDefinition,
	priority: 1,
};

export const metadata: MetaData = {
	...defaultMetadata,
	subMetaData: [withFormMeta, displayTypeMeta, singleSelectionModeMeta],
};

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { ApexTheme, DeviceTypes } from '@base/theme';
import { DisplayTypeTypes, IRepeaterProps } from './types';
import { createUseStyles } from 'react-jss';
import { BoxProps } from '@atomic/box';

export const styles = (theme: ApexTheme, props: IRepeaterProps) => ({
	container: {
		flexDirection: 'column',
		height:
			props?.displayType === DisplayTypeTypes.Carousal ? 'max-content' : '100%',
		width:
			props?.displayType === DisplayTypeTypes.Carousal ? 'max-content' : '100%',
		padding: props?.padding || '0px',
		margin: props?.margin || '0px',
		backgroundColor: props?.backgroundColor || theme.colors.monochrome.white,
		borderWidth: props?.borderWidth || '0px',
		borderColor: props?.borderColor || theme.colors.monochrome.white,
		borderStyle: props?.borderStyle || 'solid',
		borderRadius: props?.borderRadius || '0px',
	},
	cardviewContainer: ({ cardWidth, isVerticalLayout }) => ({
		width: '100%',
		gap: props?.gap || '20px',
		justifyContent: props?.cardAlignment,
		display: 'grid',
		gridTemplateColumns: `repeat(auto-fit, minmax(100px, ${
			isVerticalLayout ? '100%' : cardWidth || '406px'
		}))`,
	}),
	carousalviewContainer: {
		placeContent: props?.cardAlignment,
		width: '100%',
		height: 'max-content',
		flexWrap: 'wrap',
	},
	carouselNavigationContainer: {
		position: 'absolute',
		zIndex: 1,
		cursor: 'pointer',
	},
	addButton: ({ isEmpty }) => ({
		cursor: 'pointer',
		color: theme.colors.primary.default,
		gap: '12px',
		padding: isEmpty ? '16px 8px 8px ' : '8px',
	}),
	header: {
		width: '100%',
		height: '40px',
		marginBottom: '16px',
		justifyContent: 'flex-end',
	} as BoxProps,
	compositionHeader: {
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'space-between',
		width: '100%',
		padding: '12px 16px',
		marginBottom: '16px',
		backgroundColor: theme.colors.monochrome.bg,
		borderRadius: '8px',
	},
	compositionHeaderText: {
		fontSize: theme.fontSizes.desktop.bMedium,
		fontWeight: theme.fontWeights.bold,
		lineHeight: theme.lineHeights.desktop.bMedium,
		letterSpacing: theme.letterSpacings.desktop.bSmall,
		color: theme.colors.monochrome.body,
	},
	compositionHeaderAddButton: {
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'center',
		height: '22px',
		padding: '0px',
		gap: '12px',
		minHeight: '22px',
	},
});

export const textStyles = (theme: ApexTheme, isErrorMessage = false) => {
	return {
		fontWeight: theme.fontWeights.regular,
		fontSize: theme.fontSizes[DeviceTypes.Desktop].bSmall,
		fontFamily: theme.fontFamily,
		color: isErrorMessage
			? theme.colors.danger.default
			: theme.colors.monochrome.body,
		display: 'block',
	};
};
export const useEmptyRowRendererStyles = createUseStyles(() => ({
	'@keyframes spinAnimation': {
		'0%': {
			transform: 'rotate(0deg)',
		},
		'100%': {
			transform: 'rotate(360deg)',
		},
	},
	loaderStyles: () => ({
		margin: '11px',
		animation: '$spinAnimation 1.5s linear infinite',
	}),
}));

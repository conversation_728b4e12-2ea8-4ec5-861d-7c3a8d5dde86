/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the Repeater component
import React, { FC, memo, useCallback, useMemo } from 'react';
import { useTheme } from 'react-jss';
import { ApexTheme } from '@base/theme';
import { IRepeaterProps, VariantType } from './types';
import { styles } from './style';
import Box, { BoxProps } from '@atomic/box';
import { useScrollbarStyles } from '@atomic/scrollbar';
import Button, { ButtonSizes, ButtonTypes, IconPosition } from '@atomic/button';
import { isArray } from 'lodash';
import classNames from 'classnames';
import { EmptyRowsRenderer } from './EmptyRowRenderer';
import Text from '@atomic/text';
import _cloneDeep from 'lodash/cloneDeep';
import _merge from 'lodash/merge';
import { setMetaData } from '@base/utils-hooks';
import { metadata } from './metadata';
import Icon, { IconCodes } from '@atomic/icon';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface RepeaterProps extends IRepeaterProps {}

const CompositionRepeater: FC<RepeaterProps> = (props: RepeaterProps) => {
	// Framework generated code, below line registers the component
	// NOT TO BE DELETED
	setMetaData('Repeater', metadata);
	const scrollbarClasses = useScrollbarStyles();
	const theme: ApexTheme = useTheme();
	const componentStyles = styles(theme, props);
	const {
		id,
		name,
		containerStyles,
		rowData = [],
		children,
		showAdd,
		onClickAdd,
		itemName,
		noRecordsImageProps,
		showDelete,
		showItemName,
		onClickDelete,
		onChange,
		reRenderRepeater,
		sourceData,
		refreshKey,
		sharedRef,
		headerText,
		variant,
	} = props;

	const addMore = useCallback(() => {
		sharedRef.current = false;
		onClickAdd?.(rowData.length);
		props?.onInsert?.(rowData.length, {});
	}, [rowData.length]);

	const RenderAddButton = useMemo(() => {
		return (
			<Box
				{...componentStyles.addButton({ isEmpty: !rowData.length })}
				onClick={addMore}
			>
				<Icon
					icon={IconCodes.icon_Tb_plus}
					color={theme.colors.primary.default}
				/>
				<Text
					text={'Add ' + itemName || ''}
					color={theme.colors.primary.default}
				/>
			</Box>
		);
	}, [rowData.length]);

	const getFieldErrors = useCallback(
		(index: number) => {
			if (index === 0 && rowData.length === 1) {
				return (
					props?.errors?.filter?.((error) =>
						error?.field?.startsWith?.(props?.errorFieldName)
					) || []
				);
			}
			const field = `${props?.errorFieldName}.${index}.`;
			const fieldMatchErrors =
				props?.errors?.filter?.((error) => error?.field?.startsWith?.(field)) ||
				[];
			return fieldMatchErrors;
		},
		[props?.errorFieldName, props?.errors, rowData.length]
	);
	const RenderList = useMemo(() => {
		const childElement = isArray(children) ? children[0] : children;
		const { width: cardWidth } = childElement?.props || {};
		const data = rowData;
		return (
			<Box
				{...componentStyles.cardviewContainer({
					cardWidth,
					isVerticalLayout: true,
				})}
				className={classNames(scrollbarClasses.scrollbar)}
			>
				{children &&
					data?.map((item, index) => {
						return (
							<React.Fragment key={`${item.id}`}>
								{React.cloneElement(childElement, {
									index,
									microEditable: index === 0,
									rowData: item,
									parentName: name,
									width: '100%',
									showDelete,
									showItemName,
									itemName,
									isComposition: true,
									fieldErrors: getFieldErrors(index),
									refreshKey,
									onClickDelete: () => {
										sharedRef.current = false;
										reRenderRepeater?.();
										//rowData is what the useFieldArray returns
										const clonedRowData = _cloneDeep(rowData);
										//sourceData is the value taken directly from formstate
										const clonedSource = _cloneDeep(
											sourceData ? sourceData : []
										);

										//Removing id generated by useFieldArray
										const updatedRowData = clonedRowData.map(
											({ id, ...rest }) => rest
										);
										const slicedSourceData = clonedSource
											?.slice(0, index)
											.concat(clonedSource?.slice(index + 1));

										const slicedRowData = updatedRowData
											?.slice(0, index)
											.concat(updatedRowData?.slice(index + 1));

										//This is a hack to merge data from both sources rowData and sourceData for each index
										const newSource = slicedSourceData.map((item, index) =>
											_merge({}, item, slicedRowData[index])
										);

										onClickDelete?.(index);
										props?.onDelete?.(index);
										onChange?.({
											target: {
												value: newSource as any,
											},
										});
									},
								})}
							</React.Fragment>
						);
					})}
				{showAdd && variant !== VariantType['With header'] && RenderAddButton}
			</Box>
		);
	}, [JSON.stringify(rowData), refreshKey]);
	return (
		<Box
			id={id}
			data-component="atomic/compositionRepeater"
			{...(componentStyles.container as BoxProps)}
			{...(containerStyles as BoxProps)}
		>
			{variant === VariantType['With header'] && (
				<Box {...componentStyles.compositionHeader}>
					<Text
						text={headerText || itemName}
						{...componentStyles.compositionHeaderText}
					/>
					{showAdd && (
						<Button
							title="Add More"
							buttonType={ButtonTypes.Text}
							onClick={addMore}
							size={ButtonSizes.Small}
							buttonTextStyles={{
								fontSize: theme.fontSizes.desktop.bSmall,
								letterSpacing: theme.letterSpacings.desktop.bSmall,
								lineHeight: theme.lineHeights.desktop.bSmall,
								fontWeight: 'bold',
								display: 'flex',
								alignItems: 'center',
								justifyContent: 'center',
							}}
							iconProps={{
								icon: IconCodes.icon_Tb_plus,
								color: theme.colors.primary.default,
								iconStyle: {
									display: 'flex',
									alignItems: 'center',
									justifyContent: 'center',
									scale: 1.2,
								},
							}}
							iconPosition={IconPosition.Left}
							buttonStyles={{
								alignItems: 'center',
								justifyContent: 'center',
								columnGap: '12px',
							}}
							{...componentStyles.compositionHeaderAddButton}
						/>
					)}
				</Box>
			)}
			{rowData.length ? (
				RenderList
			) : showAdd && variant !== VariantType['With header'] ? (
				RenderAddButton
			) : (
				<Box margin="auto">
					<EmptyRowsRenderer
						theme={theme}
						imageProps={noRecordsImageProps}
						errorMessage={props?.errorMessage}
						isLoading={
							props?.isLoading && variant !== VariantType['With header']
						}
					/>
				</Box>
			)}
		</Box>
	);
};

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
export default memo(CompositionRepeater);

import { Story, Meta } from '@storybook/react';
import Docpdfviewer, { DocpdfviewerProps } from './docpdfviewer';
import { defaultProps } from './metadata';
import { IDocpdfviewerProps } from './types';
import { useState } from 'react';
import { Viewer } from '@helloviu/viewer';

const SampleFileDetails = {
	filePath: 'private/generated.pdf',
	baseUrl:
		'https://processor.platform-app-integ-test.cluster.jiffy.ai/platform',
	tenantId: '76851fa5-219c-4c7f-b39a-2a078b71b688',
	token:
		'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJzbzhTZ3owWi0xeE9rVzZadVZIZWZmZWdwaXJwN2cwcURLNmVrLWNpd3ZjIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fHx6mK6SVxbQNoMz3_cWT_F_fTNbxneYb5CzZTuDP7uXscUkz9WQpd0VCC360q-xJEfPtCHd4VSfEfiRCGuazQxjKWrDZi4qD2u5CNyicK-gv4MauYVQ1g3cZmCdizsEfwe9KpTswsIlpD8QM8ENzx119RDsT1MD4wysacR8Ux5smXvdU4xZtcvWQ0IsVtrqyrvV4q9v42PMLU6Xa6oLxDqOp-J_oO-A4odPSkjdXO0_NprMT3RxMr6Oqdkonffru6N5ma45-EhdGy9oWqNXUhnBPQEf0KGLFNTnOFNtdPyYImwELXzB3qbKXgMYT5ttjARzode6T9j25m6xfz2awg',
};
export default {
	component: Docpdfviewer,
	title: 'Atomic/Docpdfviewer',
	argTypes: {
		id: { type: 'number' },
	},
} as Meta;

const Template: Story<DocpdfviewerProps & HTMLButtonElement> = (args) => (
	<Docpdfviewer {...args}></Docpdfviewer>
);

export const DocpdfviewerStory = Template.bind({});
DocpdfviewerStory.args = {
	...defaultProps,
};

export const PdfViewer = () => {
	const containerStyles = {
		width: '100%',
		height: '600px',
	};
	const props: IDocpdfviewerProps = {
		filePath: SampleFileDetails.filePath,
		baseUrl: SampleFileDetails.baseUrl,
		tenantId: SampleFileDetails.tenantId,
	};
	return (
		<Docpdfviewer
			file={''}
			containerStyles={containerStyles}
			{...props}
		></Docpdfviewer>
	);
};

export const HelloViewerPreview = () => {
	const [file, setFile] = useState<File | null>(null);

	return (
		<div
			style={{
				display: 'flex',
				flexDirection: 'column',
				gap: '10px',
				alignItems: 'center',
				justifyContent: 'center',
			}}
		>
			<button
				style={{
					padding: '8px 16px',
					backgroundColor: '#007bff',
					color: 'white',
					border: 'none',
					borderRadius: '4px',
					cursor: 'pointer',
				}}
				onClick={() => {
					const input = document.createElement('input');
					input.type = 'file';
					input.onchange = (e) => {
						const target = e.target as HTMLInputElement;
						setFile(target.files?.[0] || null);
					};
					input.click();
				}}
			>
				Upload your file
			</button>
			<div>
				{file && (
					<>
						<h4>File information</h4>
						<p>File name: {file.name}</p>
						<p>File size: {(file.size / 1024).toFixed(2)} KB</p>
						<p>File type: {file.type}</p>
						<p>Last modified: {new Date(file.lastModified).toLocaleString()}</p>
					</>
				)}
			</div>
			<div style={{ width: '100%', height: '600px' }}>
				{<Viewer key={`file-previewer`} file={file} />}
			</div>
		</div>
	);
};

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { BoxProps } from '@atomic/box';
import { CssStyleProps } from '@base/global-events';
import { Config } from '@helloviu/viewer/types';
export interface IDocpdfviewerProps extends Config, CssStyleProps {
	id?: string;
	containerStyles?: BoxProps;
	filePath?: string;
	baseUrl?: string;
	tenantId?: string;
	hideThumbnailOnload?: boolean;
	toolbarPosition?: ToolbarPositions;
}

export enum ToolbarPositions {
	Floating = 'floating',
	Docked = 'docked',
	Hidden = 'hidden',
}

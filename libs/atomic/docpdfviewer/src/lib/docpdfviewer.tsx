/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the Docpdfviewer component
import { FC, memo, useEffect, useState } from 'react';
import { useTheme } from 'react-jss';
import { configurable } from '@base/config-provider';
import { setMetaData } from '@base/utils-hooks';
import { ApexTheme } from '@base/theme';
import { defaultProps, metadata } from './metadata';
import { IDocpdfviewerProps, ToolbarPositions } from './types';
import { styles, useEmptyRowRendererStyles } from './style';
import { Viewer } from '@helloviu/viewer';
import Box from '@atomic/box';
import { getFileFromDrive, toolbarStyle } from './helper';
import { MIME_Types } from './constants';
import './helloviewStyle.css';
import CustomLoader from '@atomic/custom-loader';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface DocpdfviewerProps extends IDocpdfviewerProps {}

const Docpdfviewer: FC<DocpdfviewerProps> = (props: DocpdfviewerProps) => {
	// Framework generated code, below line registers the component
	// NOT TO BE DELETED
	setMetaData('Docpdfviewer', metadata);

	// Framework generated code, below line provides the contextProvider instance

	const theme: ApexTheme = useTheme();
	const componentStyles = styles(theme, props);
	const classes = useEmptyRowRendererStyles();

	const {
		id,
		containerStyles,
		file,
		filePath,
		baseUrl,
		tenantId,
		hideThumbnailOnload = false,
		toolbarPosition,
		hideToolBar,
		...restProps
	} = props;
	const [fileSrc, setFileSrc] = useState<string | File>('');
	const [fileName, setFileName] = useState<string>(null);
	const [isLoading, setLoading] = useState(false);
	const fetchFile = async () => {
		setLoading(true);
		// Fetch file content from jiffy drive)
		const parts = filePath?.split('.');
		const extension = parts.pop();
		const name = filePath?.slice(
			filePath?.lastIndexOf('/') + 1,
			filePath?.lastIndexOf('.')
		);
		setFileName(decodeURI(name));
		if (Object.keys(MIME_Types)?.includes(extension?.toLowerCase())) {
			const encodedFilePath = filePath.replace(/([^/]+)$/, (name) =>
				encodeURIComponent(name)
			); // safely encodes the file name
			const fileBlob = await getFileFromDrive(
				encodedFilePath,
				tenantId,
				baseUrl,
				extension?.toLowerCase()
			);
			setFileSrc(fileBlob as File);
		}
		setLoading(false);
	};
	useEffect(() => {
		if (file) {
			setFileSrc(file);
		} else if (filePath && tenantId && baseUrl) {
			fetchFile();
		} else {
			setFileSrc('');
			setFileName('');
			setLoading(false);
		}
	}, [file, filePath, tenantId, baseUrl]);

	const LoaderRender = (
		<Box className={classes.loaderContainer}>
			<CustomLoader />
		</Box>
	);
	const isHideToolbar = toolbarPosition
		? toolbarPosition === ToolbarPositions.Hidden
		: hideToolBar; // fallback to hideToolBar prop for backward compatibility
	const isDocked = toolbarPosition === ToolbarPositions.Docked;
	return (
		<Box
			id={id}
			data-component="atomic/docpdfviewer"
			{...containerStyles}
			{...componentStyles}
		>
			{isLoading ? (
				LoaderRender
			) : (
				<Viewer
					key={`${id}-${fileName}`}
					file={fileSrc}
					showUploadButton={false}
					defaultDownloadFileName={fileName}
					showPopupDuringDownload={false}
					toolbarStyle={toolbarStyle(theme)}
					showThumbnailOnload={!hideThumbnailOnload}
					hideToolBar={isHideToolbar}
					docked={isDocked}
					{...restProps}
				/>
			)}
		</Box>
	);
};

Docpdfviewer.defaultProps = defaultProps;
Docpdfviewer.displayName = 'Docpdfviewer';

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
export default configurable(memo(Docpdfviewer), 'Docpdfviewer');

/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

import { INPUT_CSS_PROPS } from '@atomic/input';
import { IAutocompleteProps, DateFormat, dataTypeValues } from './types';

export const defaultProps: IAutocompleteProps = {
	id: 1,
	enableSearchIcon: true,
	enableSelectAll: false,
	dateFormatKey: DateFormat['MM/DD/YYYY'],
	currency: 'USD',
	enableRemoteSearch: true,
	dataType: dataTypeValues.Singlelinetext,
	primaryLabelWidth: '50%',
};

export const metadata = {
	description: 'This is a help text',
	configurationProps: {
		containerId: '',
		targetMappingEnabled: true,
	},
	cssProps: [...INPUT_CSS_PROPS],
	defaultBehavior: {
		onLoad: 'When text is Loaded\nDo something\n',
		onChange: 'When Autocomplete is Changed\nDo something\n',
	},
	editableProps: {
		component: 'Autocomplete',
		allowedChildrenComponents: ['none'],
		props: [
			{
				props: 'placeholderValue',
				propLabel: 'Placeholder',
				help: 'Used to set a placeholder text',
				allowedValues: [
					{
						show: true,
						type: 'string',
						default: '',
					},
				],
			},
			{
				props: 'defaultSelected',
				propLabel: 'Default Selected',
				help: 'Used to set a default selected value',
				allowedValues: [
					{
						show: true,
						type: 'string',
						dynamic: true,
						default: '',
					},
				],
			},
			{
				props: 'data',
				propLabel: 'Data',
				help: 'Used to set a data',
				allowedValues: [
					{
						show: true,
						type: 'string',
						dynamic: true,
						default: '',
					},
				],
			},
			{
				props: 'showLabel',
				propLabel: 'Show Label',
				help: 'Show or hide Label',
				allowedValues: [
					{
						show: true,
						default: true,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'disabled',
				propLabel: 'Disable',
				help: 'Disable the field on selection',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'width',
				propLabel: 'Width',
				help: 'Choose the width of the autocomplete',
				allowedValues: [
					{
						show: true,
						default: '334px',
						type: 'string',
					},
				],
			},
			{
				props: 'dataType',
				propLabel: 'Data Type',
				help: 'Choose Data Type',
				allowedValues: [
					{
						show: true,
						default: dataTypeValues.Singlelinetext,
						values: dataTypeValues,
						type: typeof dataTypeValues,
					},
				],
			},
			{
				props: 'enableRemoteSearch',
				propLabel: 'Enable Remote Search',
				help: 'Enable Remote Seacrh',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'acceptCustomValue',
				propLabel: 'Accept Custom Value',
				help: 'Accept Custom Value',
				allowedValues: [
					{
						show: true,
						default: false,
						values: [true, false],
						type: 'boolean',
					},
				],
			},
			{
				props: 'dateFormatKey',
				propLabel: 'Date Format',
				help: 'Choose the date format',
				allowedValues: [
					{
						show: true,
						default: DateFormat['MM/DD/YYYY'],
						values: DateFormat,
						type: typeof DateFormat,
					},
				],
			},
			{
				props: 'primaryLabelWidth',
				propLabel: 'Primary Label Width',
				help: 'Helps to set the primary lable width',
				allowedValues: [
					{
						show: true,
						default: '50%',
						type: 'dimension',
						contstraint: {
							units: ['%'],
						},
					},
				],
			},
		],
		subComponents: [],
	},
	defaultProps,
	viewProps: [
		{
			name: 'dropdown',
			label: 'Dropdown',
			componentName: 'Dropdown',
			reference: 'atomic-dropdown',
			fieldBindingKeyMap: {
				lookupValue: 'lookupValue',
				lookupLabel: 'lookupLabel',
				displayAttributes: 'displayAttributes',
				searchAttributes: 'searchAttributes',
			},
			refArgs: {},
			condition: {
				multiSelect: 'false',
			},
		},
		{
			name: 'radio',
			label: 'Radio',
			componentName: 'Radiogroup',
			reference: 'atomic-radiogroup',
			refArgs: {
				multiSelect: false,
			},
			fieldBindingKeyMap: {
				data: 'options',
			},
			condition: {
				multiSelect: 'false',
			},
		},
		{
			name: 'tabSwitcher',
			label: 'Tab switcher',
			componentName: 'Radiogroup',
			reference: 'atomic-radiogroup',
			refArgs: {
				multiSelect: false,
				isSegmentedTabs: true,
			},
			fieldBindingKeyMap: {
				data: 'options',
			},
			condition: {
				multiSelect: 'false',
			},
		},
		{
			name: 'choice-card',
			label: 'Choice cards',
			componentName: 'ChoiceCards',
			reference: 'composite-choice-cards',
			refArgs: {
				multiSelect: false,
			},
			fieldBindingKeyMap: {},
			condition: {},
		},
	],
};

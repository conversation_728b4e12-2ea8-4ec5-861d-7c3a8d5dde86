/*
 * Copyright (c) 2021.  jiffy.ai. All rights reserved.
 * This file contains confidential and proprietary intellectual property of jiffy.ai.
 * No part of this file may be copied by any means without the express written permission of jiffy.ai
 */

// Framework generated code, below lines import required components for the Autocomplete component
import React, {
	FC,
	useEffect,
	useState,
	useRef,
	useMemo,
	useLayoutEffect,
	useCallback,
} from 'react';
import { useTheme } from 'react-jss';
import { configurable } from '@base/config-provider';
import { setMetaData } from '@base/utils-hooks';
import { ApexTheme, DeviceTypes } from '@base/theme';
import Box from '@atomic/box';
import _isEmpty from 'lodash/isEmpty';
import Text, { TextSizes, TextTypes, formatter } from '@atomic/text';
import { get, isEmpty, isNil } from 'lodash';
import Input, { InputSize } from '@atomic/input';
import Icon, { IconCodes } from '@atomic/icon';
import { defaultProps, metadata } from './metadata';
import { IAutocompleteProps, dataTypeValues, DateFormat } from './types';
import classNames from 'classnames';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import { useScrollbarStyles } from '@atomic/scrollbar';
import { CaptionMessage, FormControl } from '@composite/form';
import { Controller, useForm } from 'react-hook-form';
import { styles } from './style';
import { useLayer } from 'react-laag';
import Tooltip from '@atomic/tooltip';
import { isArray } from 'lodash';
import {
	currencyFormatter,
	formatDateInput,
	getAutoCompleteInputFieldStyle,
	useDebouncedCallback,
} from './helper';
import {
	DATATYPES,
	mappingBoxId,
	PROPERTY_NO,
	QUERY_SIZE_EXCEEDED_MESSAGE,
	QUERY_SIZE,
} from './constants';
import './loaderStyle.css';
import { TooltipPosition, TooltipType } from '@atomic/tooltip';
import CustomLoader from '@atomic/custom-loader';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface AutocompleteProps extends IAutocompleteProps {}

const AutocompleteWrapper: FC<AutocompleteProps> = (
	props: AutocompleteProps
) => {
	// Framework generated code, below line registers the component
	// NOT TO BE DELETED
	setMetaData('Autocomplete', metadata as any);
	const scrollBarStyle = useScrollbarStyles();
	const wrapperRef = useRef(null);

	const {
		id,
		data,
		withForm,
		onChange,
		onChangeTrigger,
		onInputChange,
		placeholderValue,
		lookupLabel,
		lookupValue,
		displayAttributes = '',
		onSearchKeyChange,
		searchAttributes = '',
		searchAttributeLabels,
		showLabel,
		label,
		disabled,
		readonly,
		showAsterisk,
		showAsMandatory,
		defaultValue,
		resetValue,
		inpColor,
		inpFontFamily,
		enableSearchIcon,
		inputOverrideProps,
		hideSuggetionItemBorderBottom,
		hoverItemBackgroundColor,
		defaultPriorityValue,
		infiniteLoadingEnabled,
		getNextData,
		loadingNextData,
		hoverItemColor,
		enableSelectAll,
		onSelectAll,
		triggerHideSuggestion,
		acceptCustomValue,
		dataType,
		dateFormatKey,
		currency,
		defaultSelected,
		enableOnChange,
		enableRemoteSearch,
		primaryLabelWidth,
	} = props;
	const theme: ApexTheme = useTheme();
	const autoCompleteStyles = styles(props);
	const displayAttributesString = `{${displayAttributes}}`;
	const displayAttributeTypes = JSON.parse(displayAttributesString);

	const [inputValue, setInputValue] = useState<string>(defaultValue || null);
	const [defaultSetted, setDefaultSetted] = useState(false);
	const [internalData, setInternalData] = useState<string>(data || null);
	const [source, SetSource] = useState<any>(data || null);
	const [lastSelection, setLastSelection] = useState<string>(null);
	const [searchKey, setSearchKey] = useState<string>(null);
	const [showSuggestions, setShowSuggestions] = useState(false);
	const [inputBoxWidth, setInputBoxWidth] = useState('334px');
	const [selectAll, setSelctAll] = useState(false);

	useEffect(() => {
		SetSource(data);
		if (Array.isArray(data) && data.length > 0 && !defaultSetted) {
			setDefaultSetted(true);
			if (isEmpty(inputValue)) {
				const selectedItem = isArray(data)
					? data?.find((item) => get(item, lookupValue) === defaultSelected)
					: null;
				if (selectedItem) {
					setInputValue(selectedItem[lookupLabel] || '');
				} else if (acceptCustomValue) {
					setInputValue(defaultSelected || '');
					if (isNil(defaultSelected) || defaultSelected === '') {
						setLastSelection('');
						setSearchKey('');
					}
				} else if (isNil(defaultSelected) || defaultSelected === '') {
					setInputValue('');
					setLastSelection('');
					setSearchKey('');
				}
			}
		}
	}, [data]);

	useEffect(() => {
		if (withForm) {
			onChangeTrigger?.(defaultSelected);
		}
		enableOnChange && onChange?.(defaultSelected);
		const selectedItem = isArray(data)
			? data?.find((item) => get(item, lookupValue) === defaultSelected)
			: null;
		if (selectedItem) {
			setInputValue(selectedItem[lookupLabel] || '');
		} else if (acceptCustomValue) {
			setInputValue(defaultSelected || '');
			if (isNil(defaultSelected) || defaultSelected === '') {
				setLastSelection('');
				setSearchKey('');
			}
		} else if (isNil(defaultSelected) || defaultSelected === '') {
			setInputValue('');
			setLastSelection('');
			setSearchKey('');
		}
	}, [withForm, defaultSelected, enableOnChange, lookupValue]);
	useEffect(() => {
		if (resetValue) {
			setInputValue(defaultValue || '');
			setLastSelection(null);
			setSearchKey(null);
		}
	}, [resetValue]);
	useEffect(() => {
		if (triggerHideSuggestion) {
			setShowSuggestions(false);
		}
	}, [triggerHideSuggestion]);
	useEffect(() => {
		if (acceptCustomValue && enableRemoteSearch) {
			setInternalData(data);
		}
	}, [data, acceptCustomValue, enableRemoteSearch]);
	const formattedInputValue = useCallback((input) => {
		let formattedInput = input;
		if (dataType && dataType !== dataTypeValues.Singlelinetext) {
			if (dataType === dataTypeValues.Date) {
				formattedInput = formatDateInput(formattedInput, dateFormatKey);

				if (formattedInput !== null && formattedInput.length <= 10) {
					return formattedInput;
				}
			} else if (dataType === dataTypeValues.Currency) {
				if (
					formattedInput &&
					!isNaN((formattedInput + '').replace(/,|\$/g, '') as any) &&
					(formattedInput + '').replace(/,|\$/g, '').length > 0
				) {
					formattedInput = currencyFormatter(
						parseInt((formattedInput + '').replace(/,|\$/g, ''))
					);
					return formattedInput;
				}
			} else {
				formattedInput = formatter(
					formattedInput || '',
					dataType,
					dateFormatKey || DateFormat['MM/DD/YYYY'],
					'',
					currency
				);
				return formattedInput;
			}
		}
		return formattedInput;
	}, []);
	const handleCheckboxSelection = (checked) => {
		if (enableSelectAll && onSelectAll) {
			if (checked) {
				setShowSuggestions(false);
				setInputValue('All');
				onSelectAll?.(checked);
			}

			setSelctAll(checked);
		}
	};

	const [sentryRef, { rootRef }] = useInfiniteScroll({
		loading: loadingNextData,
		hasNextPage: infiniteLoadingEnabled,
		onLoadMore: () => {
			getNextData?.();
		},
		disabled: !infiniteLoadingEnabled,
		delayInMs: 400,
	});
	useEffect(() => {
		if (data?.length && infiniteLoadingEnabled) {
			const mapBoxDiv = document.getElementById(mappingBoxId);
			if (mapBoxDiv) {
				const currentScroll = mapBoxDiv.scrollTop;
				mapBoxDiv.scrollTo({
					top: Math.max(0, currentScroll - 60),
					behavior: 'smooth',
				});
			}
		}
	}, [data?.length, infiniteLoadingEnabled]);
	const handleClickOutside = () => {
		setShowSuggestions(false);
		if (searchKey?.length) {
			const lastValidValue = lastSelection;
			setInputValue(lastValidValue);
			onInputChange?.(lastValidValue);
			onSearchKeyChange(lastValidValue);
			if (lastValidValue === '' || lastValidValue === null) {
				setInternalData(data || null);
			}
		} else {
			handleClear();
		}
	};

	const DefaultPlaceHolder = useMemo(() => {
		let searchAttributesArray: any;
		if (searchAttributeLabels?.length > 0) {
			searchAttributesArray = searchAttributeLabels.split(',');
		} else {
			searchAttributesArray = searchAttributes.split(',');
		}

		const placeHolderText = searchAttributesArray.reduce(
			(accumulatorStr, currentValue, index, self) => {
				if (index > 0 && index == self.length - 1) {
					return `${accumulatorStr}or ${currentValue} `;
				} else if (index > 0 && index < self.length - 1) {
					return `${accumulatorStr},${currentValue} `;
				} else {
					return `${accumulatorStr} ${currentValue} `;
				}
			},
			acceptCustomValue ? 'Enter' : 'Search by'
		);
		return placeHolderText;
	}, [acceptCustomValue, searchAttributes, searchAttributeLabels]);

	const handleInternaleSearch = useCallback(() => {
		const internalData = inputValue
			? (data || []).filter((dataItem) => {
					const plainData =
						dataType !== dataTypeValues.Date
							? (get(dataItem, lookupLabel, '') + '')?.replace(/,|\$/g, '')
							: get(dataItem, lookupLabel, '');
					const plainInputValue =
						dataType !== dataTypeValues.Date
							? inputValue?.replace(/,|\$/g, '')
							: inputValue;
					return (
						plainData?.toLowerCase().indexOf(plainInputValue?.toLowerCase()) !==
						-1
					);
			  })
			: data;
		if (withForm) {
			onChangeTrigger(inputValue);
		}
		if (internalData.length === 0) {
			setShowSuggestions(false);
		}
		setInternalData(internalData);
	}, [
		withForm,
		data,
		lookupValue,
		lookupLabel,
		onChangeTrigger,
		inputValue,
		dataType,
	]);
	const debouncedOnChange = useDebouncedCallback(onSearchKeyChange, 400);
	const debouncedInternalSearchChange = useDebouncedCallback(
		handleInternaleSearch,
		400
	);
	const isTextTruncated = useCallback((elementId: string) => {
		const element = document.getElementById(elementId);

		if (!element) {
			return false;
		}
		const targetElement = element.querySelector('[class^="textLabel"]');
		if (!targetElement) {
			return false;
		}

		return targetElement.scrollWidth > targetElement.clientWidth;
	}, []);

	useEffect(() => {
		// Cleanup function to cancel any pending debounce call if the component unmounts
		return () => {
			debouncedInternalSearchChange.cancel();
			debouncedOnChange.cancel();
		};
	}, [debouncedOnChange, debouncedInternalSearchChange]);

	useEffect(() => {
		if (enableRemoteSearch) {
			debouncedOnChange(searchKey);
		} else {
			debouncedInternalSearchChange(searchKey);
		}
	}, [
		enableRemoteSearch,
		searchKey,
		debouncedOnChange,
		debouncedInternalSearchChange,
	]);

	const onSuggestionClicked = (selectedItem) => {
		if (selectedItem) {
			if (lookupValue) {
				const selectedAttribute = get(selectedItem, lookupValue);
				if (withForm) {
					onChangeTrigger?.(selectedAttribute);
				}
				onChange?.(selectedAttribute);
				setSearchKey('');
			}

			const displayPropertyValue = get(selectedItem, lookupLabel);
			if (displayPropertyValue) {
				let key = displayPropertyValue;
				if (dataType !== dataTypeValues.Singlelinetext) {
					key = formattedInputValue(displayPropertyValue);
				}
				setInputValue(key);
				setLastSelection(key);
				onInputChange?.(key);
			}
			setShowSuggestions(false);
		}
	};
	const handleOnchange = (event) => {
		let key = event?.target.value;
		if (dataType !== dataTypeValues.Singlelinetext) {
			key = formattedInputValue(key);
		}
		if (acceptCustomValue) {
			setLastSelection(key);
			onChange?.(key);
		}

		setInputValue(key);
		setSearchKey(key);
		if (enableRemoteSearch) {
			SetSource(null);
		}
		setShowSuggestions(true);
		onInputChange?.(key);
	};
	useLayoutEffect(() => {
		if (wrapperRef.current) {
			const width = wrapperRef.current.clientWidth;

			if (width + 'px' !== inputBoxWidth) {
				setInputBoxWidth(width + 'px');
			}
		}
	}, [window.innerWidth, props.width, wrapperRef.current]);
	const getColorHighlightedText = (
		beforeMatch: string,
		match: string,
		afterMatch: string,
		textProps
	) => {
		const { dataType } = textProps;
		if (dataType === DATATYPES.EMAIL) {
			textProps = {
				...textProps,
				color: '#0000EE', // not available in beacon
				textDecoration: 'underline',
				dataType: '',
			};
		}

		return (
			<>
				<Text {...textProps}>{beforeMatch}</Text>
				<Text
					fontWeight={theme.fontWeights.bold}
					color={theme.colors.monochrome.offBlack}
				>
					{match}
				</Text>
				<Text {...textProps}>{afterMatch}</Text>
			</>
		);
	};
	const highlightMatch = (
		dataPriority,
		suggestion,
		dataTypeKey,
		query,
		isTooltip
	) => {
		let index = -1;
		if (query) {
			const queryString = query.toString();
			index = suggestion?.toLowerCase().indexOf(queryString?.toLowerCase());
		}

		if (index === -1) {
			switch (dataPriority) {
				case 1:
					return (
						<Text
							color={theme.colors.primary.default}
							fontWeight={theme.fontWeights.bold}
							fontSize={theme.fontSizes.desktop.bSmall}
							hoverProps={{ color: hoverItemColor }}
							width="100%"
							className={
								isTooltip
									? classNames(autoCompleteStyles.tooltipText)
									: classNames(
											autoCompleteStyles.textNoWrapElipsis,
											autoCompleteStyles.textLineProperties
									  )
							}
							dataType={dataTypeKey}
							dateFormat={dateFormatKey ? dateFormatKey : undefined}
						>
							{suggestion}
						</Text>
					);
				case 2:
					return (
						<Text
							className={classNames(
								isTooltip
									? classNames(autoCompleteStyles.tooltipText)
									: classNames(
											autoCompleteStyles.textNoWrapElipsis,
											autoCompleteStyles.textLineProperties
									  )
							)}
							dataType={dataTypeKey}
							hoverProps={{ color: hoverItemColor }}
							fontWeight={theme.fontWeights.semiBold}
							fontSize={theme.fontSizes.desktop.bSmall}
							dateFormat={dateFormatKey ? dateFormatKey : undefined}
							color={'#14142B'} //color not available in beacon styles
						>
							{suggestion}
						</Text>
					);
				case 3:
					return (
						<Text
							className={
								isTooltip
									? classNames(autoCompleteStyles.tooltipText)
									: classNames(
											autoCompleteStyles.textNoWrapElipsis,
											autoCompleteStyles.textLineProperties
									  )
							}
							fontWeight={theme.fontWeights.regular}
							hoverProps={{ color: hoverItemColor }}
							fontSize={theme.fontSizes.desktop.bSmall}
							color={theme.colors.monochrome.ash}
							dateFormat={dateFormatKey ? dateFormatKey : undefined}
							dataType={dataTypeKey}
						>
							{suggestion}
						</Text>
					);
				case 4:
					return (
						<Text
							className={
								isTooltip
									? classNames(autoCompleteStyles.tooltipText)
									: classNames(
											autoCompleteStyles.textNoWrapElipsis,
											autoCompleteStyles.textLineProperties
									  )
							}
							fontWeight={theme.fontWeights.regular}
							hoverProps={{ color: hoverItemColor }}
							fontSize={theme.fontSizes.desktop.bSmall}
							color={theme.colors.monochrome.ash}
							dateFormat={dateFormatKey ? dateFormatKey : undefined}
							dataType={dataTypeKey}
						>
							{suggestion}
						</Text>
					);
			}
		} else {
			let formattedValue = suggestion;
			if (dataType !== dataTypeValues.Singlelinetext) {
				formattedValue = formattedInputValue(suggestion) + '';
			}
			const beforeMatch = formattedValue.slice(0, index);
			const match = formattedValue.slice(index, index + query.length);
			const afterMatch = formattedValue.slice(index + query.length);
			switch (dataPriority) {
				//Each property has a slightly different style as per design, hence the property check
				case PROPERTY_NO.ONE:
					return (
						<Box
							color={theme.colors.primary.default}
							fontWeight={theme.fontWeights.bold}
							fontSize={theme.fontSizes.desktop.bSmall}
							width="100%"
							className={classNames(
								autoCompleteStyles.textNoWrapElipsis,
								autoCompleteStyles.textLineProperties
							)}
						>
							{getColorHighlightedText(beforeMatch, match, afterMatch, {
								color: theme.colors.primary.default,
								fontWeight: theme.fontWeights.bold,
								fontSize: theme.fontSizes.desktop.bSmall,
								dataTypeKey,
							})}
						</Box>
					);
				case PROPERTY_NO.TWO:
					return (
						<Box
							className={classNames(
								autoCompleteStyles.textNoWrapElipsis,
								autoCompleteStyles.textLineProperties
							)}
							fontWeight={theme.fontWeights.semiBold}
							fontSize={theme.fontSizes.desktop.bSmall}
							color={'#14142B'} //color not available in beacon styles
						>
							{getColorHighlightedText(beforeMatch, match, afterMatch, {
								fontWeight: theme.fontWeights.semiBold,
								fontSize: theme.fontSizes.desktop.bSmall,
								color: '#14142B', //color not available in beacon styles
								dataTypeKey,
							})}
						</Box>
					);
				case PROPERTY_NO.THREE:
					return (
						<Box
							className={classNames(
								autoCompleteStyles.textNoWrapElipsis,
								autoCompleteStyles.textLineProperties
							)}
							fontWeight={theme.fontWeights.regular}
							fontSize={theme.fontSizes.desktop.bSmall}
							color={theme.colors.monochrome.ash}
						>
							{getColorHighlightedText(beforeMatch, match, afterMatch, {
								fontWeight: theme.fontWeights.regular,
								fontSize: theme.fontSizes.desktop.bSmall,
								color: theme.colors.monochrome.ash,
								dataTypeKey,
							})}
						</Box>
					);
				case PROPERTY_NO.FOUR:
					return (
						<Box
							className={classNames(
								autoCompleteStyles.textNoWrapElipsis,
								autoCompleteStyles.textLineProperties
							)}
							fontWeight={theme.fontWeights.regular}
							fontSize={theme.fontSizes.desktop.bSmall}
							color={theme.colors.monochrome.ash}
						>
							{getColorHighlightedText(beforeMatch, match, afterMatch, {
								fontWeight: theme.fontWeights.regular,
								fontSize: theme.fontSizes.desktop.bSmall,
								color: theme.colors.monochrome.ash,
								dataTypeKey,
							})}
						</Box>
					);
			}
		}
	};
	const onFocus = () => {
		if (!inputValue) {
			setShowSuggestions(true);
			if (searchKey) {
				setInputValue(searchKey);
				if (enableRemoteSearch) {
					SetSource(null);
					debouncedOnChange(searchKey);
				} else {
					debouncedInternalSearchChange(searchKey);
				}
			}
		}
		if (enableSelectAll && inputValue === 'All') {
			handleClear();
			setShowSuggestions(true);
		}
	};
	const handleClear = () => {
		setInputValue('');
		setSearchKey('');
		setShowSuggestions(false);
		onInputChange?.('');
		setLastSelection(null);
		if (withForm) {
			onChangeTrigger?.('');
		}
		if (enableSelectAll) {
			setSelctAll(false);
		}
		onSearchKeyChange('', true);
		onChange?.('');
		setInternalData(data);
	};
	const renderData = acceptCustomValue
		? internalData
		: source && Array.isArray(source) && source?.length > QUERY_SIZE
		? source?.slice(0, -1)
		: source;

	const showLoader = source === null;
	const showNoData = source && Array.isArray(source) && source?.length == 0;
	const hasValidData = source && Array.isArray(source) && source?.length > 0;
	const showQuerySizeExceeded =
		source &&
		Array.isArray(source) &&
		source?.length > QUERY_SIZE &&
		!infiniteLoadingEnabled;
	const errorMessage =
		source?.error?.data?.message || (source?.errors && source?.message)
			? 'Failed to fetch data'
			: null;

	const isOpen =
		showSuggestions &&
		(showLoader || showNoData || errorMessage || hasValidData)
			? true
			: false;

	const { renderLayer, triggerProps, layerProps } = useLayer({
		isOpen: isOpen,
		placement: 'bottom-start',
		triggerOffset: 5,
		containerOffset: 0,
		overflowContainer: true, // we want the menu to stay within its scroll-container
		auto: false, // auto find a placement when required
		snap: false, // snap to the possible placements (not in between)
		onOutsideClick: handleClickOutside,
	});

	const renderSuggesions = isOpen && (
		<Box gap={'8px'}>
			<span
				{...{
					...layerProps,
					style: {
						...layerProps.style,
						width: inputBoxWidth,
						zIndex: 999,
					},
				}}
			>
				<Box
					className={classNames(
						scrollBarStyle.scrollbar,
						autoCompleteStyles.scrollbarContainer
					)}
					backgroundColor={props?.foregroundColor || theme.colors.monochrome.bg}
					id={mappingBoxId}
					ref={rootRef}
				>
					{enableSelectAll && (
						<Box
							backgroundColor={theme.colors.monochrome.white}
							className={autoCompleteStyles.borderBottomStyles}
							onClick={() => handleCheckboxSelection(!selectAll)}
							width={'100%'}
							cursor="pointer"
						>
							<Text text="All" type={TextTypes.Body} size={TextSizes.Large} />
						</Box>
					)}
					{hasValidData ? (
						Array.isArray(renderData) &&
						renderData.map((item, index) => {
							const { id, ...properties } = item;
							const keys = Object.keys(displayAttributeTypes);
							return (
								<Box
									key={id}
									justifyContent={'space-between'}
									display={'flex'}
									gap={'5px'}
									flexDirection={'column'}
									cursor="pointer"
									className={classNames({
										[autoCompleteStyles.borderBottomStyles]:
											(index < renderData?.length - 1 &&
												!hideSuggetionItemBorderBottom) ||
											showQuerySizeExceeded,
										[autoCompleteStyles.backgroundBorderStyle]:
											hideSuggetionItemBorderBottom && hoverItemBackgroundColor,
										[autoCompleteStyles.disabledStyle]: selectAll,
									})}
									hoverProps={{
										backgroundColor: hoverItemBackgroundColor,
										color: `${hoverItemColor} !important`,
									}}
									onClick={() => onSuggestionClicked(item)}
								>
									<Box display={'flex'} justifyContent={'space-between'}>
										<Tooltip
											type={TooltipType.Tooltip}
											triggerOffset={10}
											containerStyles={{ width: 'max-content' }}
											placement={TooltipPosition.BottomStart}
											content={highlightMatch(
												defaultPriorityValue || 1,
												properties[keys[0]]?.toString() || '',
												displayAttributeTypes[keys[0]],
												inputValue,
												true
											)}
											isOpen={isTextTruncated(
												`${properties[keys[0]]?.toString()}-${index}-1`
											)}
										>
											<Box
												width={keys[1] ? primaryLabelWidth : '100%'}
												alignItems="center"
												padding={1}
												id={`${properties[keys[0]]?.toString()}-${index}-1`}
											>
												{highlightMatch(
													defaultPriorityValue || 1,
													properties[keys[0]]?.toString() || '',
													displayAttributeTypes[keys[0]],
													inputValue,
													false
												)}
											</Box>
										</Tooltip>
										{keys[1] ? (
											<Tooltip
												type={TooltipType.Tooltip}
												triggerOffset={10}
												containerStyles={{ width: 'max-content' }}
												placement={TooltipPosition.BottomStart}
												content={highlightMatch(
													defaultPriorityValue || 2,
													properties[keys[1]]?.toString() || '',
													displayAttributeTypes[keys[1]],
													inputValue,
													true
												)}
												isOpen={isTextTruncated(
													`${properties[keys[1]]?.toString()}-${index}-2`
												)}
											>
												<Box
													width={`calc(100% - ${primaryLabelWidth})`}
													justifyContent="end"
													padding={1}
													id={`${properties[keys[1]]?.toString()}-${index}-2`}
												>
													{highlightMatch(
														defaultPriorityValue || 2,
														properties[keys[1]]?.toString() || '',
														displayAttributeTypes[keys[1]],
														inputValue,
														false
													)}
												</Box>
											</Tooltip>
										) : (
											<></>
										)}
									</Box>
									{keys?.length > 2 ? (
										<Box display={'flex'} justifyContent={'space-between'}>
											<Tooltip
												type={TooltipType.Tooltip}
												triggerOffset={10}
												containerStyles={{ width: 'max-content' }}
												placement={TooltipPosition.BottomStart}
												content={highlightMatch(
													defaultPriorityValue || 3,
													properties[keys[2]]?.toString() || '',
													displayAttributeTypes[keys[2]],
													inputValue,
													true
												)}
												isOpen={isTextTruncated(
													`${properties[keys[2]]?.toString()}-${index}-3`
												)}
											>
												<Box
													width={keys[3] ? primaryLabelWidth : '100%'}
													alignItems="center"
													padding={1}
													id={`${properties[keys[2]]?.toString()}-${index}-3`}
												>
													{highlightMatch(
														defaultPriorityValue || 3,
														properties[keys[2]]?.toString() || '',
														displayAttributeTypes[keys[2]],
														inputValue,
														false
													)}
												</Box>
											</Tooltip>
											{keys[3] ? (
												<Tooltip
													type={TooltipType.Tooltip}
													triggerOffset={10}
													containerStyles={{ width: 'max-content' }}
													placement={TooltipPosition.BottomStart}
													content={highlightMatch(
														defaultPriorityValue || 4,
														properties[keys[3]]?.toString() || '',
														displayAttributeTypes[keys[3]],
														inputValue,
														true
													)}
													isOpen={isTextTruncated(
														`${properties[keys[3]]?.toString()}-${index}-4`
													)}
												>
													<Box
														width={`calc(100% - ${primaryLabelWidth})`}
														justifyContent="end"
														padding={1}
														id={`${properties[keys[3]]?.toString()}-${index}-4`}
													>
														{highlightMatch(
															defaultPriorityValue || 4,
															properties[keys[3]]?.toString() || '',
															displayAttributeTypes[keys[3]],
															inputValue,
															false
														)}
													</Box>
												</Tooltip>
											) : (
												<></>
											)}
										</Box>
									) : (
										<></>
									)}
								</Box>
							);
						})
					) : showNoData ? (
						<Text alignSelf={'center'}>No results found</Text>
					) : showLoader ? (
						<Box height={50} justifyContent={'center'}>
							<CustomLoader />
						</Box>
					) : errorMessage ? (
						<Box height={50} justifyContent={'center'}>
							<Text alignSelf={'center'} color={theme.colors.danger.dark}>
								{errorMessage || 'Failed to fetch data'}
							</Text>
						</Box>
					) : (
						<></>
					)}
					{showQuerySizeExceeded ? (
						<Text
							alignSelf={'center'}
							color={theme.colors.monochrome.label}
							fontSize={theme.fontSizes.desktop.bXSmall}
						>
							{QUERY_SIZE_EXCEEDED_MESSAGE}
						</Text>
					) : (
						<></>
					)}
					{!infiniteLoadingEnabled ? null : (
						<Box
							className={classNames(autoCompleteStyles.infiniteListItemBox)}
							ref={sentryRef}
						>
							<Box
								className={classNames(autoCompleteStyles.infiniteListloader)}
							>
								<Box
									className={classNames(
										autoCompleteStyles.infiniteListloaderBox,
										autoCompleteStyles.infiniteListloaderBox1
									)}
									background={theme.colors.primary.default}
								></Box>
								<Box
									className={classNames(
										autoCompleteStyles.infiniteListloaderBox,
										autoCompleteStyles.infiniteListloaderBox2
									)}
									background={theme.colors.primary.default}
								></Box>
								<Box
									className={classNames(
										autoCompleteStyles.infiniteListloaderBox,
										autoCompleteStyles.infiniteListloaderBox3
									)}
									background={theme.colors.primary.default}
								></Box>
							</Box>
						</Box>
					)}
				</Box>
			</span>
			<Box></Box>
		</Box>
	);
	return (
		<Box
			data-component="atomic/autocomplete"
			className={autoCompleteStyles.autoCompleteInputBox}
			ref={wrapperRef}
			id={id as any}
		>
			{showLabel && (
				<Text
					size={TextSizes.Small}
					type={TextTypes.Body}
					className={classNames(autoCompleteStyles.label, {
						[autoCompleteStyles.required]: showAsterisk || showAsMandatory,
					})}
				>
					{label}
				</Text>
			)}
			<Box {...triggerProps}>
				<Input
					placeholder={placeholderValue || DefaultPlaceHolder}
					inputPlaceholderStyles={{
						color: inpColor || theme.colors.monochrome.placeholder,
						fontFamily: inpFontFamily || theme.fontFamily,
					}}
					inputSize={InputSize.SMALL}
					{...getAutoCompleteInputFieldStyle(props, theme)}
					name="autoComplete"
					disabled={disabled}
					readOnly={readonly}
					onChange={handleOnchange}
					onFocus={onFocus}
					{...(enableSearchIcon && !acceptCustomValue
						? {
								inputPrefix: () => (
									<Icon
										icon={IconCodes.icon_Bd_Search}
										color={theme.colors.monochrome.label}
										fontSize={22}
										margin="0px 0px 0px 5px"
									></Icon>
								),
						  }
						: {})}
					{...(!_isEmpty(inputValue) &&
						!acceptCustomValue && {
							inputSuffix: () => (
								<Icon
									icon={IconCodes.icon_Bd_Close_X}
									color={theme.colors.monochrome.label}
									fontSize={theme.fontSizes.desktop.bSmall}
									margin="0px 0px 0px 5px"
									onClick={handleClear}
								></Icon>
							),
						})}
					value={inputValue}
					error={props.error}
					{...inputOverrideProps}
				/>
			</Box>
			{renderLayer(renderSuggesions)}
		</Box>
	);
};

const Autocomplete: FC<AutocompleteProps> = ({
	withForm,
	name,
	...otherProps
}) => {
	const { control } = useForm();
	if (withForm) {
		return (
			<FormControl name={name} {...otherProps}>
				<CaptionMessage>
					<Controller
						name={name}
						control={control}
						defaultValue={otherProps.defaultSelected}
						render={({ field: { value, onChange }, fieldState: { error } }) => {
							return (
								<AutocompleteWrapper
									{...otherProps}
									name={name}
									onChange={(e) => onChange(e)}
									error={error}
									withForm={withForm}
									defaultSelected={value}
								/>
							);
						}}
					></Controller>
				</CaptionMessage>
			</FormControl>
		);
	} else {
		return (
			<AutocompleteWrapper name={name} withForm={withForm} {...otherProps} />
		);
	}
};
Autocomplete.defaultProps = defaultProps;
Autocomplete.displayName = 'Autocomplete';

// Framework generated code, below line wraps the component with a provider
// the 'configurable' wrapper should NOT BE DELETED
export default configurable(Autocomplete, 'Autocomplete');

{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2017", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@atomic/accordion": ["libs/atomic/accordion/src/index.ts"], "@atomic/address": ["libs/atomic/address/src/index.ts"], "@atomic/argumentmapper": ["libs/atomic/argumentmapper/src/index.ts"], "@atomic/autocomplete": ["libs/atomic/autocomplete/src/index.ts"], "@atomic/avatar": ["libs/atomic/avatar/src/index.ts"], "@atomic/badge": ["libs/atomic/badge/src/index.ts"], "@atomic/box": ["libs/atomic/box/src/index.ts"], "@atomic/button": ["libs/atomic/button/src/index.ts"], "@atomic/buttonslist": ["libs/atomic/buttonslist/src/index.ts"], "@atomic/calender": ["libs/atomic/calender/src/index.ts"], "@atomic/capabilityblock": ["libs/atomic/capabilityblock/src/index.ts"], "@atomic/caption": ["libs/atomic/caption/src/index.ts"], "@atomic/card": ["libs/atomic/card/src/index.ts"], "@atomic/cardview": ["libs/atomic/cardview/src/index.ts"], "@atomic/carousel": ["libs/atomic/carousel/src/index.ts"], "@atomic/checkbox": ["libs/atomic/checkbox/src/index.ts"], "@atomic/checkboxgroup": ["libs/atomic/checkboxgroup/src/index.ts"], "@atomic/col": ["libs/atomic/col/src/index.ts"], "@atomic/collapsednavigationbar": ["libs/atomic/collapsednavigationbar/src/index.ts"], "@atomic/content": ["libs/atomic/content/src/index.ts"], "@atomic/creditcard": ["libs/atomic/creditcard/src/index.ts"], "@atomic/currency": ["libs/atomic/currency/src/index.ts"], "@atomic/custom-loader": ["libs/atomic/custom-loader/src/index.ts"], "@atomic/customscrollbar": ["libs/atomic/customscrollbar/src/index.ts"], "@atomic/date": ["libs/atomic/date/src/index.ts"], "@atomic/docpdfviewer": ["libs/atomic/docpdfviewer/src/index.ts"], "@atomic/dropdown": ["libs/atomic/dropdown/src/index.ts"], "@atomic/durationpicker": ["libs/atomic/durationpicker/src/index.ts"], "@atomic/email": ["libs/atomic/email/src/index.ts"], "@atomic/emojipicker": ["libs/atomic/emojipicker/src/index.ts"], "@atomic/flexlayout": ["libs/atomic/flexlayout/src/index.ts"], "@atomic/formtab": ["libs/atomic/formtab/src/index.ts"], "@atomic/functional-block": ["libs/atomic/functional-block/src/index.ts"], "@atomic/functionalblock": ["libs/atomic/functionalblock/src/index.ts"], "@atomic/grid-col": ["libs/atomic/grid-col/src/index.ts"], "@atomic/grid-row": ["libs/atomic/grid-row/src/index.ts"], "@atomic/groupedlistitem": ["libs/atomic/groupedlistitem/src/index.ts"], "@atomic/heading": ["libs/atomic/heading/src/index.ts"], "@atomic/icon": ["libs/atomic/icon/src/index.ts"], "@atomic/iframe": ["libs/atomic/iframe/src/index.ts"], "@atomic/image": ["libs/atomic/image/src/index.ts"], "@atomic/image-slider": ["libs/atomic/image-slider/src/index.ts"], "@atomic/info": ["libs/atomic/info/src/index.ts"], "@atomic/inline-autocomplete": ["libs/atomic/inline-autocomplete/src/index.ts"], "@atomic/inline-text-editor": ["libs/atomic/inline-text-editor/src/index.ts"], "@atomic/input": ["libs/atomic/input/src/index.ts"], "@atomic/inputstepper": ["libs/atomic/inputstepper/src/index.ts"], "@atomic/item": ["libs/atomic/item/src/index.ts"], "@atomic/layout": ["libs/atomic/layout/src/index.ts"], "@atomic/layoutbody": ["libs/atomic/layoutbody/src/index.ts"], "@atomic/linebreak": ["libs/atomic/linebreak/src/index.ts"], "@atomic/link": ["libs/atomic/link/src/index.ts"], "@atomic/listview": ["libs/atomic/listview/src/index.ts"], "@atomic/login": ["libs/atomic/login/src/index.ts"], "@atomic/menu": ["libs/atomic/menu/src/index.ts"], "@atomic/micro-toolbar": ["libs/atomic/micro-toolbar/src/index.ts"], "@atomic/navigation-menu": ["libs/atomic/navigation-menu/src/index.ts"], "@atomic/nesteditem": ["libs/atomic/nesteditem/src/index.ts"], "@atomic/newline": ["libs/atomic/newline/src/index.ts"], "@atomic/numeric": ["libs/atomic/numeric/src/index.ts"], "@atomic/overflow": ["libs/atomic/overflow/src/index.ts"], "@atomic/page": ["libs/atomic/page/src/index.ts"], "@atomic/pagedemo": ["libs/atomic/pagedemo/src/index.ts"], "@atomic/pagetitleandnavbar": ["libs/atomic/pagetitleandnavbar/src/index.ts"], "@atomic/panel": ["libs/atomic/panel/src/index.ts"], "@atomic/password": ["libs/atomic/password/src/index.ts"], "@atomic/pdfgenerator": ["libs/atomic/pdfgenerator/src/index.ts"], "@atomic/pdfviewer": ["libs/atomic/pdfviewer/src/index.ts"], "@atomic/percentage": ["libs/atomic/percentage/src/index.ts"], "@atomic/phonenumber": ["libs/atomic/phonenumber/src/index.ts"], "@atomic/pin": ["libs/atomic/pin/src/index.ts"], "@atomic/popovermenu": ["libs/atomic/popovermenu/src/index.ts"], "@atomic/preloader": ["libs/atomic/preloader/src/index.ts"], "@atomic/profilepicture": ["libs/atomic/profilepicture/src/index.ts"], "@atomic/progressbar": ["libs/atomic/progressbar/src/index.ts"], "@atomic/quickpanelbox": ["libs/atomic/quickpanelbox/src/index.ts"], "@atomic/radiobutton": ["libs/atomic/radiobutton/src/index.ts"], "@atomic/radiogroup": ["libs/atomic/radiogroup/src/index.ts"], "@atomic/react-chrono": ["libs/atomic/react-chrono/src/index.ts"], "@atomic/repeater": ["libs/atomic/repeater/src/index.ts"], "@atomic/repeateritem": ["libs/atomic/repeateritem/src/index.ts"], "@atomic/reveal-box": ["libs/atomic/reveal-box/src/index.ts"], "@atomic/scrollbar": ["libs/atomic/scrollbar/src/index.ts"], "@atomic/search": ["libs/atomic/search/src/index.ts"], "@atomic/searchbar": ["libs/atomic/searchbar/src/index.ts"], "@atomic/section": ["libs/atomic/section/src/index.ts"], "@atomic/sectioncard": ["libs/atomic/sectioncard/src/index.ts"], "@atomic/sectionhighlighter": ["libs/atomic/sectionhighlighter/src/index.ts"], "@atomic/segmenttabs": ["libs/atomic/segmenttabs/src/index.ts"], "@atomic/separator": ["libs/atomic/separator/src/index.ts"], "@atomic/signature": ["libs/atomic/signature/src/index.ts"], "@atomic/simple-list": ["libs/atomic/simple-list/src/index.ts"], "@atomic/snap": ["libs/atomic/snap/src/index.ts"], "@atomic/splitter": ["libs/atomic/splitter/src/index.ts"], "@atomic/ssn": ["libs/atomic/ssn/src/index.ts"], "@atomic/starrating": ["libs/atomic/starrating/src/index.ts"], "@atomic/tablecellhighlighter": ["libs/atomic/tablecellhighlighter/src/index.ts"], "@atomic/tabnavigation": ["libs/atomic/tabnavigation/src/index.ts"], "@atomic/tabnavigationifg": ["libs/atomic/tabnavigationifg/src/index.ts"], "@atomic/tags": ["libs/atomic/tags/src/index.ts"], "@atomic/text": ["libs/atomic/text/src/index.ts"], "@atomic/textarea": ["libs/atomic/textarea/src/index.ts"], "@atomic/time": ["libs/atomic/time/src/index.ts"], "@atomic/timeline": ["libs/atomic/timeline/src/index.ts"], "@atomic/toggle": ["libs/atomic/toggle/src/index.ts"], "@atomic/tooltip": ["libs/atomic/tooltip/src/index.ts"], "@atomic/tree": ["libs/atomic/tree/src/index.ts"], "@atomic/treelistitem": ["libs/atomic/treelistitem/src/index.ts"], "@atomic/verticalslider": ["libs/atomic/verticalslider/src/index.ts"], "@atomic/videoplayer": ["libs/atomic/videoplayer/src/index.ts"], "@atomic/wizard": ["libs/atomic/wizard/src/index.ts"], "@atomic/zipcode": ["libs/atomic/zipcode/src/index.ts"], "@atomic/zoom": ["libs/atomic/zoom/src/index.ts"], "@base/api-handler": ["libs/base/api-handler/src/index.ts"], "@base/common-string": ["libs/base/common-string/src/index.ts"], "@base/component-decorator": ["libs/base/component-decorator/src/index.ts"], "@base/config-provider": ["libs/base/config-provider/src/index.ts"], "@base/global-events": ["libs/base/global-events/src/index.ts"], "@base/grid-ui": ["libs/base/grid-ui/src/index.ts"], "@base/icon": ["libs/base/icon/src/index.ts"], "@base/layout": ["libs/base/layout/src/index.ts"], "@base/theme": ["libs/base/theme/src/index.ts"], "@base/toast": ["libs/base/toast/src/index.ts"], "@base/toolbar": ["libs/base/toolbar/src/index.ts"], "@base/utilities": ["libs/base/utilities/src/index.ts"], "@base/utils-hooks": ["libs/base/utils-hooks/src/index.ts"], "@chart/barchart": ["libs/chart/barchart/src/index.ts"], "@chart/columnchart": ["libs/chart/columnchart/src/index.ts"], "@chart/common": ["libs/chart/common/src/index.ts"], "@chart/gaugechart": ["libs/chart/gaugechart/src/index.ts"], "@chart/linechart": ["libs/chart/linechart/src/index.ts"], "@chart/piechart": ["libs/chart/piechart/src/index.ts"], "@chart/progresschart": ["libs/chart/progresschart/src/index.ts"], "@chart/radarchart": ["libs/chart/radarchart/src/index.ts"], "@chart/slicedchart": ["libs/chart/slicedchart/src/index.ts"], "@chart/treemap": ["libs/chart/treemap/src/index.ts"], "@composite/addresspicker": ["libs/composite/addresspicker/src/index.ts"], "@composite/agentviewleftpanel": ["libs/composite/agentviewleftpanel/src/index.ts"], "@composite/agentviewtemplate": ["libs/composite/agentviewtemplate/src/index.ts"], "@composite/appheader": ["libs/composite/appheader/src/index.ts"], "@composite/areaselector": ["libs/composite/areaselector/src/index.ts"], "@composite/avatargroup": ["libs/composite/avatargroup/src/index.ts"], "@composite/bell-notification": ["libs/composite/bell-notification/src/index.ts"], "@composite/box": ["libs/composite/box/src/index.ts"], "@composite/buttonbar": ["libs/composite/buttonbar/src/index.ts"], "@composite/card": ["libs/composite/card/src/index.ts"], "@composite/chatassistant": ["libs/composite/chatassistant/src/index.ts"], "@composite/choice-cards": ["libs/composite/choice-cards/src/index.ts"], "@composite/colorpicker": ["libs/composite/colorpicker/src/index.ts"], "@composite/contentcomposition": ["libs/composite/contentcomposition/src/index.ts"], "@composite/contextmenu": ["libs/composite/contextmenu/src/index.ts"], "@composite/contextualpanel": ["libs/composite/contextualpanel/src/index.ts"], "@composite/controlcard": ["libs/composite/controlcard/src/index.ts"], "@composite/conversationitem": ["libs/composite/conversationitem/src/index.ts"], "@composite/datacard": ["libs/composite/datacard/src/index.ts"], "@composite/datagrid": ["libs/composite/datagrid/src/index.ts"], "@composite/datalabel": ["libs/composite/datalabel/src/index.ts"], "@composite/datasourcebo": ["libs/composite/datasourcebo/src/index.ts"], "@composite/datatable": ["libs/composite/datatable/src/index.ts"], "@composite/datepicker": ["libs/composite/datepicker/src/index.ts"], "@composite/daterangepicker": ["libs/composite/daterangepicker/src/index.ts"], "@composite/demopage": ["libs/composite/demopage/src/index.ts"], "@composite/doceditorviewer": ["libs/composite/doceditorviewer/src/index.ts"], "@composite/documentclassifier": ["libs/composite/documentclassifier/src/index.ts"], "@composite/documentlistviewer": ["libs/composite/documentlistviewer/src/index.ts"], "@composite/downloadcard": ["libs/composite/downloadcard/src/index.ts"], "@composite/driveuploader": ["libs/composite/driveuploader/src/index.ts"], "@composite/emptystate": ["libs/composite/emptystate/src/index.ts"], "@composite/fileuploader": ["libs/composite/fileuploader/src/index.ts"], "@composite/form": ["libs/composite/form/src/index.ts"], "@composite/formbuilder": ["libs/composite/formbuilder/src/index.ts"], "@composite/functiondetailswithform": ["libs/composite/functiondetailswithform/src/index.ts"], "@composite/funding-source": ["libs/composite/funding-source/src/index.ts"], "@composite/hotkeys": ["libs/composite/hotkeys/src/index.ts"], "@composite/icondetailswithform": ["libs/composite/icondetailswithform/src/index.ts"], "@composite/iconfinder": ["libs/composite/iconfinder/src/index.ts"], "@composite/iconpicker": ["libs/composite/iconpicker/src/index.ts"], "@composite/infinitescroll": ["libs/composite/infinitescroll/src/index.ts"], "@composite/infoicon": ["libs/composite/infoicon/src/index.ts"], "@composite/informationcard": ["libs/composite/informationcard/src/index.ts"], "@composite/kanban": ["libs/composite/kanban/src/index.ts"], "@composite/listing": ["libs/composite/listing/src/index.ts"], "@composite/messaging": ["libs/composite/messaging/src/index.ts"], "@composite/modal": ["libs/composite/modal/src/index.ts"], "@composite/modallibrary": ["libs/composite/modallibrary/src/index.ts"], "@composite/modalwindows": ["libs/composite/modalwindows/src/index.ts"], "@composite/multiinput": ["libs/composite/multiinput/src/index.ts"], "@composite/navigationbar": ["libs/composite/navigationbar/src/index.ts"], "@composite/orghiearchy": ["libs/composite/orghiearchy/src/index.ts"], "@composite/pageviewer": ["libs/composite/pageviewer/src/index.ts"], "@composite/previewcard": ["libs/composite/previewcard/src/index.ts"], "@composite/progress-tracker": ["libs/composite/progress-tracker/src/index.ts"], "@composite/progressbar": ["libs/composite/progressbar/src/index.ts"], "@composite/reconmatchsummary": ["libs/composite/reconmatchsummary/src/index.ts"], "@composite/rulecard": ["libs/composite/rulecard/src/index.ts"], "@composite/ruleeditor": ["libs/composite/ruleeditor/src/index.ts"], "@composite/statusbar": ["libs/composite/statusbar/src/index.ts"], "@composite/table": ["libs/composite/table/src/index.ts"], "@composite/text": ["libs/composite/text/src/index.ts"], "@composite/timeline": ["libs/composite/timeline/src/index.ts"], "@composite/timepicker": ["libs/composite/timepicker/src/index.ts"], "@composite/toast": ["libs/composite/toast/src/index.ts"], "@composite/toolbar": ["libs/composite/toolbar/src/index.ts"], "@composite/treelisting": ["libs/composite/treelisting/src/index.ts"], "@composite/uploadcard": ["libs/composite/uploadcard/src/index.ts"], "@composite/validationcard": ["libs/composite/validationcard/src/index.ts"], "@ifg/accountheader": ["libs/ifg/accountheader/src/index.ts"], "@ifg/accountopeningform": ["libs/ifg/accountopeningform/src/index.ts"], "@ifg/accountopeningformsummary": ["libs/ifg/accountopeningformsummary/src/index.ts"], "@ifg/approve-modal": ["libs/ifg/approve-modal/src/index.ts"], "@ifg/assign-modal": ["libs/ifg/assign-modal/src/index.ts"], "@ifg/audittrail": ["libs/ifg/audittrail/src/index.ts"], "@ifg/basicinformationform": ["libs/ifg/basicinformationform/src/index.ts"], "@ifg/client-list": ["libs/ifg/client-list/src/index.ts"], "@ifg/document-viewer": ["libs/ifg/document-viewer/src/index.ts"], "@ifg/documentheader": ["libs/ifg/documentheader/src/index.ts"], "@ifg/documentlist": ["libs/ifg/documentlist/src/index.ts"], "@ifg/documentupload": ["libs/ifg/documentupload/src/index.ts"], "@ifg/footer": ["libs/ifg/footer/src/index.ts"], "@ifg/formdata": ["libs/ifg/formdata/src/index.ts"], "@ifg/forward-modal": ["libs/ifg/forward-modal/src/index.ts"], "@ifg/header": ["libs/ifg/header/src/index.ts"], "@ifg/reject-modal": ["libs/ifg/reject-modal/src/index.ts"], "@ifg/statusloader": ["libs/ifg/statusloader/src/index.ts"], "@ifg/success-modal": ["libs/ifg/success-modal/src/index.ts"], "@ifg/summary": ["libs/ifg/summary/src/index.ts"], "@ifg/summaryheader": ["libs/ifg/summaryheader/src/index.ts"], "@ifg/supervisor-review-modal": ["libs/ifg/supervisor-review-modal/src/index.ts"], "@ifg/validationpopup": ["libs/ifg/validationpopup/src/index.ts"], "@ui-component-library/composite/agentviewtemplate-v2": ["libs/composite/agentviewtemplate-v2/src/index.ts"], "@workflow/automation": ["libs/workflow/automation/src/index.ts"], "@workflow/call": ["libs/workflow/call/src/index.ts"], "@workflow/conditional": ["libs/workflow/conditional/src/index.ts"], "@workflow/configure": ["libs/workflow/configure/src/index.ts"], "@workflow/connector": ["libs/workflow/connector/src/index.ts"], "@workflow/customnode": ["libs/workflow/customnode/src/index.ts"], "@workflow/end": ["libs/workflow/end/src/index.ts"], "@workflow/exit": ["libs/workflow/exit/src/index.ts"], "@workflow/extract": ["libs/workflow/extract/src/index.ts"], "@workflow/finally": ["libs/workflow/finally/src/index.ts"], "@workflow/forstart": ["libs/workflow/forstart/src/index.ts"], "@workflow/generate": ["libs/workflow/generate/src/index.ts"], "@workflow/iterator": ["libs/workflow/iterator/src/index.ts"], "@workflow/log": ["libs/workflow/log/src/index.ts"], "@workflow/logiceditor": ["libs/workflow/logiceditor/src/index.ts"], "@workflow/nodewrapper": ["libs/workflow/nodewrapper/src/index.ts"], "@workflow/notification": ["libs/workflow/notification/src/index.ts"], "@workflow/parallel": ["libs/workflow/parallel/src/index.ts"], "@workflow/raise": ["libs/workflow/raise/src/index.ts"], "@workflow/skip": ["libs/workflow/skip/src/index.ts"], "@workflow/sleep": ["libs/workflow/sleep/src/index.ts"], "@workflow/start": ["libs/workflow/start/src/index.ts"], "@workflow/task": ["libs/workflow/task/src/index.ts"], "@workflow/transform": ["libs/workflow/transform/src/index.ts"], "@workflow/transformer": ["libs/workflow/transformer/src/index.ts"], "@workflow/try": ["libs/workflow/try/src/index.ts"], "@workflow/validate": ["libs/workflow/validate/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}